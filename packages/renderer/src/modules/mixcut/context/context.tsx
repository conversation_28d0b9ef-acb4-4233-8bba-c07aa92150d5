import React from 'react'
import { Combo } from '@app/shared/types/ipc/mixcut'
import { EditorState } from '@/libs/cache/parts/editor.cache'
import { Mixcut } from '@/types/mixcut'

import { RenderableOverlay } from '@app/shared/types/overlay'
import { MultiSelection } from './useMultiSelection'
import { MixcutRulesForm } from '@/modules/mixcut/context/useMixcutRulesForm'

export type MixcutPageTabs = 'generation' | 'saved'

// 重复率筛选规则
export type DuplicateRateFilter = {
  // 重复率最小值 (0-100)
  minRate: number
  // 重复率最大值 (0-100)
  maxRate: number
}

export type MixcutContextValues = {
  state: EditorState
  activeTab: MixcutPageTabs
  setActiveTab(v: MixcutPageTabs): void

  generation: MultiSelection<PreviewableMixcut> & {
    drawerOpen: boolean
    setDrawerOpen: React.Dispatch<React.SetStateAction<boolean>>

    generateCount: number
    setGenerateCount: React.Dispatch<React.SetStateAction<number>>

    rulesForm: MixcutRulesForm

    generatedMixcuts: PreviewableMixcut[]

    batchUploadState: {
      visible: boolean
      completed: number
      total: number
    }

    /**
     * 批量上传选中的混剪结果
     */
    uploadSelectedPreviews(): Promise<void>

    /**
     * 生成所有可能的混剪组合
     */
    generateCombinations(): void

    // 重复率筛选状态
    duplicateRateFilter: DuplicateRateFilter
    setDuplicateRateFilter(filter: DuplicateRateFilter): void
  }

  saved: MultiSelection<Mixcut.SavedMixcut> & {
  }

  playerOverlays: RenderableOverlay[]
}

export type GeneratedMixcut = {
  cover?: string
  videoCombo: Combo
  narrationSelections: number[]
}

export type PreviewableMixcut = GeneratedMixcut & {
  overlays: RenderableOverlay[]
}

export const MixcutContext = React.createContext<MixcutContextValues>(null as any)

export const useMixcutContext = () => {
  const context = React.useContext(MixcutContext)
  if (!context) {
    throw new Error('useMixcutContext must be used within a MixcutProvider')
  }
  return context
}
