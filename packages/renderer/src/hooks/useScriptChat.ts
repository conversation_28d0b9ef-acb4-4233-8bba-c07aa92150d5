import { useCallback, useMemo, useState } from 'react'
import { useInfiniteQueryScriptChatMessageList } from './queries/useQueryScriptChat'
import { ChatMessage, ScriptChatAPI } from '@/libs/request/api/script-chat'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { chunk } from 'lodash'
import { getInfiniteQueryKey } from './useInfiniteQuery'

export const useScriptChat = (sessionId?: number) => {
  const queryClient = useQueryClient()
  const { data, refetch } = useInfiniteQueryScriptChatMessageList(sessionId)
  const messages = useMemo(() => data?.pages.flatMap(page => page.list) || [], [data])
  const [messageMap, setMessageMap] = useState<Record<number, ChatMessage>>({})

  const sendMessage = useCallback(
    async ({ content, modelId, sessionId }: { content: string; modelId: number; sessionId: number }) => {
      const queryKey = getInfiniteQueryKey([QUERY_KEYS.SCRIPT_CHAT_MESSAGE_PAGE], { sessionId })
      queryClient.setQueryData<typeof data>(queryKey, oldData => {
        oldData ??= { pages: [], pageParams: [] }
        const newMessage: ChatMessage = {
          id: Date.now(),
          modelId,
          sessionId,
          parentId: null,
          content,
          role: 'user',
          runTime: null,
          completionTokens: null,
          promptTokens: null,
          totalTokens: null,
          createTime: Date.now(),
          pending: true,
          isForm: false,
        }
        const messages = [newMessage, ...oldData.pages.flatMap(page => page.list)]

        // HACK: 这里使用默认数量，因为 useInfiniteQuery 没有暴露 pageSize
        const pageSize = 20
        return { ...oldData, pages: chunk(messages, pageSize).map(list => ({ list, total: messages.length })) }
      })

      await ScriptChatAPI.send(
        {
          sessionId,
          modelId,
          content,
        },
        {
          onMessage: ({ data }) => {
            setMessageMap(prev => {
              const message =
                prev[sessionId] ||
                ({
                  ...data,
                  content: '',
                  modelId,
                  sessionId,
                  parentId: null,
                  role: 'assistant',
                  isForm: false,
                } satisfies ChatMessage)
              return {
                ...prev,
                [sessionId]: { ...message, ...data, content: message.content + data.content },
              }
            })
          },
        },
      )
      await refetch()
      setMessageMap(prev => Object.fromEntries(Object.entries(prev).filter(([key]) => Number(key) !== sessionId)))
    },
    [sessionId, refetch],
  )

  const genScript = useCallback(
    async ({ sessionId, promptCode, params }: Parameters<typeof ScriptChatAPI.genForm>[0]) => {
      const queryKey = getInfiniteQueryKey([QUERY_KEYS.SCRIPT_CHAT_MESSAGE_PAGE], { sessionId })
      queryClient.setQueryData<typeof data>(queryKey, oldData => {
        oldData ??= { pages: [], pageParams: [] }
        const newMessage: ChatMessage = {
          id: Date.now(),
          modelId: 0,
          sessionId,
          parentId: null,
          content: '正在生成脚本...',
          role: 'user',
          runTime: null,
          completionTokens: null,
          promptTokens: null,
          totalTokens: null,
          createTime: Date.now(),
          pending: true,
          isForm: true,
        }
        const messages = [newMessage, ...oldData.pages.flatMap(page => page.list)]

        // HACK: 这里使用默认数量，因为 useInfiniteQuery 没有暴露 pageSize
        const pageSize = 20
        return { ...oldData, pages: chunk(messages, pageSize).map(list => ({ list, total: messages.length })) }
      })

      await ScriptChatAPI.genForm(
        {
          sessionId,
          promptCode,
          params,
        },
        {
          onMessage: ({ data }) => {
            setMessageMap(prev => {
              const message =
                prev[sessionId] ||
                ({
                  ...data,
                  content: '',
                  modelId: 0,
                  sessionId,
                  parentId: null,
                  role: 'assistant',
                  isForm: true,
                } satisfies ChatMessage)
              return {
                ...prev,
                [sessionId]: { ...message, ...data, content: message.content + data.content },
              }
            })
          },
        },
      )
      await refetch()
      setMessageMap(prev => Object.fromEntries(Object.entries(prev).filter(([key]) => Number(key) !== sessionId)))
    },
    [sessionId, refetch],
  )

  const message = useMemo(() => (sessionId ? messageMap[sessionId] : null), [messageMap, sessionId])
  return {
    messages: message ? [message, ...messages] : messages,
    sendMessage,
    genScript,
    pending: !!message,
  }
}
