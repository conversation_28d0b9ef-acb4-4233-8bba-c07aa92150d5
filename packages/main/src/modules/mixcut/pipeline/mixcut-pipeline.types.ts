import { MixcutableOverlay, PlayerMetadata } from '@app/shared/types/ipc/mixcut.js'
import { Overlay, OverlayType } from '@app/shared/types/overlay.js'
import { MixcutPipelines, ParamsForPipeline } from '@app/shared/types/mixcut.js'

export type MixcutPipelineContext<TPipeline extends MixcutPipelines> = Readonly<{
  pipeline: TPipeline
  playerMetadata: PlayerMetadata,
  overlays: MixcutableOverlay[],
  params: ParamsForPipeline<TPipeline>
}>

export abstract class MixcutPipeline<TPipeline extends MixcutPipelines> {

  constructor(
    public readonly context: MixcutPipelineContext<TPipeline>,
  ) {
  }

  abstract process(): Promise<MixcutableOverlay[]>
}

export abstract class SimpleMixcutPipeline<
  TPipeline extends MixcutPipelines,
  TLimitedOverlay extends Overlay = Overlay
> extends MixcutPipeline<TPipeline> {

  abstract processSingleOverlay(overlay: TLimitedOverlay & MixcutableOverlay): Promise<TLimitedOverlay & MixcutableOverlay>

  abstract filter(overlay: MixcutableOverlay): overlay is TLimitedOverlay & MixcutableOverlay

  static readonly filters = {
    videoOverlay: (o: MixcutableOverlay) => o.type === OverlayType.VIDEO,

    narrationText: (o: MixcutableOverlay) => o.type === OverlayType.TEXT && !!o.isNarrationText,

    globalText: (o: MixcutableOverlay) => o.type === OverlayType.TEXT && o.isInGlobalTrack,
  }

  public async process(): Promise<MixcutableOverlay[]> {
    const { relevant, irrelevant } = this.context.overlays.reduce(
      (result, item) => {
        if (!this.filter || this.filter(item)) {
          result.relevant.push(item)
        } else {
          result.irrelevant.push(item)
        }

        return result
      },
      { relevant: [] as MixcutableOverlay[], irrelevant: [] as MixcutableOverlay[] }
    )

    return [
      ...irrelevant,
      ...await Promise.all(relevant.map(overlay => this.processSingleOverlay(overlay)))
    ]
  }
}
