import React, { PropsWith<PERSON>hildren, useCallback, useState } from 'react'
import {
  DndContext,
  DragEndEvent,
  DragMoveEvent,
  DragStartEvent,
  PointerSensor,
  pointerWithin,
  useDraggable,
  useDroppable,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import { Overlay } from '@app/shared/types/overlay'
import { MaterialResource, PasterResource, ResourceSource, SoundResource } from '@/types/resources'
import { IndexableTrack } from '@/modules/video-editor/types'
import folderIcon from '@/assets/folder.svg'
import { useItemActions } from '@/hooks/useItemActions'
import { AuthedImg } from '@/components/authed-img'
import { omit } from 'lodash'
import { buildTextOverlay } from '@/modules/video-editor/utils/text'
import { DroppableResource, useTimelineDnd } from '@/modules/video-editor/contexts'
import { useTimelineItemMoving } from '../hooks/draggable-handlers/useTimelineItemMoving'
import { useResourceDrag } from '../hooks/draggable-handlers/useResourceDrag'
import { TextLayerRenderer } from '@clipnest/overlay-renderer'
import { ResourcePlugins } from '@/modules/video-editor/resource-plugin-system'

export enum EditorDraggableTypes {
  TimelineItem = 'TimelineItem',
  Resource = 'Resource',
}

export enum EditorDroppableTypes {
  TimelineTrack = 'TimelineTrack',
  Folder = 'Folder',
}

type PayloadTypeByDraggableType = {
  [EditorDraggableTypes.TimelineItem]: {
    type: EditorDraggableTypes.TimelineItem
    overlay: Overlay
  }
  [EditorDraggableTypes.Resource]: {
    type: EditorDraggableTypes.Resource
  } & DroppableResource
}

type PayloadTypeByDroppableType = {
  [EditorDroppableTypes.TimelineTrack]: {
    type: EditorDroppableTypes.TimelineTrack
    track: IndexableTrack
  }
  [EditorDroppableTypes.Folder]: {
    type: EditorDroppableTypes.Folder
    resource: MaterialResource.Media & { materialType: ResourceSource }
  }
}

type ValueOf<T> = T[keyof T]
type DraggablePayload = ValueOf<PayloadTypeByDraggableType>
type DroppablePayload = ValueOf<PayloadTypeByDroppableType>

type TransformedDragEvent<Event extends DragStartEvent | DragEndEvent | DragMoveEvent> = {
  event: Event & { activatorEvent: PointerEvent }
  draggable: DraggablePayload
  droppable?: DroppablePayload
}

function transformDragEvent<Event extends DragStartEvent | DragEndEvent | DragMoveEvent>(
  event: Event,
): TransformedDragEvent<Event> {
  const draggable = event.active.data.current as DraggablePayload

  const droppable = 'over' in event ? (event.over?.data.current as DroppablePayload | undefined) : undefined

  return { event: event as any, draggable, droppable }
}

type DraggingResourceData = {
  x: number
  y: number
  dx: number
  dy: number
  resource: DroppableResource
}

const DraggingResource: React.FC<DraggingResourceData> = ({ x, y, dy, dx, resource }) => {
  const contentForMaterial = (data: MaterialResource.Media) => {
    return (
      <>
        {data.resType === MaterialResource.MediaType.FOLDER
          ? <img src={folderIcon} alt="文件夹" className="w-10 h-10" />
          : data.resType === MaterialResource.MediaType.AUDIO
            ? <AuthedImg src={data.cover} alt={data.fileName} className="w-10 h-10" />
            : data.cover
              ? <AuthedImg src={data.cover} alt={data.fileName} className="w-10 h-10" />
              : null}
      </>
    )
  }

  const renderContent = useCallback(() => {
    if (resource.sourcePlugin === ResourcePlugins.MATERIAL_LIB) {
      return contentForMaterial(resource.data)
    }

    if (resource.sourcePlugin === ResourcePlugins.STICKER) {
      return (
        <AuthedImg
          src={resource.data.content.thumbUrl}
          alt={'materialType' in resource.data && resource.data.materialType === ResourceSource.LOCAL_STICK
            ? resource.data.fileId
            : resource.data.id.toString()}
          className="w-10 h-10"
        />
      )
    }

    if (resource.sourcePlugin === ResourcePlugins.SOUND_EFFECT) {
      return <AuthedImg src={resource.data.cover?.url} alt={resource.data.title} className="w-10 h-10" />
    }

    if (resource.sourcePlugin === ResourcePlugins.TEXT) {
      return (
        <TextLayerRenderer
          overlay={buildTextOverlay(resource.data, { isPreview: true, textContent: '花字' })}
          containerStyle={{
            width: 96,
            height: 48
          }}
        />
      )
    }

    if (resource.sourcePlugin === ResourcePlugins.TRANSITION) {
      return (
        <div
          style={{
            width: 96,
            height: 96,
            background: 'linear-gradient(45deg, #6b7280, #9ca3af)',
            borderRadius: 8,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: 12,
            fontWeight: 'bold',
            textAlign: 'center'
          }}
        >
          {resource.data?.name || '转场'}
        </div>
      )
    }

    return null
  }, [resource])

  return (
    <div
      style={{
        position: 'fixed',
        left: x + dx,
        top: y + dy,
        zIndex: 9999, // 确保拖拽元素在最上层
        pointerEvents: 'none', // 避免拖拽元素阻挡鼠标事件
      }}
    >
      {renderContent()}
    </div>
  )
}

export function useTypedDraggable<T extends EditorDraggableTypes>(
  type: T,
  id: string | number,
  payload: Omit<PayloadTypeByDraggableType[T], 'type'>,
  options?: { disabled?: boolean }
) {
  return useDraggable({
    id: `${type}-${id}`,
    data: {
      ...payload,
      type,
    },
    disabled: options?.disabled,
  })
}

export function useTypedDroppable<T extends EditorDroppableTypes>(
  type: T,
  id: string | number,
  payload: Omit<PayloadTypeByDroppableType[T], 'type'>,
) {
  return useDroppable({
    id: `${type}-${id}`,
    data: {
      ...payload,
      type,
    },
  })
}

function isSharedPaster(
  resource: PasterResource.Paster | (PasterResource.SharedPaster & { materialType: ResourceSource })
): resource is PasterResource.SharedPaster & { materialType: ResourceSource } {
  return 'materialType' in resource
}

function isSharedSound(
  resource: SoundResource.Sound | (SoundResource.SharedSound & { materialType: ResourceSource })
): resource is SoundResource.SharedSound & { materialType: ResourceSource } {
  return 'materialType' in resource
}

function canMoveResourceToFolder(
  draggable: DroppableResource
): draggable is Extract<DroppableResource,
    | { sourcePlugin: ResourcePlugins.MATERIAL_LIB }
    | { sourcePlugin: ResourcePlugins.STICKER, data: PasterResource.SharedPaster & { materialType: ResourceSource } }
    | { sourcePlugin: ResourcePlugins.SOUND_EFFECT, data: SoundResource.SharedSound & { materialType: ResourceSource } }
> {
  return (
    draggable.sourcePlugin === ResourcePlugins.MATERIAL_LIB ||
    (draggable.sourcePlugin === ResourcePlugins.STICKER && isSharedPaster(draggable.data)) ||
    (draggable.sourcePlugin === ResourcePlugins.SOUND_EFFECT && isSharedSound(draggable.data))
  )
}

export const EditorDndWrapper: React.FC<PropsWithChildren> = ({ children }) => {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8
      }
    })
  )

  const { handleOverlayDragMove } = useTimelineItemMoving()
  const { handleTimelineItemAdjustStart, handleTimelineItemAdjustEnd } = useTimelineDnd()
  const { handleResourceDragStart, handleResourceDragMove, handleResourceDragEnd, } = useResourceDrag()

  const { handleResourceFolderOnDragEnd } = useItemActions()

  const [draggingResource, setDraggingResource] = useState<DraggingResourceData | null>(null)

  const handleDragStart = useCallback(
    ({ draggable, event }: TransformedDragEvent<DragStartEvent>) => {
      if (draggable.type === EditorDraggableTypes.TimelineItem) {
        const { overlay } = draggable
        return handleTimelineItemAdjustStart(overlay)
      }

      if (draggable.type === EditorDraggableTypes.Resource) {
        const resource = omit(draggable, 'type') as DroppableResource

        if (resource) {
          setDraggingResource({
            resource,
            x: event.activatorEvent.clientX,
            y: event.activatorEvent.clientY,
            dx: 0,
            dy: 0,
          })
          handleResourceDragStart(resource, event.activatorEvent.clientX)
        }
      }
    },
    [handleTimelineItemAdjustStart, handleResourceDragStart],
  )

  const handleDragMove = useCallback(
    ({ droppable, event, draggable }: TransformedDragEvent<DragMoveEvent>) => {
      // 处理时间轴中 `TimelineItem` 拖动的情况
      if (
        draggable.type === EditorDraggableTypes.TimelineItem &&
        droppable?.type === EditorDroppableTypes.TimelineTrack
      ) {
        const { x: deltaX } = event.delta
        const targetTrackIndex = droppable.track.index
        return handleOverlayDragMove(deltaX, targetTrackIndex)
      }

      // 处理左侧资源栏 (资源库/贴纸/音效/音乐/文字) 拖动的情况
      if (draggable.type === EditorDraggableTypes.Resource) {
        const currentMouseX = event.activatorEvent.clientX + event.delta.x
        const targetTrack = droppable?.type === EditorDroppableTypes.TimelineTrack
          ? droppable.track
          : undefined

        handleResourceDragMove(currentMouseX, targetTrack)

        setDraggingResource(prev =>
          prev && targetTrack === undefined
            ? {
              ...prev,
              dx: event.delta.x,
              dy: event.delta.y,
            }
            : null,
        )
      }
    },
    [handleOverlayDragMove, handleResourceDragMove],
  )

  const handleDragEnd = useCallback(
    ({ draggable, droppable }: TransformedDragEvent<DragEndEvent>) => {
      if (draggable.type === EditorDraggableTypes.TimelineItem) {
        return handleTimelineItemAdjustEnd()
      }

      // 处理 "资源库/我的音效/我的音乐/我的贴纸" 面板中, 将资源/文件夹拖拽到文件夹中的动作
      if (
        droppable?.type === EditorDroppableTypes.Folder
        && draggable.type === EditorDraggableTypes.Resource
        && canMoveResourceToFolder(draggable)
      ) {
        setDraggingResource(null)
        return handleResourceFolderOnDragEnd(
          draggable.data.materialType,
          droppable.resource.materialType,
          draggable.data.fileId,
          droppable.resource.fileId,
        )
      }

      if (draggable.type === EditorDraggableTypes.Resource) {
        setDraggingResource(null)
        handleResourceDragEnd()
        return
      }

      // 移到空白处也要清空预览图片
      setDraggingResource(null)
    },
    [handleTimelineItemAdjustEnd, handleResourceDragEnd, handleResourceFolderOnDragEnd],
  )

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={pointerWithin}
      onDragStart={v => handleDragStart(transformDragEvent(v))}
      onDragMove={v => handleDragMove(transformDragEvent(v))}
      onDragEnd={v => handleDragEnd(transformDragEvent(v))}
    >
      {children}

      {draggingResource && <DraggingResource {...draggingResource} />}
    </DndContext>
  )
}
