import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { usePlatform } from '@/modules/matrix/context/context'
import { GenericMatrixModule, PlatformKey } from '@/libs/request/api/generic-matrix'

export const useQueryAccountGroup = (defaultPlatform?: PlatformKey) => {
  const { currentPlatform } = usePlatform()

  const platform = defaultPlatform || currentPlatform

  return useQuery({
    queryKey: [QUERY_KEYS.ACCOUNT_GROUP, platform, defaultPlatform],
    queryFn: () => GenericMatrixModule.endpoints.groupList(platform),
  })
}
  