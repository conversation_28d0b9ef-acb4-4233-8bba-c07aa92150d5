import { Injectable, Logger } from '@nestjs/common'
import { MixcutableOverlay, MixcutIPC } from '@app/shared/types/ipc/mixcut.js'
import { MIXCUT_PIPELINES, MixcutPipelines } from '@app/shared/types/mixcut.js'

import { MixcutPipeline, MixcutPipelineContext } from './mixcut-pipeline.types.js'

import { VideoTrimPipeline } from './pipelines/video-trim.pipeline.js'
import { VideoScalePipeline } from './pipelines/video-scale.pipeline.js'
import { VideoRotationPipeline } from './pipelines/video-rotation.pipeline.js'
import { VideoPositionOffsetPipeline } from './pipelines/video-position-offset.pipeline.js'

import { GlobalTextPositionPipeline } from './pipelines/global-text-position.pipeline.js'
import { GlobalTextStyledTextPipeline } from './pipelines/global-text-styled-text.pipeline.js'
import { GlobalTextFontFamilyPipeline } from './pipelines/global-text-font-family.pipeline.js'

import { NarrationTextPositionPipeline } from './pipelines/narration-text-position.pipeline.js'
import { NarrationTextFontFamilyPipeline } from './pipelines/narration-text-font-family.pipeline.js'
import { NarrationTextStyledTextPipeline } from './pipelines/narration-text-styled-text.pipeline.js'

const PIPELINE_HANDLER_BY_NAME: {
  [K in MixcutPipelines]?: new (context: MixcutPipelineContext<K>) => MixcutPipeline<K>
} = {
  [MIXCUT_PIPELINES.video.trim]: VideoTrimPipeline,
  [MIXCUT_PIPELINES.video.scale]: VideoScalePipeline,
  [MIXCUT_PIPELINES.video.rotation]: VideoRotationPipeline,
  [MIXCUT_PIPELINES.video.positionOffset]: VideoPositionOffsetPipeline,

  [MIXCUT_PIPELINES.narrationText.fontFamily]: NarrationTextFontFamilyPipeline,
  [MIXCUT_PIPELINES.narrationText.styledText]: NarrationTextStyledTextPipeline,
  [MIXCUT_PIPELINES.narrationText.positionOffset]: NarrationTextPositionPipeline,

  [MIXCUT_PIPELINES.globalText.fontFamily]: GlobalTextFontFamilyPipeline,
  [MIXCUT_PIPELINES.globalText.styledText]: GlobalTextStyledTextPipeline,
  [MIXCUT_PIPELINES.globalText.positionOffset]: GlobalTextPositionPipeline,
}

@Injectable()
export class MixcutPipelineService {

  private readonly logger = new Logger(MixcutPipelineService.name)

  public async process(
    context: Pick<MixcutPipelineContext<any>, 'playerMetadata'>,
    overlays: MixcutableOverlay[],
    pipelines: Array<MixcutIPC.MixcutPipelineConfig>,
  ) {
    for (const pipeline of pipelines) {
      const { name, params } = pipeline

      const constructor = PIPELINE_HANDLER_BY_NAME[name]

      if (!constructor) {
        this.logger.warn('No pipeline handler found by pipeline name: ' + name)
        continue
      }

      const processor = new constructor({
        ...context,
        pipeline: name,
        overlays,
        params: params
      } as any)

      overlays = await processor.process()
    }

    return overlays
  }
}
