import z from 'zod'
import { accountFormSchema, commonAccountProductSchema, commonPoiSchema, commonPublishPlanSchema, publishSettingSchema, publishTimeTypeSchema, timeSettingSchema, titleDescSchema } from '../shared.schema'
import { CommercialType } from '../../enums/xhs.enums'
import { buildDetailDO } from '../../utils/shared'
import { timeSettingMap } from '../../constants/shared'
import { PlatformKey } from '@/libs/request/api/generic-matrix'

export const commercialTypeSchema = z.nativeEnum(CommercialType)

export const xhsPositionInfoSchema = commonPoiSchema.extend({
  poiType: z.number().optional(),
  subName: z.string().optional()
})

export const xhsFormSchema = z.object({
  setting: publishSettingSchema,
  timeType: publishTimeTypeSchema,
  poi: xhsPositionInfoSchema.optional(),
  timeSetting: timeSettingSchema.optional(),
  
  titles: z.array(titleDescSchema).optional(),
  accountList: z.array(accountFormSchema).min(1, '至少选择一个发布账号'),
  accountProducts: z.array(commonAccountProductSchema).optional(),
  commercialType: commercialTypeSchema,
  maxProduct: z.number().optional(),
})

export const xhsMergedFormSchema = xhsFormSchema.merge(commonPublishPlanSchema)

export const xhsSubmitSchema = xhsMergedFormSchema.transform(form => ({
  ...form,
  timeSetting: timeSettingMap[form.timeType]?.(form.timeSetting) ?? {},
  accountIds: form.accountList.map(a => a.id),
  accountProducts: form.accountProducts?.map(ap => ({
    accountId: ap.accountInfo.id,
    products: ap.products
  })),
  totalAccount: form.accountList.length,
  totalVideo: form.videoList.length,
  detailDOS: buildDetailDO(PlatformKey.xhs, form)
}))

export type XhsMergedForm = z.infer<typeof xhsMergedFormSchema>
export type XhsPositionInfo = z.infer<typeof xhsFormSchema>
export type XhsSubmitForm = z.infer<typeof xhsSubmitSchema>
