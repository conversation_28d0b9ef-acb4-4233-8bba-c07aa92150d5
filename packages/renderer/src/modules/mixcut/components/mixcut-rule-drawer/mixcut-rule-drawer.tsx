import React, { <PERSON><PERSON><PERSON><PERSON><PERSON>hildren, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Drawer, DrawerClose, Drawer<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>er<PERSON><PERSON><PERSON>, DrawerTrigger } from '@/components/ui/drawer'
import { Button } from '@/components/ui/button'
import { NumberInput } from '@/components/ui/number-input'
import { X } from 'lucide-react'
import { useMixcutContext } from '@/modules/mixcut/context/context'
import { MixcutRulesFormCategories } from '@/modules/mixcut/context/useMixcutRulesForm'

import { VideoTransform } from './rules-category-panels/video-transform'
import { GlobalTextTransform } from './rules-category-panels/global-text-transform'
import { NarrationTextTransform } from './rules-category-panels/narration-text-transform'

const CATEGORIES_MAP: Record<MixcutRulesFormCategories, { title: string, component: React.FC }> = {
  [MixcutRulesFormCategories.videoTransform]: { title: '视频变换处理', component: VideoTransform },
  [MixcutRulesFormCategories.narrationText]: { title: '口播字幕变换', component: NarrationTextTransform },
  [MixcutRulesFormCategories.globalTextTransform]: { title: '全局文字变换', component: GlobalTextTransform },
}

const TABS = Object
  .entries(CATEGORIES_MAP)
  .map(([key, value]) => ({
    key,
    label: value.title,
    component: value.component,
  }))

// 混剪规则 Tab 组件
const MixcutRulesTabs = () => {
  const [activeTab, setActiveTab] = useState(TABS[0].key)

  return (
    <div className="flex h-full">
      {/* 左侧垂直 Tab 导航 */}
      <div className="w-48 border-r border-border bg-muted/30">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          orientation="vertical"
          className="h-full"
        >
          <TabsList className="flex flex-col h-full w-full justify-start bg-transparent p-2 space-y-1">
            {TABS.map(tab => (
              <TabsTrigger
                key={tab.key}
                value={tab.key}
                className="w-full justify-start text-left px-3 py-2 text-sm font-normal
                  data-[state=active]:bg-background data-[state=active]:shadow-sm
                  hover:bg-background/50 transition-colors"
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      {/* 垂直分割线 */}
      <Separator orientation="vertical" className="h-full" />

      {/* 右侧内容区域 */}
      <div className="flex-1 p-6">
        <Tabs value={activeTab} className="h-full overflow-y-auto pr-4">
          {TABS.map(tab => (
            <TabsContent key={tab.key} value={tab.key} className="h-full">
              <div className="flex h-full text-muted-foreground">
                {tab.component && React.createElement(tab.component)}
                {/*{tab.label} 内容区域*/}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  )
}

// 混剪规则 Drawer 组件
export const MixcutRulesDrawer: React.FC<PropsWithChildren> = ({ children }) => {
  const {
    generation: {
      generateCount, drawerOpen,
      setGenerateCount, generateCombinations, setDrawerOpen
    }
  } = useMixcutContext()

  return (
    <Drawer direction="right" open={drawerOpen} onOpenChange={setDrawerOpen}>
      <DrawerTrigger asChild>
        {children}
      </DrawerTrigger>

      <DrawerContent className="max-w-[640px] p-0 flex flex-col">
        {/* Drawer 标题栏 */}
        <DrawerHeader className="px-6 py-4 border-b border-border">
          <div className="flex items-center justify-between">
            <DrawerTitle className="text-lg font-medium">
              混剪规则设置
            </DrawerTitle>
            <DrawerClose asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <X className="h-4 w-4" />
              </Button>
            </DrawerClose>
          </div>
        </DrawerHeader>

        {/* Drawer 内容区域 */}
        <div className="flex-1 overflow-hidden">
          <MixcutRulesTabs />
        </div>

        {/* 底部生成数量控制 */}
        <div className="px-6 py-4 border-t border-border bg-muted/30">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-sm font-medium text-foreground">生成混剪视频数量:</span>
              <NumberInput
                value={generateCount}
                onChange={setGenerateCount}
                min={1}
                max={50}
                step={1}
                className="w-20"
              />
              <span className="text-xs text-muted-foreground">个</span>
            </div>
            <div className="text-xs text-muted-foreground">
              建议设置1-20个，数量过多可能影响生成速度
            </div>
          </div>

          <div className="flex justify-end">
            <Button variant="default" size="sm" onClick={generateCombinations}>
              生成混剪预览
            </Button>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  )
}
