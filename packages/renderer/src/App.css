#root {
  max-width: 1280px;
}

/* 默认允许文本选择，只对特定元素禁用 */
button, .app-drag-region, .no-select {
  user-select: none;
}

/* 明确允许文本选择的元素 */
.selectable, input, textarea, [contenteditable] {
  user-select: text;
}

/* 窗口拖动区域 */
.app-drag-region {
  -webkit-app-region: drag;
  app-region: drag;
}

/* 禁止拖动的区域（按钮等交互元素） */
.app-no-drag {
  -webkit-app-region: no-drag;
  app-region: no-drag;
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Webkit浏览器 (Chrome, Safari, Edge等) */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #333;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #666;
  border-radius: 3px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}



/* 专门修复 Radix 隐藏控件导致的滚动异常 */
/* [aria-hidden="true"][tabindex="-1"] {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 1px !important;
  height: 1px !important;
  margin: 0 !important;
  border: 0 !important;
  padding: 0 !important;
  clip: rect(0, 0, 0, 0) !important;
  clip-path: inset(50%) !important;
  overflow: hidden !important;
  white-space: nowrap !important;
} */
