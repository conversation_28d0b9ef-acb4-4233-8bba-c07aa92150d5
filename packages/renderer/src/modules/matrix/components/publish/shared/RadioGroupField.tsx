import { Label } from '@/components/ui/label'
import React from 'react'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'

export interface RadioOption<T extends string | number> {
  value: T
  label: string
}

interface RadioGroupFieldProps<T extends string | number> {
  label: string
  options: RadioOption<T>[]
  value: T
  onChange: (value: T) => void
  error?: string,
  tailContent?: (val: T) => React.ReactNode
}

export const RadioGroupField = <T extends string | number>({
  label,
  options,
  value,
  onChange,
  error,
  tailContent
}: RadioGroupFieldProps<T>) => {
  return (
    <div className="space-y-6">
      <div className="flex gap-6 items-center">
        <Label>{label}</Label>
        <RadioGroup
          value={String(value)}
          onValueChange={(val: string) => onChange(val as unknown as T)}
          className="flex gap-3"
        >
          {options.map(option => (
            <div key={option.value} className="flex items-center space-x-2">
              <RadioGroupItem
                value={String(option.value)}
                id={`${label}-${option.value}`}
              />
              <Label
                htmlFor={`${label}-${option.value}`}
                className="text-sm font-normal cursor-pointer"
              >
                {option.label}
              </Label>
              {tailContent?.(option.value) }
            </div>
          ))}
        </RadioGroup>
        {error && <p className="text-sm text-red-500">{error}</p>}
      </div>
    </div>
  )
}