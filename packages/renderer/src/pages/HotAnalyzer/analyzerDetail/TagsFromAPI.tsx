import React, { useEffect, useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Loader2 } from 'lucide-react'
interface TagsFromAPIProps {
  dependOnValue: string
  fetchTags: (keyword: string) => Promise<string[]> // 从接口获取标签
  debounceTime?: number
  onTagClick?: (tag: string) => void
}

export const TagsFromAPI: React.FC<TagsFromAPIProps> = ({ dependOnValue, fetchTags, debounceTime = 500, onTagClick }) => {
  const [tags, setTags] = useState<string[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!dependOnValue) {
      setTags([])
      setLoading(false)
      return
    }

    setLoading(true)
    const handler = setTimeout(() => {
      fetchTags(dependOnValue)
        .then(res => setTags(res))
        .finally(() => setLoading(false))
    }, debounceTime)

    return () => clearTimeout(handler)
  }, [dependOnValue, fetchTags, debounceTime])

  return (
    <div className="relative flex gap-2 overflow-x-auto whitespace-nowrap">
      {loading && <Loader2 className="absolute left-0 w-4 h-4 animate-spin" />}
      {tags.map(tag => (
        <Badge key={tag} variant="primaryHighlight1" className="rounded mb-1 cursor-pointer" onClick={() => onTagClick?.(tag)}>
          {tag}
        </Badge>
      ))}
    </div>
  )
}
