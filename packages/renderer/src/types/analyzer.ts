export enum AnalyzeStatus {
  SUCCESS = 'SUCCESS', // 成功
  PENDING = 'PENDING', // 排队中
  STARTED = 'STARTED', // 任务正在执行
  FAILURE = 'FAILURE', // 任务异常
  REVOKED = 'REVOKED', // 任务取消
}

export type HotAnalyzer = {
  id: number
  taskId: string
  title: string
  coverUrl: string
  videoDuration: number
  status: AnalyzeStatus
  createTime: number
}

export type AnalyzerScriptScene = {
  notes: string
  shotType: string
  url: string
}

export type AnalyzerScript = {
  text: string
  script_name: string
  scene: AnalyzerScriptScene[]
}

export type HotAnalyzerInfo = {
  title: string
  create_time: number
  des: any
  videoUrl: string
  script: AnalyzerScript[]
}