import { IndexableOverlay, IndexableTrack, Track, TrackType } from '@/modules/video-editor/types'
import { Overlay, OverlayType, TransitionOverlay } from '@app/shared/types/overlay'
import {
  byStartFrame,
  byStoryboard,
  findOverlaysAboveStoryboard,
  findOverlaysBetweenFrames,
  forRelevantOverlays,
  getOverlayTimeRange
} from '@/modules/video-editor/utils/overlay-helper'
import { FPS, OVERLAY_THRESHOLD_WHEN_DRAG_TO_SWAP } from '@/modules/video-editor/constants'
import { DraggableState, OverlaysAdjustment } from './types'
import { calculateLeftSpaceForMoving, getStoryboards } from '../../utils/track-helper'

/**
 * 检测转场效果是否与现有转场效果冲突
 * @param tracks 所有轨道
 * @param prevStoryboardIndex 前一个分镜索引
 * @returns 是否存在冲突
 */
function checkTransitionConflict(
  tracks: Track[],
  prevStoryboardIndex: number,
): boolean {
  // 找到分镜轨道
  const storyboardTrack = tracks.find(track => track.type === TrackType.STORYBOARD)
  if (!storyboardTrack) {
    return false
  }

  // 性能优化：直接在过滤的同时检查冲突，避免创建中间数组
  return storyboardTrack.overlays.some(overlay => {
    if (overlay.type !== OverlayType.TRANSITION) {
      return false
    }

    const transition = overlay as TransitionOverlay
    return transition.prevStoryboardIndex === prevStoryboardIndex
  })
}

/**
 * 计算转场效果应该放置在哪两个分镜之间
 */
function calculateTransitionStoryboardIndices(
  tracks: Track[],
  targetFrame: number
): { prevIndex: number; isValid: boolean } {
  const storyboards = getStoryboards(tracks)

  if (storyboards.length < 2) {
    return { prevIndex: -1, isValid: false }
  }

  for (let i = 0; i < storyboards.length - 1; i++) {
    const currentStoryboard = storyboards[i]
    const nextStoryboard = storyboards[i + 1]

    const [, currentEnd] = getOverlayTimeRange(currentStoryboard)
    const [nextStart] = getOverlayTimeRange(nextStoryboard)

    // 检查目标帧是否在当前分镜和下一个分镜之间. 允许 30 帧的容错范围
    if (targetFrame >= currentEnd - 30 && targetFrame <= nextStart + 30) {
      const hasConflict = checkTransitionConflict(tracks, i)

      return { prevIndex: i, isValid: !hasConflict }
    }
  }

  // 找不到合适的位置
  return { prevIndex: -1, isValid: false }
}

export class AdjustCalculator {

  constructor(private readonly tracks: Track[]) {
  }

  /**
   * 计算移动情况下的 OverlaysAdjust
   * @param currentOverlay 当前正在调整的 Overlay
   * @param originalStoryboard 当前 Overlay 所处的原始分镜
   * @param originalTrack 当前 Overlay 所处的原始轨道
   * @param targetStoryboard 拖拽目标分镜
   * @param targetTrack 拖拽目标轨道
   * @param intendedStartFrame 目标起始帧
   */
  public calcAdjustForMoving(
    currentOverlay: Overlay,
    originalStoryboard: IndexableOverlay | null,
    originalTrack: IndexableTrack | null,
    targetStoryboard: IndexableOverlay | null,
    targetTrack: IndexableTrack,
    intendedStartFrame: number,
  ): DraggableState {
    if (!targetTrack.isGlobalTrack && !targetStoryboard) {
      return { draggable: false }
    }

    if (currentOverlay.type === OverlayType.TRANSITION) {
      if (targetTrack.type !== TrackType.STORYBOARD) {
        return { draggable: false }
      }

      return this.#handleTransitionOverlay(currentOverlay, intendedStartFrame)
    }

    // 正在移动的是分镜
    const isMovingStoryboard = originalTrack
      && originalTrack.type === TrackType.STORYBOARD
      && targetTrack.type === TrackType.STORYBOARD
      && currentOverlay.type === OverlayType.STORYBOARD

    // 正在移动的是视频, 且移动位置在同一个轨道、同一分镜下
    const isMovingVideoWithinSameStoryboard = originalTrack
      && originalTrack.index === targetTrack.index
      && originalStoryboard
      && targetStoryboard
      && originalStoryboard.index === targetStoryboard.index
      && targetTrack.type === TrackType.VIDEO

    if (isMovingStoryboard || isMovingVideoWithinSameStoryboard) {
      // console.debug({ isMovingStoryboard, isMovingInsideGroup })
      const { overlaysAdjust, fromFrameShift } = this.calcAdjustForInsideMoving(
        currentOverlay,
        isMovingVideoWithinSameStoryboard
          ? targetTrack.overlays.filter(byStoryboard(originalStoryboard))
          : targetTrack.overlays.filter(o => o.type === OverlayType.STORYBOARD),
        intendedStartFrame,
      )

      return {
        draggable: true,
        overlaysAdjust,
        adjustedStartFrame: currentOverlay.from + fromFrameShift,
        adjustedRow: originalTrack.index,
      }
    }

    if (targetStoryboard) {
      const leftSpace = calculateLeftSpaceForMoving(targetTrack, targetStoryboard, currentOverlay)
      // console.debug(`[adjust-calculator:73] ${leftSpace}`)
      if (leftSpace <= 0) {
        // console.debug('no enough left space in storyboard')
        return {
          draggable: false
        }
      }
    }

    let adjustedStartFrame = intendedStartFrame
    let adjustedDuration = currentOverlay.durationInFrames
    const overlaysAdjust = new OverlaysAdjustment()

    // 仅当从混剪视频轨道中拖出视频时, 才需要填补空隙
    const shouldRemoveGap = !!originalTrack
      && !originalTrack.isGlobalTrack
      && originalTrack.type === TrackType.VIDEO
      && !!originalStoryboard

    if (shouldRemoveGap) {
      overlaysAdjust.apply(
        this.#fillGapOfTracks(
          currentOverlay,
          originalTrack!.overlays.filter(byStoryboard(originalStoryboard))
        )
      )
    }

    // 仅当拖动到混剪视频轨道中时, 才需要自动贴合到最左侧
    const shouldStickToLeft = !targetTrack.isGlobalTrack
      && targetTrack.type === TrackType.VIDEO
      && !!targetStoryboard

    if (shouldStickToLeft) {
      const positionToStick = this.#findPositionToStick(
        targetTrack.overlays.filter(byStoryboard(targetStoryboard)),
        intendedStartFrame
      )

      adjustedStartFrame = positionToStick ?? targetStoryboard!.from
      // console.debug(`Due to shouldStickToLeft=true, adjustedStartFrame => ${adjustedStartFrame}`)
    }

    // 当拖入到混剪视频轨道中时, 可以触发 “重叠以向后推移” 的效果，用于适配插入的位置不在末端的情况
    if (!targetTrack.isGlobalTrack && targetTrack.type === TrackType.VIDEO) {
      overlaysAdjust.apply(
        this.#pushOverlappedOverlays(
          targetTrack.overlays.filter(byStoryboard(targetStoryboard)),
          adjustedStartFrame,
          currentOverlay.durationInFrames
        )
      )
    } else {
      const [from, duration, adjust] = this.calcNormalMoving(
        currentOverlay,
        targetTrack.overlays.filter(forRelevantOverlays({
          storyboard: targetStoryboard,
          track: targetTrack,
          currentOverlay,
          excludingOverlay: currentOverlay
        })),
        adjustedStartFrame,
        !!targetTrack.isGlobalTrack
      )

      adjustedStartFrame = from
      adjustedDuration = duration
      // console.debug(`Due to MovingToGlobalTrack=true, adjustedStartFrame => ${adjustedStartFrame}, adjustedDuration => ${adjustedDuration}`)
      overlaysAdjust.apply(adjust)
    }

    // 拖动到分镜轨道中时, 限制结束时间不超过该分镜的结束时间
    if (targetStoryboard) {
      const [, targetStoryboardEnd] = getOverlayTimeRange(targetStoryboard)
      adjustedDuration = Math.min(adjustedDuration, targetStoryboardEnd - adjustedStartFrame)
    }
    // 否则限制结束时间不超过最后一个分镜的结束时间
    else {
      const storyboardDuration = getStoryboards(this.tracks)
        .reduce(
          (total, storyboard) => total + storyboard.durationInFrames
          , 0
        )

      if (storyboardDuration) {
        adjustedDuration = Math.min(adjustedDuration, storyboardDuration - adjustedStartFrame)
      } else {
        // 当没有任何分镜时, 则默认限制为 20 秒
        adjustedDuration = Math.min(adjustedDuration, 20 * FPS)
      }
    }

    if (adjustedDuration < FPS * 0.5) {
      return {
        draggable: false
      }
    }

    return {
      draggable: true,
      overlaysAdjust,
      adjustedStartFrame,
      adjustedDuration,
      targetStoryboardIndex: targetStoryboard?.index
    }
  }

  #handleTransitionOverlay(
    currentOverlay: TransitionOverlay,
    intendedStartFrame: number,
  ): DraggableState {
    const { prevIndex, isValid } = calculateTransitionStoryboardIndices(this.tracks, intendedStartFrame)

    if (!isValid || prevIndex === -1) {
      return { draggable: false }
    }

    const storyboards = getStoryboards(this.tracks)
    const prevStoryboard = storyboards[prevIndex]
    const [, prevEnd] = getOverlayTimeRange(prevStoryboard)

    // 计算转场效果的位置（在前一个分镜结束位置的中心）
    return {
      draggable: true,
      adjustedStartFrame: prevEnd - currentOverlay.durationInFrames / 2,
      updatedOverlay: {
        ...currentOverlay,
        prevStoryboardIndex: prevIndex,
      }
    }
  }

  #pushOverlappedOverlays(targetOverlays: Overlay[], startFrame: number, distanceToPush: number): OverlaysAdjustment {
    return new OverlaysAdjustment(
      targetOverlays
        .filter(o => {
          return o.from >= startFrame
        })
        .map(o => [o.id, { fromFrameShift: distanceToPush }])
    )
  }

  #findPositionToStick(
    targetOverlays: Overlay[],
    intendedNewFrom: number,
  ): number | null {
    const target = targetOverlays
      .sort(byStartFrame())
      .find(o => {
        return (o.from + o.durationInFrames / 2) > intendedNewFrom
      })

    if (target) return target.from
    if (targetOverlays.at(-1)) {
      const [, lastEnd] = getOverlayTimeRange(targetOverlays.at(-1))
      return lastEnd
    }
    return null
  }

  /**
   * 用于从 `sourceOverlays` 中移走 `currentOverlay` 时, 将其后方的 Overlay 前移, 以填补空隙
   * @param removedOverlay
   * @param sourceOverlays
   * @private
   */
  #fillGapOfTracks(
    removedOverlay: Overlay,
    sourceOverlays: Overlay[],
  ) {
    const overlaysAdjust = new OverlaysAdjustment()

    const affectedOverlays = findOverlaysBetweenFrames(
      sourceOverlays,
      removedOverlay.from + removedOverlay.durationInFrames - 1,
      Infinity,
      'start'
    )
    affectedOverlays
      .filter(o => o.id !== removedOverlay.id)
      .forEach(o => {
        overlaysAdjust.set(o.id, { fromFrameShift: -removedOverlay.durationInFrames })
      })

    return overlaysAdjust
  }

  /**
   * 处理其他情况下的移动情况
   * @param currentOverlay 当前正在调整的 Overlay
   * @param targetOverlays 目标轨道中的 Overlay
   * @param intendedNewFrom 目标起始帧
   * @param allowPush 是否允许在右侧发生重叠时, 将重叠元素向后推移
   * @return [number, number, OverlaysAdjustment] 元组. 分别为:
   *            1. 当前 Overlay 经调整后的起始帧
   *            2. 当前 Overlay 经调整后的持续时长
   *            3. 目标轨道中受影响的 Overlay 的调整方案
   */
  public calcNormalMoving(
    currentOverlay: Pick<Overlay, 'durationInFrames'>,
    targetOverlays: Overlay[],
    intendedNewFrom: number,
    allowPush: boolean
  ): [number, number, OverlaysAdjustment] {
    let adjustedFrom = intendedNewFrom
    let adjustedDuration = currentOverlay.durationInFrames
    const overlaysAdjust = new OverlaysAdjustment()

    const leftOverlapping = findOverlaysBetweenFrames(
      targetOverlays,
      -1,
      intendedNewFrom,
      'start',
    ).filter(o => o.from + o.durationInFrames > intendedNewFrom)
      .sort(byStartFrame('desc'))
      .at(0)

    // 左侧有重叠的情况下，则以重叠元素的结尾帧作为拖动的新起始帧
    if (leftOverlapping) {
      adjustedFrom = leftOverlapping.from + leftOverlapping.durationInFrames
    }

    const rightOverlapping = findOverlaysBetweenFrames(
      targetOverlays,
      // 若左侧有重叠时, 目标位置已向右偏移。所以后续应以 `adjustedFrom` 作为筛选的起始帧
      adjustedFrom - 1,
      Infinity,
      'start'
    ).filter(o => o.from < adjustedFrom + currentOverlay.durationInFrames)
      .sort(byStartFrame())
      .at(0)

    // 两侧都没有重叠的情况下, 则允许拖入并无需对目标轨道进行调整，
    if (!leftOverlapping && !rightOverlapping) {
      return [adjustedFrom, adjustedDuration, overlaysAdjust]
    }

    // 右侧有重叠的情况下, 根据传入的 `allowPush` 参数决定执行何种操作
    if (rightOverlapping) {
      // 将重叠元素向后推移
      if (allowPush) {
        const affectedOverlays = findOverlaysBetweenFrames(
          targetOverlays,
          rightOverlapping.from - 1,
          Infinity,
          'start'
        )

        const overlapSize = adjustedFrom + currentOverlay.durationInFrames - rightOverlapping.from

        affectedOverlays.forEach(o => {
          overlaysAdjust.set(o.id, { fromFrameShift: overlapSize })
        })
      }
      // 不允许推移的情况下, 限制当前 Overlay 的持续时长，使其不超过右侧重叠元素的起始帧
      else {
        adjustedDuration = rightOverlapping.from - adjustedFrom
      }
    }

    return [adjustedFrom, adjustedDuration, overlaysAdjust]
  }

  /**
   * 计算 "组内" 移动情况下的 OverlaysAdjust.
   * 以下两种情况可被视为 "组内" 移动:
   *   1. 在分镜轨道中移动分镜
   *   2. 在混剪视频轨道的同一个分镜下移动视频
   *
   * 在组内移动时拥有如下特性：
   *   1. 拖动位置重叠时将会触发位置交换
   *   2. 如果是分镜发生了位置交换，则其下的所有 Overlay 也会跟随移动
   */
  private calcAdjustForInsideMoving(
    currentOverlay: Overlay,
    restOverlays: Overlay[],
    intendedNewFrom: number,
  ): {
    overlaysAdjust: OverlaysAdjustment
    fromFrameShift: number
  } {
    const intendedNewDuration = currentOverlay.durationInFrames

    // 移动方向是否为时间轴的起始方向
    const isBackward = intendedNewFrom < currentOverlay.from
    const [currentFrom, currentEnd] = getOverlayTimeRange(currentOverlay)
    const intendedNewEnd = intendedNewFrom + intendedNewDuration

    // 根据移动方向和拖动位置, 筛选出受影响的分镜
    const affectedOverlays = findOverlaysBetweenFrames(
      restOverlays,
      isBackward ? intendedNewFrom : currentFrom,
      isBackward ? currentEnd : intendedNewEnd,
      isBackward ? 'end' : 'start',
      OVERLAY_THRESHOLD_WHEN_DRAG_TO_SWAP
    ).filter(o => o.id !== currentOverlay.id)

    if (!affectedOverlays.length) {
      return {
        overlaysAdjust: new OverlaysAdjustment(),
        fromFrameShift: 0
      }
    }

    const overlaysAdjust = new OverlaysAdjustment()

    const targetStoryboardIndex = affectedOverlays.at(isBackward ? 0 : -1)!.index

    // 计算所有受影响分镜的总时长; 同时处理受影响分镜(及其下 Overlay)的移动
    const totalDuration = affectedOverlays.reduce(
      (result, item) => {
        // 根据拖动方向, 移动所涉及的分镜
        const fromFrameShift = currentOverlay.durationInFrames * (isBackward ? 1 : -1)
        overlaysAdjust.set(item.id, {
          fromFrameShift
        })

        if (item.type === OverlayType.STORYBOARD) {
          // 分镜下的所有 Overlay 也要跟随移动, 并且需要更新其所在分镜序号
          findOverlaysAboveStoryboard(this.tracks, item)
            .forEach(relatedOverlay => {
              overlaysAdjust.set(relatedOverlay.id, {
                fromFrameShift,
                ...(relatedOverlay.storyboardIndex !== undefined && {
                  targetStoryboardIndex: relatedOverlay.storyboardIndex + (isBackward ? 1 : -1)
                })
              })
            })
        }

        return result + item.durationInFrames
      },
      0
    )

    const shiftOfCurrentOverlay = totalDuration * (isBackward ? -1 : 1)
    overlaysAdjust.set(currentOverlay.id, { fromFrameShift: shiftOfCurrentOverlay })

    if (currentOverlay.type === OverlayType.STORYBOARD) {
      findOverlaysAboveStoryboard(this.tracks, currentOverlay)
        .forEach(relatedOverlay => {
          overlaysAdjust.set(relatedOverlay.id, {
            fromFrameShift: shiftOfCurrentOverlay,
            targetStoryboardIndex,
          })
        })
    }

    return {
      overlaysAdjust,
      fromFrameShift: shiftOfCurrentOverlay
    }
  }
}
