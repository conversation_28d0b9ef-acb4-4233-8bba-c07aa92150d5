import z from 'zod'
import { PublishTimeType, VideoMountMode } from './enums/shared'
import { PlatformKey } from '@/libs/request/api/generic-matrix'
import { PublishData } from './schemas/publish.schema'

type ValidationRule = (args: {
  data: PublishData,
  ctx: z.RefinementCtx,
}) => void

export const platformValidationRules: Record<PlatformKey, ValidationRule[]> = {
  dy: [
    ({ data, ctx }) => {
      const { common, platforms } = data
      const dy = platforms.dy
      if (dy && dy.accountList.length > common.videoList.length) {
        ctx.addIssue({
          code: 'custom',
          path: ['platforms', 'dy', 'accountIds'],
          message: '选择的账号数量不能大于视频数量',
        })
      }
    },
  
    ({ data, ctx }) => {
      const { platforms } = data
      const dy = platforms.dy
      if (dy?.mountType === VideoMountMode.LOCATION && !dy?.poi?.poiName) {
        ctx.addIssue({
          code: 'custom',
          path: ['poi', 'poiName'],
          message: '选择位置挂载时，位置名称为必填项',
        })
      }
    },
    ({ data, ctx }) => {
      const { platforms } = data
      const dy = platforms.dy
      if (dy?.mountType === VideoMountMode.CART && (!dy?.accountProducts || dy?.accountProducts.length === 0)) {
        ctx.addIssue({
          code: 'custom',
          path: ['accountProducts'],
          message: '选择购物车挂载时，请至少为一个账号配置商品',
        })
      }
    },
    ({ data, ctx }) => {
      const { platforms } = data
      const dy = platforms.dy

      if (dy?.timeType === PublishTimeType.SCHEDULED) {
        if (!dy?.timeSetting?.publishTime) {
          ctx.addIssue({
            code: 'custom',
            path: ['timeSetting', 'publishTime'],
            message: '选择定时发布时，发布时间为必填项',
          })
        }
      }
    },
    
  ],
  xhs: [],
  ks: []
}