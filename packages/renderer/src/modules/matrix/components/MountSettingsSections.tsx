import { Label } from '@/components/ui/label'
import React, { FC } from 'react'
import { RadioGroup, RadioGroupItem } from '../../../components/ui/radio-group'
import { toast } from 'react-toastify'
import { cn } from '../../../components/lib/utils'
import { VideoMountMode } from '@/modules/matrix/enums/shared'

const mountTypeOptions = [
  { value: VideoMountMode.NONE, label: '不挂载' },
  { value: VideoMountMode.LOCATION, label: '位置' },
  { value: VideoMountMode.CART, label: '购物车' }
]

interface MountSettingsSectionProps {
  value: VideoMountMode
  onChange: (value: VideoMountMode) => void
  isDisabled?: boolean
  errors?: string
}

export const MountSettingsSection: FC<MountSettingsSectionProps> = ({ value, onChange, isDisabled, errors }) => {
  const isCartDisabled = isDisabled
    
  const handleCartOptionClick = (e: React.MouseEvent) => {
    if (isCartDisabled) {
      e.preventDefault()
      e.stopPropagation()
      toast('请先选择您的发布账号，完成选择后才能成功添加到购物车。', {
        type: 'warning'
      })
    }
  }
 
  return (
    <div className="space-y-6">
      {/* 视频位置/小程序挂载 */}
      <div className="flex gap-6 items-center">
        <Label>视频位置/小程序挂载</Label>
        <RadioGroup
          value={value.toString()}
          onValueChange={(value: string) => {
            const numValue = Number(value)

            if (numValue === VideoMountMode.CART && isCartDisabled) {
              return handleCartOptionClick({} as React.MouseEvent)
            }
            onChange(numValue)
          }}
          className="flex gap-3"
        >
          {mountTypeOptions.map(option => {
            const isCartOption = option.value === VideoMountMode.CART
            const isDisabled = isCartOption && isCartDisabled

            return (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem
                    
                  value={option.value.toString()}
                  id={`mountType-${option.value}`}
                  disabled={isDisabled}
                />
                <Label
                  htmlFor={`mountType-${option.value}`}
                  className={cn({ 'cursor-not-allowed opacity-50': isDisabled }, 'text-sm font-normal cursor-pointer ')}
                >
                  {option.label}
                </Label>
              </div>
            )
          })}
        </RadioGroup>

        {errors && (
          <p className="text-sm text-red-500">{errors}</p>
        )}
      </div>
     
    </div>
  )
}
