import React, { useState, useMemo, useEffect } from 'react'
import { <PERSON>alog, CustomDialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { toast } from 'react-toastify'
import { useInfiniteQueryProjectList } from '@/hooks/queries/useQueryProject'
import { ResourceModule } from '@/libs/request/api/resource'
import { ScriptSceneData } from '@/hooks/queries/useQueryScript'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { HotAnalyzerInfo } from '@/types/analyzer'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore'

interface SaveScriptDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  scriptData?: HotAnalyzerInfo
  description?: string
  onParentClose?: () => void
}

const SaveScriptDialog: React.FC<SaveScriptDialogProps> = ({
  open,
  onOpenChange,
  scriptData,
  description,
  onParentClose,
}) => {
  const [selectedProject, setSelectedProject] = useState<string | null>(null)
  const [scriptName, setScriptName] = useState<string>('')
  const [saving, setSaving] = useState(false)

  const { data: projectData } = useInfiniteQueryProjectList({})
  const projects = useMemo(() => projectData?.pages.flatMap(page => page.list) || [], [projectData])
  const queryClient = useQueryClient()
  const { pushNamedTab } = useVirtualTabsStore()

  const handleSave = async () => {
    if (!scriptData) {
      toast.error('脚本数据丢失，请重试')
      return
    }
    if (!scriptData || !selectedProject || !scriptName.trim()) {
      toast.error('请选择项目并填写脚本名字')
      return
    }
    setSaving(true)
    try {
      const scriptSceneData: ScriptSceneData[] = scriptData.script.map(item => {
        const notes = item.scene.map(sceneItem => sceneItem.notes).join('\n') // 合并 notes
        const urls = item.scene.map(sceneItem => sceneItem.url) // url 数组
        const shotType = item.scene.length > 0 ? item.scene[0].shotType : '' // 取第一个

        return {
          id: crypto.randomUUID(),
          title: item.script_name,
          shotType,
          script: item.text,
          notes,
          refVideo: urls,
        }
      })
      const data = await ResourceModule.script.create({
        projectId: Number(selectedProject),
        title: scriptName.trim(),
        content: JSON.stringify(scriptSceneData),
        fullContent: '[]',
      })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SCRIPT_LIST] })
      toast.success('脚本保存成功')
      onParentClose?.()
      pushNamedTab('Script', {
        id: data.toString(),
      })
      onOpenChange(false)
    } catch (err: any) {
      toast.error(err?.message || '脚本保存失败')
    } finally {
      setSaving(false)
    }
  }

  useEffect(() => {
    if (!open) {
      setSelectedProject(null)
      setScriptName('')
    }
  }, [open])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <CustomDialogContent>
        <DialogHeader>
          <DialogTitle>保存脚本</DialogTitle>
        </DialogHeader>
        {description && <p className="mb-4 text-sm text-muted-foreground">{description}</p>}

        <Select value={selectedProject || ''} onValueChange={setSelectedProject}>
          <SelectTrigger>
            <SelectValue placeholder="请选择项目" />
          </SelectTrigger>
          <SelectContent>
            {projects.map((p: any) => (
              <SelectItem key={p.id} value={p.id}>
                {p.projectName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {/* 脚本名字输入框 */}
        <div className="mb-4">
          <Input placeholder="请输入脚本名字" value={scriptName} onChange={e => setScriptName(e.target.value)} />
        </div>
        <DialogFooter className="mt-4 flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button
            className="bg-primary-highlight1 hover:bg-primary-highlight1/80 text-white"
            disabled={!selectedProject || saving}
            onClick={handleSave}
          >
            {saving ? '保存中...' : '确认保存'}
          </Button>
        </DialogFooter>
      </CustomDialogContent>
    </Dialog>
  )
}

export default SaveScriptDialog
