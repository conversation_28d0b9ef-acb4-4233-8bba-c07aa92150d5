var At = Object.defineProperty;
var Wt = (t, e, n) => e in t ? At(t, e, { enumerable: !0, configurable: !0, writable: !0, value: n }) : t[e] = n;
var P = (t, e, n) => Wt(t, typeof e != "symbol" ? e + "" : e, n);
import dt, { useContext as _t, useMemo as v, useState as ut, useEffect as nt, useRef as $t, useCallback as ct, Fragment as It } from "react";
import { useCurrentFrame as rt, interpolate as h, Easing as K, Audio as Pt, delayRender as jt, continueRender as et, OffthreadVideo as Mt, Sequence as pt, AbsoluteFill as Ot, registerRoot as Dt, Composition as Nt } from "remotion";
import * as Yt from "opentype.js";
var Q = { exports: {} }, G = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var mt;
function zt() {
  if (mt) return G;
  mt = 1;
  var t = Symbol.for("react.transitional.element"), e = Symbol.for("react.fragment");
  function n(r, s, o) {
    var i = null;
    if (o !== void 0 && (i = "" + o), s.key !== void 0 && (i = "" + s.key), "key" in s) {
      o = {};
      for (var c in s)
        c !== "key" && (o[c] = s[c]);
    } else o = s;
    return s = o.ref, {
      $$typeof: t,
      type: r,
      key: i,
      ref: s !== void 0 ? s : null,
      props: o
    };
  }
  return G.Fragment = e, G.jsx = n, G.jsxs = n, G;
}
var q = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var gt;
function vt() {
  return gt || (gt = 1, process.env.NODE_ENV !== "production" && function() {
    function t(a) {
      if (a == null) return null;
      if (typeof a == "function")
        return a.$$typeof === I ? null : a.displayName || a.name || null;
      if (typeof a == "string") return a;
      switch (a) {
        case y:
          return "Fragment";
        case E:
          return "Profiler";
        case b:
          return "StrictMode";
        case F:
          return "Suspense";
        case O:
          return "SuspenseList";
        case H:
          return "Activity";
      }
      if (typeof a == "object")
        switch (typeof a.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), a.$$typeof) {
          case w:
            return "Portal";
          case k:
            return (a.displayName || "Context") + ".Provider";
          case S:
            return (a._context.displayName || "Context") + ".Consumer";
          case j:
            var m = a.render;
            return a = a.displayName, a || (a = m.displayName || m.name || "", a = a !== "" ? "ForwardRef(" + a + ")" : "ForwardRef"), a;
          case X:
            return m = a.displayName || null, m !== null ? m : t(a.type) || "Memo";
          case _:
            m = a._payload, a = a._init;
            try {
              return t(a(m));
            } catch {
            }
        }
      return null;
    }
    function e(a) {
      return "" + a;
    }
    function n(a) {
      try {
        e(a);
        var m = !1;
      } catch {
        m = !0;
      }
      if (m) {
        m = console;
        var R = m.error, T = typeof Symbol == "function" && Symbol.toStringTag && a[Symbol.toStringTag] || a.constructor.name || "Object";
        return R.call(
          m,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          T
        ), e(a);
      }
    }
    function r(a) {
      if (a === y) return "<>";
      if (typeof a == "object" && a !== null && a.$$typeof === _)
        return "<...>";
      try {
        var m = t(a);
        return m ? "<" + m + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function s() {
      var a = B.A;
      return a === null ? null : a.getOwner();
    }
    function o() {
      return Error("react-stack-top-frame");
    }
    function i(a) {
      if (N.call(a, "key")) {
        var m = Object.getOwnPropertyDescriptor(a, "key").get;
        if (m && m.isReactWarning) return !1;
      }
      return a.key !== void 0;
    }
    function c(a, m) {
      function R() {
        L || (L = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          m
        ));
      }
      R.isReactWarning = !0, Object.defineProperty(a, "key", {
        get: R,
        configurable: !0
      });
    }
    function u() {
      var a = t(this.type);
      return U[a] || (U[a] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), a = this.props.ref, a !== void 0 ? a : null;
    }
    function g(a, m, R, T, W, A, at, ot) {
      return R = A.ref, a = {
        $$typeof: d,
        type: a,
        key: m,
        props: A,
        _owner: W
      }, (R !== void 0 ? R : null) !== null ? Object.defineProperty(a, "ref", {
        enumerable: !1,
        get: u
      }) : Object.defineProperty(a, "ref", { enumerable: !1, value: null }), a._store = {}, Object.defineProperty(a._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(a, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(a, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: at
      }), Object.defineProperty(a, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: ot
      }), Object.freeze && (Object.freeze(a.props), Object.freeze(a)), a;
    }
    function x(a, m, R, T, W, A, at, ot) {
      var $ = m.children;
      if ($ !== void 0)
        if (T)
          if (C($)) {
            for (T = 0; T < $.length; T++)
              p($[T]);
            Object.freeze && Object.freeze($);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else p($);
      if (N.call(m, "key")) {
        $ = t(a);
        var z = Object.keys(m).filter(function(Lt) {
          return Lt !== "key";
        });
        T = 0 < z.length ? "{key: someKey, " + z.join(": ..., ") + ": ...}" : "{key: someKey}", V[$ + T] || (z = 0 < z.length ? "{" + z.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          T,
          $,
          z,
          $
        ), V[$ + T] = !0);
      }
      if ($ = null, R !== void 0 && (n(R), $ = "" + R), i(m) && (n(m.key), $ = "" + m.key), "key" in m) {
        R = {};
        for (var it in m)
          it !== "key" && (R[it] = m[it]);
      } else R = m;
      return $ && c(
        R,
        typeof a == "function" ? a.displayName || a.name || "Unknown" : a
      ), g(
        a,
        $,
        A,
        W,
        s(),
        R,
        at,
        ot
      );
    }
    function p(a) {
      typeof a == "object" && a !== null && a.$$typeof === d && a._store && (a._store.validated = 1);
    }
    var l = dt, d = Symbol.for("react.transitional.element"), w = Symbol.for("react.portal"), y = Symbol.for("react.fragment"), b = Symbol.for("react.strict_mode"), E = Symbol.for("react.profiler"), S = Symbol.for("react.consumer"), k = Symbol.for("react.context"), j = Symbol.for("react.forward_ref"), F = Symbol.for("react.suspense"), O = Symbol.for("react.suspense_list"), X = Symbol.for("react.memo"), _ = Symbol.for("react.lazy"), H = Symbol.for("react.activity"), I = Symbol.for("react.client.reference"), B = l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, N = Object.prototype.hasOwnProperty, C = Array.isArray, Y = console.createTask ? console.createTask : function() {
      return null;
    };
    l = {
      "react-stack-bottom-frame": function(a) {
        return a();
      }
    };
    var L, U = {}, J = l["react-stack-bottom-frame"].bind(
      l,
      o
    )(), Z = Y(r(o)), V = {};
    q.Fragment = y, q.jsx = function(a, m, R, T, W) {
      var A = 1e4 > B.recentlyCreatedOwnerStacks++;
      return x(
        a,
        m,
        R,
        !1,
        T,
        W,
        A ? Error("react-stack-top-frame") : J,
        A ? Y(r(a)) : Z
      );
    }, q.jsxs = function(a, m, R, T, W) {
      var A = 1e4 > B.recentlyCreatedOwnerStacks++;
      return x(
        a,
        m,
        R,
        !0,
        T,
        W,
        A ? Error("react-stack-top-frame") : J,
        A ? Y(r(a)) : Z
      );
    };
  }()), q;
}
var xt;
function Xt() {
  return xt || (xt = 1, process.env.NODE_ENV === "production" ? Q.exports = zt() : Q.exports = vt()), Q.exports;
}
var f = Xt();
const Ct = dt.createContext(null), st = () => _t(Ct), wt = {
  fontFamily: "Inter, sans-serif",
  fontSize: "2.5rem",
  lineHeight: 1.4,
  textAlign: "center",
  color: "#FFFFFF",
  textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
  padding: "24px",
  highlightStyle: {
    backgroundColor: "rgba(20, 184, 166, 0.95)",
    scale: 1.1,
    fontWeight: 600,
    textShadow: "2px 2px 4px rgba(0,0,0,0.3)"
  }
}, Ht = ({
  overlay: t
}) => {
  const { playerMetadata: { fps: e } } = st(), r = rt() / e * 1e3, s = t.styles || wt, o = t.captions.find(
    (c) => r >= c.startMs && r <= c.endMs
  );
  if (!o) return null;
  const i = (c) => {
    var u;
    return (u = c == null ? void 0 : c.words) == null ? void 0 : u.map((g, x) => {
      const p = r >= g.startMs && r <= g.endMs, l = p ? Math.min((r - g.startMs) / 300, 1) : 0, d = s.highlightStyle || wt.highlightStyle;
      return /* @__PURE__ */ f.jsx(
        "span",
        {
          className: "inline-block transition-all duration-200",
          style: {
            color: p ? d == null ? void 0 : d.color : s.color,
            backgroundColor: p ? d == null ? void 0 : d.backgroundColor : "transparent",
            opacity: p ? 1 : 0.85,
            transform: p ? `scale(${1 + (d != null && d.scale ? (d.scale - 1) * l : 0.08)})` : "scale(1)",
            fontWeight: p ? (d == null ? void 0 : d.fontWeight) || 600 : s.fontWeight || 400,
            textShadow: p ? d == null ? void 0 : d.textShadow : s.textShadow,
            padding: (d == null ? void 0 : d.padding) || "4px 8px",
            borderRadius: (d == null ? void 0 : d.borderRadius) || "4px",
            margin: "0 2px"
          },
          children: g.word
        },
        `${g.word}-${x}`
      );
    });
  };
  return /* @__PURE__ */ f.jsx(
    "div",
    {
      className: "absolute inset-0 flex items-center justify-center p-4",
      style: {
        ...s,
        width: "100%",
        height: "100%"
      },
      children: /* @__PURE__ */ f.jsx(
        "div",
        {
          className: "leading-relaxed tracking-wide",
          style: {
            whiteSpace: "pre-wrap",
            width: "100%",
            textAlign: "center",
            wordBreak: "break-word",
            display: "flex",
            flexWrap: "wrap",
            justifyContent: "center",
            alignItems: "center",
            gap: "2px"
          },
          children: i(o)
        }
      )
    }
  );
};
function bt() {
  return process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
}
function Ft(t) {
  return t.startsWith("http://") || t.startsWith("https://") ? t : t.startsWith("/") ? `${bt()}${t}` : `${bt()}/${t}`;
}
const Bt = ({
  overlay: t,
  baseUrl: e
}) => {
  var p;
  const n = rt(), { playerMetadata: { fps: r } } = st();
  let s = t.src;
  t.src.startsWith("/") && e ? s = `${e}${t.src}` : t.src.startsWith("/") && (s = Ft(t.src));
  let o = ((p = t.styles) == null ? void 0 : p.volume) ?? 1;
  const i = t.fadeInDuration ?? 0, c = t.fadeOutDuration ?? 0, u = i * r, g = c * r, x = t.durationInFrames;
  if (u > 0 && n < u) {
    const l = h(
      n,
      [0, u],
      [0, 1],
      {
        extrapolateLeft: "clamp",
        extrapolateRight: "clamp",
        easing: K.out(K.cubic)
      }
    );
    o *= l;
  }
  if (g > 0 && n > x - g) {
    const l = h(
      n,
      [x - g, x],
      [1, 0],
      {
        extrapolateLeft: "clamp",
        extrapolateRight: "clamp",
        easing: K.out(K.cubic)
      }
    );
    o *= l;
  }
  return /* @__PURE__ */ f.jsx(
    Pt,
    {
      src: s,
      startFrom: t.startFromSound || 0,
      volume: o,
      playbackRate: t.speed ?? 1
    }
  );
}, St = {
  fade: {
    name: "Fade",
    preview: "Simple fade in/out",
    enter: (t) => ({
      opacity: h(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      opacity: h(t, [e - 15, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  },
  slideRight: {
    name: "Slide",
    preview: "Slide in from left",
    isPro: !0,
    enter: (t) => ({
      transform: `translateX(${h(t, [0, 15], [-100, 0], {
        extrapolateRight: "clamp"
      })}%)`,
      opacity: h(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `translateX(${h(
        t,
        [e - 15, e],
        [0, 100],
        { extrapolateLeft: "clamp" }
      )}%)`,
      opacity: h(t, [e - 15, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  },
  scale: {
    name: "Scale",
    preview: "Scale in/out",
    enter: (t) => ({
      transform: `scale(${h(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      })})`,
      opacity: h(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `scale(${h(
        t,
        [e - 15, e],
        [1, 0],
        { extrapolateLeft: "clamp" }
      )})`,
      opacity: h(t, [e - 15, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  },
  bounce: {
    name: "Bounce",
    preview: "Elastic bounce entrance",
    isPro: !0,
    enter: (t) => ({
      transform: `translateY(${h(
        t,
        [0, 10, 13, 15],
        [100, -10, 5, 0],
        { extrapolateRight: "clamp" }
      )}px)`,
      opacity: h(t, [0, 10], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `translateY(${h(
        t,
        [e - 15, e - 13, e - 10, e],
        [0, 5, -10, 100],
        { extrapolateLeft: "clamp" }
      )}px)`,
      opacity: h(t, [e - 10, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  },
  flipX: {
    name: "Flip",
    preview: "3D flip around X axis",
    isPro: !0,
    enter: (t) => ({
      transform: `perspective(400px) rotateX(${h(
        t,
        [0, 15],
        [90, 0],
        { extrapolateRight: "clamp" }
      )}deg)`,
      opacity: h(t, [0, 5, 15], [0, 0.7, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `perspective(400px) rotateX(${h(
        t,
        [e - 15, e],
        [0, -90],
        { extrapolateLeft: "clamp" }
      )}deg)`,
      opacity: h(
        t,
        [e - 15, e - 5, e],
        [1, 0.7, 0],
        {
          extrapolateLeft: "clamp"
        }
      )
    })
  },
  zoomBlur: {
    name: "Zoom",
    preview: "Zoom with blur effect",
    isPro: !0,
    enter: (t) => ({
      transform: `scale(${h(t, [0, 15], [1.5, 1], {
        extrapolateRight: "clamp"
      })})`,
      opacity: h(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      }),
      filter: `blur(${h(t, [0, 15], [10, 0], {
        extrapolateRight: "clamp"
      })}px)`
    }),
    exit: (t, e) => ({
      transform: `scale(${h(
        t,
        [e - 15, e],
        [1, 1.5],
        { extrapolateLeft: "clamp" }
      )})`,
      opacity: h(t, [e - 15, e], [1, 0], {
        extrapolateLeft: "clamp"
      }),
      filter: `blur(${h(t, [e - 15, e], [0, 10], {
        extrapolateLeft: "clamp"
      })}px)`
    })
  },
  slideUp: {
    name: "Slide",
    preview: "Modern slide from bottom",
    enter: (t) => ({
      transform: `translateY(${h(t, [0, 15], [30, 0], {
        extrapolateRight: "clamp"
      })}px)`,
      opacity: h(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `translateY(${h(
        t,
        [e - 15, e],
        [0, -30],
        { extrapolateLeft: "clamp" }
      )}px)`,
      opacity: h(t, [e - 15, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  },
  snapRotate: {
    name: "Snap",
    preview: "Quick rotate with snap",
    isPro: !0,
    enter: (t) => ({
      transform: `rotate(${h(t, [0, 8, 12, 15], [-10, 5, -2, 0], {
        extrapolateRight: "clamp"
      })}deg) scale(${h(t, [0, 15], [0.8, 1], {
        extrapolateRight: "clamp"
      })})`,
      opacity: h(t, [0, 10], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `rotate(${h(
        t,
        [e - 15, e - 12, e - 8, e],
        [0, -2, 5, -10],
        { extrapolateLeft: "clamp" }
      )}deg) scale(${h(t, [e - 15, e], [1, 0.8], {
        extrapolateLeft: "clamp"
      })})`,
      opacity: h(t, [e - 10, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  },
  glitch: {
    name: "Glitch",
    preview: "Digital glitch effect",
    isPro: !0,
    enter: (t) => {
      const e = h(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      }), n = t % 3 === 0 ? (Math.random() * 10 - 5) * (1 - e) : 0, r = t % 4 === 0 ? (Math.random() * 8 - 4) * (1 - e) : 0;
      return {
        transform: `translate(${n}px, ${r}px) scale(${h(
          t,
          [0, 3, 6, 10, 15],
          [0.9, 1.05, 0.95, 1.02, 1],
          { extrapolateRight: "clamp" }
        )})`,
        opacity: h(t, [0, 3, 5, 15], [0, 0.7, 0.8, 1], {
          extrapolateRight: "clamp"
        })
      };
    },
    exit: (t, e) => {
      const n = h(t, [e - 15, e], [0, 1], {
        extrapolateLeft: "clamp"
      }), r = (e - t) % 3 === 0 ? (Math.random() * 10 - 5) * n : 0, s = (e - t) % 4 === 0 ? (Math.random() * 8 - 4) * n : 0;
      return {
        transform: `translate(${r}px, ${s}px) scale(${h(
          t,
          [e - 15, e - 10, e - 6, e - 3, e],
          [1, 1.02, 0.95, 1.05, 0.9],
          { extrapolateLeft: "clamp" }
        )})`,
        opacity: h(
          t,
          [e - 15, e - 5, e - 3, e],
          [1, 0.8, 0.7, 0],
          {
            extrapolateLeft: "clamp"
          }
        )
      };
    }
  },
  swipeReveal: {
    name: "Swipe",
    preview: "Reveals content with a swipe",
    isPro: !0,
    enter: (t) => ({
      transform: `translateX(${h(t, [0, 15], [0, 0], {
        extrapolateRight: "clamp"
      })}px)`,
      opacity: 1,
      clipPath: `inset(0 ${h(t, [0, 15], [100, 0], {
        extrapolateRight: "clamp"
      })}% 0 0)`
    }),
    exit: (t, e) => ({
      transform: `translateX(${h(
        t,
        [e - 15, e],
        [0, 0],
        { extrapolateLeft: "clamp" }
      )}px)`,
      opacity: 1,
      clipPath: `inset(0 0 0 ${h(
        t,
        [e - 15, e],
        [0, 100],
        { extrapolateLeft: "clamp" }
      )}%)`
    })
  },
  floatIn: {
    name: "Float",
    preview: "Smooth floating entrance",
    enter: (t) => ({
      transform: `translate(${h(t, [0, 15], [10, 0], {
        extrapolateRight: "clamp"
      })}px, ${h(t, [0, 15], [-20, 0], {
        extrapolateRight: "clamp"
      })}px)`,
      opacity: h(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `translate(${h(
        t,
        [e - 15, e],
        [0, -10],
        { extrapolateLeft: "clamp" }
      )}px, ${h(t, [e - 15, e], [0, -20], {
        extrapolateLeft: "clamp"
      })}px)`,
      opacity: h(t, [e - 15, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  }
}, ht = (t) => {
  var c, u;
  const { playerMetadata: { fps: e } } = st(), n = rt(), r = n >= t.durationInFrames - e, s = v(() => !("styles" in t) || !t.styles || !("animation" in t.styles) ? null : t.styles.animation || null, []), o = !r && (s != null && s.enter) ? (c = St[s.enter]) == null ? void 0 : c.enter(
    n,
    t.durationInFrames
  ) : {}, i = r && (s != null && s.exit) ? (u = St[s.exit]) == null ? void 0 : u.exit(
    n,
    t.durationInFrames
  ) : {};
  return r ? i : o;
};
function Ut(t) {
  const e = rt(), { playerMetadata: { fps: n } } = st();
  return v(() => {
    const r = t.fadeInDuration ? Math.round(t.fadeInDuration * n) : 0, s = t.fadeOutDuration ? Math.round(t.fadeOutDuration * n) : 0;
    let o = 1, i = 1;
    r > 0 && e < r && (o = e / r), s > 0 && e >= t.durationInFrames - s && (i = (t.durationInFrames - e) / s);
    let c = o * i;
    return c = Math.max(0, Math.min(1, c)), c;
  }, [e, n, t.fadeInDuration, t.fadeOutDuration]);
}
const Vt = ({ overlay: t }) => {
  var s;
  const e = ht(t), n = {
    width: "100%",
    height: "100%",
    objectPosition: t.styles.objectPosition,
    opacity: t.styles.opacity,
    transform: t.styles.transform || "none",
    filter: t.styles.filter || "none",
    borderRadius: t.styles.borderRadius || "0px",
    boxShadow: t.styles.boxShadow || "none",
    border: t.styles.border || "none",
    ...e
  }, r = {
    width: "100%",
    height: "100%",
    padding: t.styles.padding || "0px",
    backgroundColor: t.styles.paddingBackgroundColor || "transparent",
    display: "flex",
    // Use flexbox for centering
    alignItems: "center",
    justifyContent: "center"
  };
  return /* @__PURE__ */ f.jsx(
    "div",
    {
      style: {
        width: "100%",
        height: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        borderRadius: "4px",
        opacity: ((s = t.styles) == null ? void 0 : s.opacity) || 1,
        transform: `rotate(${t.rotation || 0}deg)`,
        ...r
      },
      children: /* @__PURE__ */ f.jsx(
        "img",
        {
          src: t.localSrc || t.src,
          style: {
            width: "100%",
            height: "100%",
            objectFit: "contain",
            ...n
          },
          alt: "贴纸"
        }
      )
    }
  );
}, D = class D {
  constructor() {
    P(this, "cache", /* @__PURE__ */ new Map());
  }
  static getInstance() {
    return D.instance || (D.instance = new D()), D.instance;
  }
  getCacheKey(e, n, r, s) {
    return s !== void 0 ? `${e}:${n}:${r}:${s}` : `${e}:${n}:${r}`;
  }
  getCachedWidth(e, n, r, s) {
    const o = this.getCacheKey(e, n, r, s);
    return this.cache.get(o) || null;
  }
  setCachedWidth(e, n, r, s, o) {
    const i = this.getCacheKey(e, n, r, o);
    this.cache.set(i, s);
  }
};
P(D, "instance", null);
let lt = D;
class Gt {
  constructor(e, n, r, s = 0) {
    P(this, "fontPath");
    P(this, "fontSize");
    P(this, "containerWidth");
    P(this, "letterSpacing");
    this.fontPath = e, this.fontSize = n, this.containerWidth = r, this.letterSpacing = s;
  }
  /**
   * 获取文本片段的宽度（包含字间距）
   */
  getTextWidth(e) {
    if (!e) return 0;
    try {
      const n = this.fontPath.getAdvanceWidth(e, this.fontSize), r = Math.max(0, e.length - 1) * this.letterSpacing;
      return n + r;
    } catch (n) {
      console.warn("[贪心换行] 获取文本宽度失败:", e, n);
      const r = e.length * this.fontSize * 0.6, s = Math.max(0, e.length - 1) * this.letterSpacing;
      return r + s;
    }
  }
  /**
   * 贪心算法：考虑单词边界
   * @param content 要分割的文本内容
   * @param respectWordBoundary 是否尊重单词边界（对中文无效）
   * @returns 分割后的字符串数组
   */
  wrapText(e, n = !1) {
    if (!e || !this.fontPath)
      return [e || ""];
    const r = [], s = e.split(`
`);
    for (const o of s) {
      if (!o.trim()) {
        r.push("");
        continue;
      }
      n && /^[a-zA-Z\s]+$/.test(o) ? this.wrapEnglishText(o, r) : this.wrapChineseText(o, r);
    }
    return r.length > 0 ? r : [""];
  }
  /**
   * 处理英文文本换行（按单词）
   */
  wrapEnglishText(e, n) {
    const r = e.split(/(\s+)/);
    let s = "", o = 0;
    for (const i of r) {
      const c = this.getTextWidth(i);
      o + c > this.containerWidth && s.trim().length > 0 ? (n.push(s.trimEnd()), s = i, o = c) : (s += i, o += c);
    }
    s.trim().length > 0 && n.push(s.trimEnd());
  }
  /**
   * 处理中文文本换行（按字符）
   */
  wrapChineseText(e, n) {
    let r = "", s = 0;
    for (let o = 0; o < e.length; o++) {
      const i = e[o], c = this.fontPath.getAdvanceWidth(i, this.fontSize), u = r.length === 0 ? c : c + this.letterSpacing;
      s + u > this.containerWidth && r.length > 0 ? (n.push(r), r = i, s = c) : (r += i, s += u);
    }
    r.length > 0 && n.push(r);
  }
  /**
   * 获取换行后的总高度
   * @param lines 文本行数组
   * @param lineHeight 自定义行高，如果不提供则使用默认值
   */
  // public getWrappedTextHeight(lines: string[], lineHeight?: number): number {
  //   const actualLineHeight = lineHeight || this.fontSize * 1.2
  //   return lines.length * actualLineHeight
  // }
  /**
   * 获取每行的详细信息
   * @param lines 文本行数组
   * @param lineHeight 自定义行高，如果不提供则使用默认值
   */
  // public getLineDetails(lines: string[], lineHeight?: number): Array<{ line: string, width: number, height: number }> {
  //   const actualLineHeight = lineHeight || this.fontSize * 1.2
  //
  //   return lines.map(line => ({
  //     line,
  //     width: this.getTextWidth(line),
  //     height: actualLineHeight
  //   }))
  // }
  /**
   * 获取换行后的总高度（包含行间距）
   * @param lines 文本行数组
   * @param lineHeight 行高，如果不提供则使用默认值
   * @param lineSpacingRatio 行间距倍数，默认为0
   */
  getWrappedTextHeightWithSpacing(e, n, r = 0) {
    const s = n || this.fontSize * 1.2, o = e.length, i = r * this.fontSize;
    return o * s + Math.max(0, o - 1) * i;
  }
  /**
   * 获取每行的详细信息（包含行间距）
   * @param lines 文本行数组
   * @param lineHeight 行高，如果不提供则使用默认值
   * @param lineSpacingRatio 行间距倍数，默认为0
   */
  getLineDetailsWithSpacing(e, n, r = 0) {
    const s = n || this.fontSize * 1.2, o = r * this.fontSize;
    return e.map((i, c) => ({
      line: i,
      width: this.getTextWidth(i),
      height: s,
      y: c * (s + o)
      // 每行的Y坐标包含行间距
    }));
  }
}
const Rt = lt.getInstance(), qt = 1, yt = (t, e, n, r, s = 0) => {
  if (!n)
    return 1;
  const o = Rt.getCachedWidth(e, n, r, s);
  if (o !== null)
    return o;
  try {
    const i = t.getAdvanceWidth(n, r), c = Math.max(0, n.length - 1) * s, u = i + c;
    return Rt.setCachedWidth(e, n, r, u, s), u;
  } catch (i) {
    return console.error("[增强文本渲染器] 计算文本宽度失败:", i), 1;
  }
};
function Jt(t, e, n, r, s, o = 0, i = !1) {
  try {
    return new Gt(t, s, r, o).wrapText(n, i).map((g) => ({
      content: g,
      width: yt(t, e, g, s, o)
    }));
  } catch (c) {
    return console.error("[增强文本渲染器] 优化贪心换行失败:", c), [{
      content: n,
      width: yt(t, e, n, s, o)
    }];
  }
}
function Zt(t, e, n) {
  const {
    width: r = e.width,
    content: s = e.content,
    fontSize: o = e.styles.fontSize ?? 0,
    lineSpacing: i = e.styles.lineSpacing ?? 0,
    letterSpacing: c = e.styles.letterSpacing ?? 0
  } = {}, u = o * qt, g = i * o, x = Jt(
    t,
    e.src,
    s,
    r,
    o,
    c,
    !0
  ), p = x.length, l = p * u + Math.max(0, p - 1) * g, d = Math.max(...x.map((w) => w.width));
  return {
    wrappedLines: x,
    lineHeight: u,
    totalHeight: l,
    maxLineWidth: d
  };
}
function Kt(t, e, n) {
  const {
    width: r = e.width,
    fontSize: s = e.styles.fontSize,
    lineSpacing: o = e.styles.lineSpacing ?? 0,
    letterSpacing: i = e.styles.letterSpacing ?? 0
  } = n || {}, c = r / 2, u = e.height / 2, g = e.styles.textAlign, x = o * s, { totalHeight: p, lineHeight: l, wrappedLines: d, maxLineWidth: w } = Zt(
    t,
    e
  ), y = () => {
    switch (g) {
      case "left":
        return 0;
      case "right":
        return r;
      case "center":
      default:
        return c;
    }
  }, b = 1, E = y(), S = u - p / 2 + l / 2;
  return {
    x: E,
    y: S,
    scale: b,
    fontSize: s,
    letterSpacing: i,
    wrappedLines: d,
    lineHeight: l,
    totalHeight: p,
    minHeight: p,
    minWidth: w,
    lineSpacing: x
  };
}
function Qt(t) {
  const e = t.match(/left|center|right/gi) || [], n = e.length === 0 ? "left" : e[0], r = t.match(/baseline|top|bottom|middle/gi) || [], s = r.length === 0 ? "baseline" : r[0];
  return { horizontal: n, vertical: s };
}
function te(t, e) {
  return t.charToGlyph(e).name !== ".notdef";
}
class ee {
  constructor(e, n) {
    this.font = e, this.fallbackFont = n;
  }
  toVector(e, n = {}) {
    const r = Object.keys(n.attributes || {}).map((u) => `${u}="${n.attributes[u]}"`).join(" "), { d: s, height: o, cursorX: i } = this.getD(e, n);
    return {
      path: r ? `<path ${r} d="${s}"/>` : `<path d="${s}"/>`,
      width: i,
      height: o
    };
  }
  _getHeight(e) {
    const n = 1 / this.font.unitsPerEm * e;
    return (this.font.ascender - this.font.descender) * n;
  }
  _getWidth(e, n) {
    return this.font.getAdvanceWidth(e, n);
  }
  getD(e, n = {}) {
    const r = n.fontSize || 72, s = "kerning" in n ? n.kerning : !0, o = n.letterSpacing || 0, i = n.tracking || 0;
    let c = n.x || 0;
    const u = n.y || 0, g = Qt(n.anchor || ""), x = this._getHeight(r), p = this.font.ascender * (r / this.font.unitsPerEm);
    let l = u;
    switch (g.vertical) {
      case "baseline":
        l = u;
        break;
      case "top":
        l = u + p;
        break;
      case "middle":
        l = u + p - x / 2;
        break;
      case "bottom":
        l = u + p - x;
        break;
    }
    const d = [];
    let w = null, y = null;
    for (let b = 0; b < e.length; b++) {
      const E = e[b], S = te(this.font, E) ? this.font : this.fallbackFont, k = 1 / S.unitsPerEm * r, j = S.charToGlyph(E);
      if (s && w && y === S) {
        const O = S.getKerningValue(w, j);
        c += O * k;
      }
      const F = S.getPath(E, c, l, r, {
        kerning: !1,
        tracking: i
      });
      if (d.push(F), j.advanceWidth) {
        const O = j.advanceWidth * k;
        c += O;
      }
      o ? c += o * r : i && (c += i / 1e3 * r), w = j, y = S;
    }
    return {
      d: d.map((b) => b.toPathData(4)).join(" "),
      height: x,
      cursorX: c
    };
  }
}
const ft = 0, ne = ({ overlay: t, font: e, renderInfo: n }) => {
  const { styles: r } = t, s = !!(r.strokeEnabled && t.styles.strokeWidth && t.styles.strokeColor), o = !!(r.shadowEnabled && t.styles.shadowDistance && t.styles.shadowColor);
  if (!s && !o)
    return null;
  const i = $t(null);
  if (t.width === 0)
    return null;
  const c = ct(
    () => {
      const l = [];
      if (o) {
        const d = t.styles.shadowDistance ? t.styles.shadowDistance / 100 * t.styles.fontSize : 0, w = t.styles.shadowAngle ?? 0, y = t.styles.shadowBlur ?? 0, b = t.styles.shadowColor || "#000000", E = t.styles.shadowOpacity ?? 0;
        if (d > 0) {
          const S = w * Math.PI / 180, k = Math.cos(S) * d, j = Math.sin(S) * d;
          l.push(
            /* @__PURE__ */ f.jsx(
              "filter",
              {
                id: `shadow-${t.id}`,
                x: "-100%",
                y: "-100%",
                width: "300%",
                height: "300%",
                filterUnits: "userSpaceOnUse",
                colorInterpolationFilters: "sRGB",
                children: /* @__PURE__ */ f.jsx(
                  "feDropShadow",
                  {
                    dx: k,
                    dy: j,
                    stdDeviation: y,
                    floodColor: b,
                    floodOpacity: E
                  }
                )
              },
              "shadow"
            )
          );
        }
      }
      return l;
    },
    [o, t.styles, t.id]
  ), u = ct(
    () => {
      try {
        const l = (t.styles.strokeWidth ?? 0) / 100 * t.styles.fontSize * 0.3, d = t.styles.strokeColor || "#000000", w = o ? `shadow-${t.id}` : void 0, y = new ee(e, e), b = [], {
          x: E,
          y: S,
          fontSize: k,
          wrappedLines: j,
          lineHeight: F,
          lineSpacing: O
        } = n;
        return j.forEach((X, _) => {
          if (!X.content.trim()) return;
          const { d: H } = y.getD(X.content, {
            x: 0,
            y: 0,
            fontSize: k,
            anchor: "left middle",
            letterSpacing: (t.styles.letterSpacing ?? 0) / k
            // 转换为相对值
          });
          if (!H) return;
          const I = S + _ * (F + O), B = r.textAlign || "center", N = X.width;
          let C;
          switch (B) {
            case "left":
              C = E;
              break;
            case "right":
              C = E - N;
              break;
            case "center":
            default:
              C = E - N / 2;
              break;
          }
          let Y = `translate(${C}, ${I})`;
          if (r.fontStyle === "italic" && (Y += " skewX(-12)"), b.push(
            /* @__PURE__ */ f.jsx(
              "path",
              {
                d: H,
                fill: "none",
                stroke: s ? d : "none",
                strokeWidth: l,
                strokeLinejoin: "round",
                strokeLinecap: "round",
                filter: w ? `url(#${w})` : void 0,
                transform: Y,
                style: {
                  vectorEffect: "non-scaling-stroke"
                }
              },
              `line-${_}`
            )
          ), r.fontWeight === "bold") {
            const L = Math.max(0.5, k * 0.01);
            [
              `translate(${C + L}, ${I})`,
              `translate(${C}, ${I + L})`,
              `translate(${C + L}, ${I + L})`
            ].forEach((J, Z) => {
              let V = J;
              r.fontStyle === "italic" && (V += " skewX(-12)"), b.push(
                /* @__PURE__ */ f.jsx(
                  "path",
                  {
                    d: H,
                    fill: "none",
                    stroke: s ? d : "none",
                    strokeWidth: l,
                    strokeLinejoin: "round",
                    strokeLinecap: "round",
                    filter: w ? `url(#${w})` : void 0,
                    transform: V,
                    style: {
                      vectorEffect: "non-scaling-stroke"
                    }
                  },
                  `line-bold-${_}-${Z}`
                )
              );
            });
          }
          if (r.underlineEnabled) {
            const L = I + k * 0.5 + k * 0.1, U = Math.max(1, k * 0.05);
            b.push(
              /* @__PURE__ */ f.jsx(
                "line",
                {
                  x1: C,
                  y1: L,
                  x2: C + N,
                  y2: L,
                  stroke: s ? d : t.styles.color || "#ffffff",
                  strokeWidth: U,
                  strokeLinecap: "round",
                  filter: w ? `url(#${w})` : void 0
                },
                `underline-${_}`
              )
            );
          }
        }), /* @__PURE__ */ f.jsx("g", { children: b });
      } catch (l) {
        return console.error("[增强文本渲染器] SVG路径渲染失败:", l), null;
      }
    },
    [t, s, o, r, n]
  ), g = c(), x = u(), p = Math.max(t.height, n.totalHeight + ft);
  return /* @__PURE__ */ f.jsxs(
    "svg",
    {
      ref: i,
      style: {
        position: "absolute",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        zIndex: 1,
        pointerEvents: "none"
      },
      viewBox: `0 0 ${t.width} ${p}`,
      preserveAspectRatio: "xMidYMid meet",
      children: [
        /* @__PURE__ */ f.jsx("defs", { children: g }),
        x
      ]
    }
  );
}, re = ({ overlay: t, renderInfo: e }) => {
  const n = $t(null), r = ct((s, o, i, c, u, g, x) => {
    let p = i;
    if (g === "center" ? p = i - x / 2 : g === "right" && (p = i - x), u === 0) {
      s.fillText(o, p, c);
      return;
    }
    let l = p;
    for (let d = 0; d < o.length; d++) {
      const w = o[d];
      s.fillText(w, l, c);
      const y = s.measureText(w).width;
      l += y, d < o.length - 1 && (l += u);
    }
  }, []);
  return nt(
    () => {
      if (!n.current || t.width === 0)
        return;
      const { styles: s } = t, o = n.current;
      if (!o) return;
      const i = o.getContext("2d");
      if (!i) return;
      const {
        x: c,
        y: u,
        totalHeight: g,
        wrappedLines: x,
        lineHeight: p,
        letterSpacing: l,
        lineSpacing: d
      } = e, w = Math.max(t.height, g + ft);
      o.width = t.width * window.devicePixelRatio, o.height = w * window.devicePixelRatio, o.style.width = `${t.width}px`, o.style.height = `${w}px`, i.scale(window.devicePixelRatio, window.devicePixelRatio), i.clearRect(0, 0, t.width, w);
      const y = s.fontFamily || "Arial";
      i.font = `${t.styles.fontSize}px "${t.styles.fontFamily}", ${y}, sans-serif`, i.fillStyle = t.styles.color || "#ffffff", i.textBaseline = "middle", x.forEach((b, E) => {
        const S = u + E * (p + d);
        i.save();
        let k = 0;
        s.fontStyle === "italic" && (k = S * -0.2, i.transform(1, 0, -0.2, 1, 0, 0));
        const j = c - k, F = s.textAlign || "center";
        if (r(
          i,
          b.content,
          j,
          S,
          l,
          F,
          b.width
        ), s.fontWeight === "bold") {
          const O = Math.max(0.5, t.styles.fontSize * 0.01);
          r(
            i,
            b.content,
            j + O,
            S,
            l,
            F,
            b.width
          ), r(
            i,
            b.content,
            j,
            S + O,
            l,
            F,
            b.width
          ), r(
            i,
            b.content,
            j + O,
            S + O,
            l,
            F,
            b.width
          );
        }
        i.restore();
      });
    },
    [t, e, r]
  ), /* @__PURE__ */ f.jsx(
    "canvas",
    {
      ref: n,
      style: {
        position: "absolute",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        zIndex: 2,
        pointerEvents: "none"
      }
    }
  );
}, se = ({ font: t, overlay: e, containerStyle: n }) => {
  const { styles: r } = e, s = !!r.backgroundImage, o = s && !!e.styles.bubbleTextRect, i = v(
    () => Kt(t, e),
    [t, e]
  ), c = {
    ...n,
    backgroundImage: s ? `url(${e.styles.backgroundImage})` : void 0,
    backgroundSize: "contain",
    backgroundRepeat: "no-repeat",
    backgroundPosition: "center"
  }, u = v(() => s ? {
    position: "absolute",
    left: "50%",
    top: "50%",
    transform: "translate(-50%, -50%)",
    width: "100%",
    height: "60%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    boxSizing: "border-box"
  } : {}, [s]), g = () => /* @__PURE__ */ f.jsxs(
    "div",
    {
      style: {
        position: "relative",
        width: "100%",
        height: e.height,
        backgroundColor: r.backgroundColor,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        opacity: e.styles.textOpacity ?? 1
      },
      children: [
        /* @__PURE__ */ f.jsx(ne, { overlay: e, renderInfo: i, font: t }),
        /* @__PURE__ */ f.jsx(re, { overlay: e, renderInfo: i })
      ]
    }
  ), x = () => o ? /* @__PURE__ */ f.jsx("div", { style: u, children: g() }) : g(), p = Math.max(e.height, i.totalHeight + ft);
  return /* @__PURE__ */ f.jsx(
    "div",
    {
      style: {
        ...c,
        position: "relative",
        overflow: "hidden",
        height: `${p}px`
      },
      children: x()
    }
  );
}, ae = ({
  overlay: t,
  containerStyle: e = { width: "100%", height: "100%" }
}) => {
  const [n] = ut(() => jt()), [r, s] = ut(null), o = async () => {
    const i = t.src, c = t.styles.fontFamily;
    if (!i)
      return null;
    const u = await Yt.load(i);
    try {
      const x = ((l) => {
        if (l.startsWith("http://") || l.startsWith("https://"))
          return l;
        const d = l.replace(/\\/g, "/");
        return d.startsWith("/") ? `file://${d}` : `file:///${d}`;
      })(i);
      if (!Array.from(document.fonts).find(
        (l) => l.family === c
      )) {
        const l = new FontFace(c, `url("${x}")`);
        await l.load(), document.fonts.add(l), console.log(`[CLOUD] 字体已加载到 DOM: ${c}`);
      }
    } catch (g) {
      console.warn("[CLOUD] DOM 字体加载失败，但 opentype.js 加载成功:", g);
    }
    s({
      font: u,
      fontFamily: c
    });
  };
  return nt(() => {
    r && et(n);
  }, [r]), nt(() => {
    o();
  }, [t.src, t.styles.fontFamily]), r ? /* @__PURE__ */ f.jsx(
    se,
    {
      font: r.font,
      overlay: t,
      containerStyle: e
    }
  ) : null;
}, oe = dt.memo(
  ae,
  (t, e) => {
    const n = t.overlay.id === e.overlay.id && t.overlay.content === e.overlay.content && t.overlay.src === e.overlay.src && t.overlay.width === e.overlay.width && t.overlay.left === e.overlay.left && t.overlay.top === e.overlay.top && t.overlay.rotation === e.overlay.rotation, r = JSON.stringify(t.overlay.styles) === JSON.stringify(e.overlay.styles), s = JSON.stringify(t.containerStyle) === JSON.stringify(e.containerStyle);
    return n && r && s;
  }
), ie = ({ overlay: t }) => {
  const e = ht(t), n = {
    width: "100%",
    height: "100%",
    display: "flex",
    alignItems: "center",
    textAlign: t.styles.textAlign,
    justifyContent: t.styles.textAlign === "center" ? "center" : t.styles.textAlign === "right" ? "flex-end" : "flex-start",
    overflow: "hidden",
    boxSizing: "border-box",
    ...e
  };
  return /* @__PURE__ */ f.jsx(
    oe,
    {
      overlay: t,
      containerStyle: n
    }
  );
};
function ce(t, e) {
  let n = t.localSrc || t.src;
  return t.src.startsWith("/") && e ? n = `${e}${t.src}` : t.src.startsWith("/") && (n = Ft(t.src)), n;
}
const le = ({
  overlay: t,
  baseUrl: e
}) => {
  const n = ht(t), r = Ut(t), s = ce(t, e);
  nt(() => {
    const p = jt("Loading video"), l = document.createElement("video");
    l.src = s;
    const d = () => {
      et(p);
    }, w = (y) => {
      et(p);
    };
    return l.addEventListener("loadedmetadata", d), l.addEventListener("error", w), () => {
      l.removeEventListener("loadedmetadata", d), l.removeEventListener("error", w), et(p);
    };
  }, []);
  const o = v(() => {
    if (!t.cropData) return {};
    const { x: p, y: l, width: d, height: w } = t.cropData, y = 100 / d * 100, b = 100 / w * 100, E = -p, S = -l;
    return {
      width: `${y}%`,
      height: `${b}%`,
      transform: `translate(${E}%, ${S}%)`,
      transformOrigin: "0 0"
    };
  }, [t.cropData]), i = {
    width: "100%",
    height: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: t.styles.paddingBackgroundColor || "transparent",
    opacity: (t.styles.opacity ?? 1) * r
  }, c = t.styles.transform || "none", u = t.styles.padding || 0, g = {
    position: "relative",
    width: `${100 - u}%`,
    height: `${100 - u}%`,
    overflow: "hidden",
    transform: c
  }, x = {
    width: o.width,
    height: o.height,
    maxWidth: "unset",
    objectFit: t.styles.objectFit || "cover",
    position: "absolute",
    left: 0,
    top: 0,
    transform: o.transform,
    transformOrigin: o.transformOrigin || "center",
    borderRadius: t.styles.borderRadius || "0px",
    filter: t.styles.filter || "none",
    boxShadow: t.styles.boxShadow || "none",
    border: t.styles.border || "none",
    ...n,
    transition: "opacity 0.1s ease-in-out"
  };
  return /* @__PURE__ */ f.jsx("div", { style: i, className: "video-layer-content-container", children: /* @__PURE__ */ f.jsx("div", { style: g, children: /* @__PURE__ */ f.jsx(
    Mt,
    {
      src: s,
      startFrom: t.trimStartFrames,
      style: x,
      className: "video-layer-content-video",
      volume: t.styles.volume ?? 1,
      playbackRate: t.speed ?? 1
    }
  ) }) });
};
var M = /* @__PURE__ */ ((t) => (t.STORYBOARD = "storyboard", t.TEXT = "text", t.VIDEO = "video", t.SOUND = "sound", t.STICKER = "sticker", t.CAPTION = "caption", t))(M || {});
const tt = {
  width: "100%",
  height: "100%"
}, Et = ({
  overlay: t,
  baseUrl: e
}) => {
  switch (t.type) {
    case M.VIDEO:
      return /* @__PURE__ */ f.jsx("div", { style: { ...tt }, children: /* @__PURE__ */ f.jsx(le, { overlay: t, baseUrl: e }) });
    case M.TEXT:
      return /* @__PURE__ */ f.jsx("div", { style: { ...tt }, children: /* @__PURE__ */ f.jsx(ie, { overlay: t }) });
    case M.CAPTION:
      return /* @__PURE__ */ f.jsx(
        "div",
        {
          style: {
            ...tt,
            position: "relative",
            overflow: "hidden",
            display: "flex"
          },
          children: /* @__PURE__ */ f.jsx(Ht, { overlay: t })
        }
      );
    case M.STICKER:
      return /* @__PURE__ */ f.jsx("div", { style: { ...tt }, className: "layer-content-wrapper", children: /* @__PURE__ */ f.jsx(Vt, { overlay: t }) });
    case M.SOUND:
      return /* @__PURE__ */ f.jsx(Bt, { overlay: t, baseUrl: e });
    default:
      return null;
  }
}, de = ({ overlay: t, baseUrl: e }) => {
  if (t.type === M.STORYBOARD) return null;
  const n = v(() => ({
    position: "absolute",
    left: t.left,
    top: t.top,
    width: t.width,
    height: t.height,
    transform: `rotate(${t.rotation || 0}deg)`,
    transformOrigin: "center center",
    zIndex: t.zIndex
  }), [t]);
  return t.type === "sound" ? /* @__PURE__ */ f.jsx(
    pt,
    {
      from: t.from,
      durationInFrames: t.durationInFrames,
      children: /* @__PURE__ */ f.jsx(Et, { overlay: t, baseUrl: e })
    },
    t.id
  ) : /* @__PURE__ */ f.jsx(It, { children: /* @__PURE__ */ f.jsx(
    Ot,
    {
      style: {
        overflow: "hidden",
        maxWidth: "3000px"
      },
      children: /* @__PURE__ */ f.jsx(
        pt,
        {
          from: t.from,
          durationInFrames: t.durationInFrames,
          layout: "none",
          children: /* @__PURE__ */ f.jsx("div", { style: n, children: /* @__PURE__ */ f.jsx(Et, { overlay: t, baseUrl: e }) })
        },
        t.id
      )
    }
  ) });
}, he = ({
  overlays: t,
  baseUrl: e,
  playerMetadata: n
}) => {
  if (!n)
    throw new Error("Unable to load player metadata");
  return /* @__PURE__ */ f.jsx(
    Ct.Provider,
    {
      value: {
        overlays: t,
        baseUrl: e,
        playerMetadata: n
      },
      children: /* @__PURE__ */ f.jsx(Ot, { style: { backgroundColor: "#000000" }, children: t.map((r) => /* @__PURE__ */ f.jsx(
        de,
        {
          overlay: r,
          baseUrl: e
        },
        r.id
      )) })
    }
  );
}, kt = 0, Tt = 30, fe = "V0-0-2", ue = () => {
  const t = {
    overlays: [],
    durationInFrames: kt,
    fps: Tt,
    width: 1920,
    height: 1920,
    src: "",
    setSelectedOverlayId: () => {
    },
    selectedOverlayId: null,
    changeOverlay: () => {
    }
  };
  return /* @__PURE__ */ f.jsx(f.Fragment, { children: /* @__PURE__ */ f.jsx(
    Nt,
    {
      id: fe,
      component: he,
      durationInFrames: kt,
      fps: Tt,
      width: 1920,
      height: 1920,
      calculateMetadata: async ({ props: e }) => {
        const { durationInFrames: n, width: r, height: s } = e.playerMetadata;
        return {
          durationInFrames: n,
          width: r,
          height: s
        };
      },
      defaultProps: t
    }
  ) });
};
Dt(ue);
