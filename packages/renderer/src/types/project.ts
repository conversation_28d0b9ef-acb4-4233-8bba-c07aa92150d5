export type Project = {
  createTime: number
  description: string
  extraMeta: null
  id: number
  ownerUserId: null
  projectCode: null
  projectName: string
  sortOrder: number
  status: number
}

export type Script = {
  id: number
  content: string
  contentPoint: string
  copyFrom: number
  coverContent: string
  coverType: number
  createTime: number
  editorDataInit: number
  extInfo: string
  formulaTitle: string
  fullContent: string
  generateStatus: number
  historyVersion: number
  isMultiCaptions: number
  lastModifier: null
  mixedVideoCount: number
  otherInfo: string
  projectId: number
  readonly: number
  referenceVideo: string
  referenceVideos: string
  sceneRequire: string
  title: string
  version: number
  videoDirection: number
}

export type Work = {
  auditStatus: number
  cid: number
  commentCount: number
  composeTaskNo: string
  composeReason: string
  composeStatus: number
  composeSubStatus: number
  cover: string
  createTime: number
  digitalPersonModelId: number
  distList: string
  downloadCount: number
  duration: number
  url: string
  id: number
  name: string
  objectOid: string
  previewFile: string
  product: number
  projectId: number
  repetitionRate: number
  scriptId: number
  size: number
  vid: number
}

export const shotTypes = [
  { value: 'long', label: '远景' },
  { value: 'medium', label: '中景' },
  { value: 'close', label: '近景' },
  { value: 'closeup', label: '特写' },
  { value: 'full', label: '全景' },
]