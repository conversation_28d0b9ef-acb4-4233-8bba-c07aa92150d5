import { requestCurrying } from '../request'

export type Team = {
  contactMobile?: string
  contactName: string
  contactUserId: number
  id: number
  name: string
  status: number
}

export type Role = {
  code: string
  createTime: number
  dataScope: number
  dataScopeDeptIds: string
  id: number
  name: string
  remark: string
  sort: number
  status: number
  type: number
}

export type Member = {
  avatar: string
  createTime: number
  id: number
  isComment: boolean
  isEditVideo: boolean
  isCreator: boolean
  isPrivateMsg: boolean
  isPublishVideo: boolean
  isTiktokComment: boolean
  mobile: string
  nickname: string
  roles: Role[] | null
  remark?: string
}

export type TeamPermission = Pick<
  Member,
  'isEditVideo' | 'isPublishVideo' | 'isComment' | 'isPrivateMsg' | 'isTiktokComment'
>

type InviteParams = {
  roleIds: number[]
  projectIds: number[]
}

type SetMemberPermissionParams = {
  memberId: number
  type: keyof TeamPermission
  status: boolean
}

export const TeamAPI = {
  list: requestCurrying.get<object, Team[]>('/app-api/creative/team/list'),
  current: requestCurrying.get<object, Team>('/app-api/creative/team/current'),
  check: requestCurrying.post<{ teamId: number }, boolean>('/app-api/creative/team/change'),
  create: requestCurrying.post<{ name: string }, number>('/app-api/creative/team/create'),
  leave: requestCurrying.post<{ teamId: number, userId?: number }>('/app-api/creative/team/leave'),
  transfer: requestCurrying.post('/app-api/creative/team/transfer-ownership'),
  disband: requestCurrying.post<{ teamId: number }, null>('/app-api/creative/team/disband'),
  invite: requestCurrying.post<InviteParams, string>('/app-api/creative/team-invite/create-code'),
  join: requestCurrying.post<{ inviteCode: string }, number>('/app-api/creative/team-member/join'),
  roles: requestCurrying.get<object, Role[]>('/app-api/creative/team-role/list'),
  // 修改团队头像和名称
  update: requestCurrying.post<{ teamId: number, name?: string, avatar?: string }, boolean>('/app-api/creative/team/update'),
  // 成员相关
  members: requestCurrying.get<{ keyword?: string }, Member[]>('/app-api/creative/team-member/list'),
  setMemberPermission: requestCurrying.post<SetMemberPermissionParams, null>(
    '/app-api/creative/team-member/change-permission',
  ),
  member: async ({ memberId }: { memberId: number }) => {
    // HACK: 暂时使用列表查询来获取单个成员信息
    const members = await TeamAPI.members({})
    return members.find(member => member.id === memberId)
  },
  updateMemberRemark: requestCurrying.post<{ memberId: number; remark: string }>('/app-api/creative/team-member/update-remark'),
  removeMember: requestCurrying.post<{ teamId: number, userId: number[] }>('/app-api/creative/team-member/delete'),
  // 修改成员角色和项目权限
  updateMember: requestCurrying.post<{ projectIds: number[], roleId: number, userId: number }>('/app-api/creative/team-member/update'),
}
