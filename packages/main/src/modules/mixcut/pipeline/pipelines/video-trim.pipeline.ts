import { SimpleMixcutPipeline } from '../mixcut-pipeline.types.js'
import { VideoOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut.js'

/**
* 随机截断视频开头和结尾的部分帧数
*/
export class VideoTrimPipeline extends SimpleMixcutPipeline<typeof MIXCUT_PIPELINES.video.trim, VideoOverlay> {

  filter(overlay: MixcutableOverlay): overlay is VideoOverlay & MixcutableOverlay {
    return SimpleMixcutPipeline.filters.videoOverlay(overlay)
  }

  async processSingleOverlay(overlay: VideoOverlay & MixcutableOverlay) {
    const { allowTrimStart, rangeMin = 0, rangeMax = 15 } = this.context.params

    const { originalDurationFrames, trimStartFrames = 0, trimEndFrames = 0, durationInFrames } = overlay

    if (trimEndFrames > 0 && trimStartFrames > 0) return overlay

    const additionalTrimEnd = trimEndFrames
      ? 0
      : _.random(
        rangeMin,
        Math.min((originalDurationFrames - trimStartFrames) / 3, rangeMax)
      )

    const additionalTrimStart = !allowTrimStart || trimStartFrames
      ? 0
      : _.random(
        rangeMax,
        Math.min((originalDurationFrames - trimEndFrames) / 3, rangeMax) - additionalTrimEnd
      )

    const newTrimStartFrames = trimStartFrames + additionalTrimStart
    const newTrimEndFrames = trimEndFrames + additionalTrimEnd

    const indeedPlayedFrames = originalDurationFrames - newTrimEndFrames - newTrimStartFrames

    // 更改变速, 以保证去除片尾后, 播放时长不变
    const newSpeed = indeedPlayedFrames / durationInFrames
    // console.log(`trimStart ${trimStartFrames} => ${newTrimStartFrames}; trimEnd ${trimEndFrames} => ${newTrimEndFrames}; speed ${speed} => ${newSpeed}`)

    return {
      ...overlay,
      trimStartFrames: newTrimStartFrames,
      trimEndFrames: newTrimEndFrames,
      speed: newSpeed
      // durationInFrames: durationInFrames - additionalTrimEnd / speed
    }
  }
}
