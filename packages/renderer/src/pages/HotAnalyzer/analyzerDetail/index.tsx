import React, { useState, useRef, useEffect } from 'react'
import { <PERSON>, Ellipsis } from 'lucide-react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify'
import { Card, CardContent } from '@/components/ui/card'
import { WithTooltip } from '@/components/WithTooltip'
import { Button } from '@/components/ui/button'
import { useDeleteModal } from '@/components/modal/delete'
import { cn, formatTimestamp } from '@/components/lib/utils'
import { formatDuration } from '@/components/material/MediaItem'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useQueryAnalyzer } from '@/hooks/queries/useQueryAnalyzer'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { HotAnalyzerModule } from '@/libs/request/api/analyzer'
import { useQueryClient } from '@tanstack/react-query'
import AiImitativeModal from '@/pages/HotAnalyzer/analyzerDetail/AiImitativeModal'
import SaveScriptDialog from '@/pages/HotAnalyzer/analyzerDetail/SaveScriptDialog'
import { shotTypes } from '@/types/project'

function VideoPlayer({ data, onAspectRatioChange }: { data: any; onAspectRatioChange?: (ratio: number) => void }) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMetadataLoaded, setIsMetadataLoaded] = useState(false)
  const videoRef = useRef<HTMLVideoElement | null>(null)

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    setIsPlaying(false) // 重置播放状态

    const handleLoadedMetadata = () => {
      const ratio = video.videoWidth / video.videoHeight
      setIsMetadataLoaded(true)
      if (onAspectRatioChange) onAspectRatioChange(ratio)
    }

    // 如果已经有 metadata，就直接调用一次
    if (video.readyState >= 1 && video.videoWidth && video.videoHeight) {
      handleLoadedMetadata()
    } else {
      setIsMetadataLoaded(false) // 只有没加载时才显示 loading
      video.addEventListener('loadedmetadata', handleLoadedMetadata)
    }

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
    }
  }, [data.video_url, onAspectRatioChange])

  const handlePlay = () => {
    if (videoRef.current) {
      videoRef.current.play()
      setIsPlaying(true)
    }
  }

  const handlePause = () => {
    setIsPlaying(false)
  }

  // 点击右上角按钮复制链接
  const handleCopyLink = async (e: React.MouseEvent) => {
    e.stopPropagation()
    try {
      await navigator.clipboard.writeText(data.source_link)
      toast.success('视频链接已复制到剪贴板')
    } catch (err) {
      toast.error('视频链接复制失败')
    }
  }

  return (
    <div className="h-[100%] w-full relative">
      <video
        ref={videoRef}
        src={data.video_url}
        controls={isPlaying} // 只有播放时显示控制条
        className="h-full w-full rounded-lg"
        onEnded={handlePause} // 视频结束后显示遮罩层
        onPause={handlePause} // 点击暂停也显示遮罩层
      />
      {/* loading 遮罩层 */}
      {!isMetadataLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-t-transparent border-primary-highlight1" />
        </div>
      )}
      {/* 初始状态覆盖层 */}
      {isMetadataLoaded && !isPlaying && (
        <div
          className="absolute inset-0 flex flex-col items-center justify-center bg-black/30 cursor-pointer rounded-lg"
          onClick={handlePlay}
        >
          {/* 中间播放按钮 */}
          <div className="mb-4 w-16 h-16 bg-white rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-black" fill="currentColor" viewBox="0 0 20 20">
              <path d="M6 4l12 6-12 6V4z" />
            </svg>
          </div>
          {data.source_link && (
            <div
              className="absolute top-2 right-2 w-6 h-6 bg-white rounded-full flex items-center justify-center p-1"
              onClick={handleCopyLink}
            >
              <Link className="w-full h-full text-black" />
            </div>
          )}
        </div>
      )}
    </div>
  )
}

function BadgeList({ items }: { items: string[] }) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [overflow, setOverflow] = useState(false)

  useEffect(() => {
    const container = containerRef.current
    if (container) {
      const lineHeight = 24 // 假设每行高度 1.5rem = 24px
      const maxHeight = lineHeight * 2 // 两行高度
      setOverflow(container.scrollHeight > maxHeight)
    }
  }, [items])

  const renderBadges = (items: string[]) =>
    items.map((item, index) => (
      <Badge key={index} variant="primaryHighlight1">
        {item}
      </Badge>
    ))

  return (
    <div className="relative overflow-hidden max-h-[3.5rem] pr-6" ref={containerRef}>
      <div className="flex flex-wrap gap-2">{renderBadges(items)}</div>
      {overflow && (
        <WithTooltip
          content={<div className="flex flex-wrap gap-2 p-2 rounded shadow-lg max-w-xs">{renderBadges(items)}</div>}
        >
          <Ellipsis className="absolute bottom-2 right-0 px-1 text-primary-highlight1 w-6 h-6 cursor-pointer" />
        </WithTooltip>
      )}
    </div>
  )
}
const HotAnalyzerDetail: React.FC = () => {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { id } = useParams()
  const { data } = useQueryAnalyzer(id?.toString())
  const [aspectRatio, setAspectRatio] = useState<number | null>(null)
  const [openSaveDialog, setOpenSaveDialog] = useState(false)
  const [openAiImitativeDialog, setOpenAiImitativeDialog] = useState(false)
  const deleteModal = useDeleteModal()
  const shotTypeMap = Object.fromEntries(shotTypes.map(item => [item.value, item.label]))

  const handleDelete = async () => {
    if (!id) return
    deleteModal({
      kind: '解析记录',
      name: data?.title + '解析脚本',
      danger: true,
      action: async () => {
        try {
          await HotAnalyzerModule.delete({ id })
          await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.HOT_ANALYZER_LIST] })
          toast('删除成功', { type: 'success' })
          navigate('/home/<USER>')
        } catch (error: any) {
          toast.error(error?.message || '删除失败')
        }
      },
    })
  }

  if (!data) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <p className="text-lg font-bold" onClick={() => navigate(-1)}>
          数据请求失败 , 请点击返回上页重试
        </p>
      </div>
    )
  }
  const isLandscape = aspectRatio !== null ? aspectRatio >= 1 : null

  return (
    <div className="flex flex-col w-full h-full gap-6 p-2">
      <div className="flex items-center justify-between border-b py-4">
        <div className="text-primary-highlight1 text-lg font-bold">{data.title}解析脚本</div>
        <div className="flex items-center gap-2 text-xs">
          <div className="text-gray-400">
            任务ID：{id} &nbsp; 上传时间：{formatTimestamp(data.create_time)}
          </div>
          <Button variant="outline" onClick={handleDelete}>
            删除记录
          </Button>
        </div>
      </div>
      <div className="flex flex-wrap gap-4">
        <Card
          key="video"
          className={cn(
            'border shadow-lg overflow-hidden bg-white/4 p-0',
            isLandscape === null
              ? 'h-[387px]'
              : isLandscape
                ? 'aspect-[16/9] max-w-[688px] min-w-[380px]'
                : 'aspect-[9/16] h-[387px]',
          )}
        >
          <CardContent className="w-full h-full flex flex-col relative justify-around gap-4 p-0">
            <VideoPlayer data={data} onAspectRatioChange={setAspectRatio} />
          </CardContent>
        </Card>
        <Card key="info" className="flex-1 border shadow-lg bg-white/4 min-w-[640px] h-[387px] cursor-default">
          <CardContent className="flex flex-col relative pt-6 justify-between h-full">
            <div className="flex justify-between">
              <div className="font-bold">视频摘要</div>
              <div className="text-xs text-gray-400 flex gap-x-2">
                <span>口播时长：{formatDuration(data.des.时长, true)}</span>
                <span>口播语言：{data.des.语言}</span>
              </div>
            </div>
            <div className="mt-4 flex flex-1 flex-col justify-around">
              {Object.entries(data.des)
                .filter(([key]) => key !== '语言' && key !== '时长')
                .map(([key, value]) => {
                  const v = value as string | string[]

                  return (
                    <div key={key} className="flex flex-col text-sm">
                      <span className="font-semibold mb-2 text-primary-highlight1">{key}</span>
                      {Array.isArray(v) ? <BadgeList items={v} /> : <span>{v}</span>}
                    </div>
                  )
                })}
            </div>
          </CardContent>
        </Card>
      </div>
      <Card key="list" className="border shadow-lg bg-white/4 min-w-[640px]">
        <CardContent className="flex flex-col relative pt-6">
          <div className="flex justify-between">
            <div className="font-bold">视频脚本</div>
            <div className="text-gray-400 flex gap-x-2">
              <Button variant="outline" className="text-xs" onClick={() => setOpenSaveDialog(true)}>
                保存脚本
              </Button>
              <Button className="bg-gradient-brand text-white text-xs" onClick={() => setOpenAiImitativeDialog(true)}>
                AI仿写
              </Button>
            </div>
          </div>
          <div className="">
            <Table>
              <TableHeader>
                <TableRow className="border-dashed">
                  <TableHead>分镜名称</TableHead>
                  <TableHead>话术</TableHead>
                  <TableHead>景别</TableHead>
                  <TableHead>画面</TableHead>
                  <TableHead>备注</TableHead>
                </TableRow>
              </TableHeader>
              {data.script ? (
                <TableBody>
                  {data.script.map((script, index) =>
                    script.scene.map((s, idx) => (
                      <TableRow key={`${index}-${idx}`} className="border-dashed">
                        {idx === 0 && (
                          <>
                            <TableCell
                              rowSpan={script.scene.length}
                              className="whitespace-normal break-words min-w-0 border-r border-dashed text-center"
                            >
                              <div className="font-semibold text-nowrap">{script.script_name}</div>
                            </TableCell>
                            <TableCell
                              rowSpan={script.scene.length}
                              className="whitespace-normal break-words min-w-0 border-r border-dashed"
                            >
                              {script.text}
                            </TableCell>
                          </>
                        )}
                        <TableCell className="border-r border-dashed min-w-[100px] text-center">
                          {shotTypeMap[s.shotType] || s.shotType}
                        </TableCell>
                        <TableCell className="border-r border-dashed">
                          <video
                            src={s.url}
                            controls
                            className={cn('', isLandscape ? 'w-[300px] aspect-[16/9]' : 'w-[100px] aspect-[9/16]')}
                          />
                        </TableCell>
                        <TableCell className="whitespace-normal break-words min-w-0">{s.notes}</TableCell>
                      </TableRow>
                    )),
                  )}
                </TableBody>
              ) : (
                <TableCaption>
                  <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-blue-500" />
                </TableCaption>
              )}
            </Table>
          </div>
        </CardContent>
      </Card>
      <SaveScriptDialog
        open={openSaveDialog} // 控制弹窗是否显示
        onOpenChange={setOpenSaveDialog} // 弹窗自己关闭时会回调
        scriptData={data} // 传入当前视频脚本数据
      />

      <AiImitativeModal
        open={openAiImitativeDialog}
        onOpenChange={setOpenAiImitativeDialog}
        hotCopyScriptId={id}
      />
    </div>
  )
}

export default HotAnalyzerDetail
