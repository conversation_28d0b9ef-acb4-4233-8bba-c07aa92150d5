import React, { useCallback, useRef, useState } from 'react'
import { Ellipsis, EllipsisVertical, FolderInput } from 'lucide-react'
import { FolderActionKeys, MaterialResource } from '@/types/resources'
import folderIcon from '@/assets/folder.svg'
import { cn, formatCountdown } from '@/components/lib/utils'
import { createPortal } from 'react-dom'
import { PreviewableVideoCard } from '@/components/material/PreviewableVideoCard'
import { TeamSharedAudioItem } from '@/modules/video-editor/resource-plugin-system/components/team-shared-audio-item'
import { AuthedImg } from '@/components/authed-img'
import { ResourceCacheType } from '@app/shared/types/resource-cache.types'

/**
 * 格式化时长
 * ms： 时长（毫秒）
 * colonFormat： 是否显示格式-00:00
 */
export function formatDuration(ms: number, colonFormat?: boolean): string {
  if (!ms || ms < 0) return colonFormat ? '00:00' : '0s'

  const totalSeconds = Math.floor(ms / 1000)

  if (colonFormat) {
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60

    const pad = (num: number) => String(num).padStart(2, '0')

    if (hours > 0) {
      return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`
    } else {
      return `${pad(minutes)}:${pad(seconds)}`
    }
  }

  // 默认格式（最大单位 + 小数）
  const seconds = ms / 1000
  if (seconds >= 3600) {
    return `${(seconds / 3600).toFixed(1)}h`
  } else if (seconds >= 60) {
    return `${(seconds / 60).toFixed(1)}m`
  } else {
    return `${seconds.toFixed(1)}s`
  }
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes?: number): string {
  if (!bytes || bytes <= 0) return ''
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(1)}${sizes[i]}`
}

const ActionMenuItem: React.FC<{
  action: FolderAction | MediaAction
  isFolder: boolean
  media: MaterialResource.Media
  currentFolderId?: string
  onClose?: () => void
}> = ({ action, isFolder, media, currentFolderId, onClose }) => {
  const [showDetailTooltip, setShowDetailTooltip] = useState(false)
  const isDetail = action.label === '详细信息'

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (isFolder) {
      ;(action as FolderAction).onClick?.(media.fileId, currentFolderId, media.fileName)
    } else {
      ;(action as MediaAction).onClick?.(media.fileId, media.fileName, media.folderUuid)
    }
    onClose?.()
  }

  // 普通菜单项
  return (
    <div
      className="flex items-center px-3 py-1 cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-700"
      onClick={handleClick}
      onMouseEnter={() => isDetail && setShowDetailTooltip(true)}
      onMouseLeave={() => isDetail && setShowDetailTooltip(false)}
    >
      {action.icon}
      <span className="ml-2">{action.label}</span>

      {isDetail && showDetailTooltip && (
        <div className="absolute top-full right-0 mt-2 grid grid-cols-2 gap-x-4 gap-y-1 w-96 p-3 rounded bg-white dark:bg-neutral-900 shadow-lg border text-xs">
          <div>名称: {media.fileName}</div>
          <div>类型: 文件夹</div>
          {media.fileSize && <div>大小: {formatFileSize(media.fileSize)}</div>}
          <div className="whitespace-nowrap">
            创建时间:{' '}
            {media.createTime ? new Date(media.createTime).toLocaleString().replace(/[\u2028\u2029\s]+/g, ' ') : '-'}
          </div>
          {media.updatedTime && (
            <div className="whitespace-nowrap">
              更新时间: {new Date(media.updatedTime).toLocaleString().replace(/\s+/g, ' ')}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
interface MediaItemProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation: string
  media: MaterialResource.Media
  isSelected: boolean
  isFolder: boolean
  actions: FolderAction[] | MediaAction[]
  currentFolderId?: string
  isTrash?: boolean
  isEditItem?: boolean
  onToggleSelect: (fileId: string) => void
  onFolderClick?: () => void
  onbatchImportStoryboards?: () => Promise<void> | void
}

type FolderActionHandler = (nodeId: string, parentId?: string, label?: string) => void
export type FolderAction = {
  icon: React.ReactNode
  label: string
  value: string
  onClick: FolderActionHandler
}

type MediaActionHandler = (fileId: string, fileName: string, folderUuid: string) => void
export type MediaAction = {
  icon: React.ReactNode
  label: string
  value: string
  onClick: MediaActionHandler
}

const MediaItemComponent: React.FC<MediaItemProps> = ({
  orientation,
  media,
  isSelected,
  isFolder,
  actions,
  currentFolderId,
  isTrash = false,
  isEditItem = false,
  onToggleSelect,
  onFolderClick,
  onbatchImportStoryboards,
  ...rest
}) => {
  const DEFAULT_WIDTH = 120

  const [popup, setPopup] = useState(false)
  const [editPopup, setEditPopup] = useState(false)
  const ellipsisRef = useRef<HTMLDivElement | null>(null)
  const editEllipsisRef = useRef<HTMLDivElement | null>(null)

  const renderActionsMenu = useCallback(
    (
      anchorRef: React.RefObject<HTMLElement | null>,
      actions: (FolderAction | MediaAction)[],
      isFolder: boolean,
      media: MaterialResource.Media,
      currentFolderId?: string,
      onClose?: () => void,
    ) => {
      const rect = anchorRef.current?.getBoundingClientRect()
      if (!rect) return null

      const menuWidth = 150 // 菜单最小宽度
      const menuHeight = actions.length * 40 // 简单估算: 每个菜单项40px
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      // 如果右边不够显示，就向左对齐
      const left = rect.left + menuWidth > viewportWidth ? rect.right - menuWidth : rect.left
      // 如果下方不够显示，就放到上方
      const spaceBelow = viewportHeight - rect.bottom
      const top = spaceBelow >= menuHeight ? rect.bottom + window.scrollY : rect.top - menuHeight + window.scrollY

      return createPortal(
        <div
          style={{
            position: 'absolute',
            top,
            left,
            zIndex: 9999,
          }}
          onMouseLeave={onClose}
        >
          <div className="border dark:bg-neutral-800 rounded shadow-lg min-w-[150px] py-2 px-4">
            {actions
              .filter(action => action.value !== FolderActionKeys.CREATE)
              .map((action, idx) => (
                <ActionMenuItem
                  key={idx}
                  action={action}
                  isFolder={isFolder}
                  media={media}
                  currentFolderId={currentFolderId}
                  onClose={onClose}
                />
              ))}
          </div>
        </div>,
        document.body,
      )
    },
    [],
  )

  return (
    <div
      key={media.fileId}
      {...rest}
      className={cn('flex flex-col relative group')}
      style={{
        width: !isEditItem ? (orientation === 'horizontal' ? 200 : 160) : DEFAULT_WIDTH,
      }}
    >
      {/* 类型图标和选择框 */}
      <div
        className={cn(
          'w-full border rounded-sm shadow-sm flex flex-col relative group overflow-hidden',
          !isEditItem ? (orientation === 'horizontal' ? 'h-50' : 'h-64') : '',
        )}
        onClick={e => {
          const target = e.target as Element

          // 判断点击是否来自复选框
          if (!target.closest('input[type="checkbox"]')) {
            if (isFolder && onFolderClick) {
              onFolderClick() // 确保调用函数
            }
          }
        }}
      >
        {/* 回收站特有信息显示 */}
        {isTrash && media.recycleExpiredAt && media.recycleExpiredAt !== 0 && (
          <div className="absolute top-2 left-2 text-xs text-gray-500 bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block">
            剩余{formatCountdown(media.recycleExpiredAt, ['day'])}
          </div>
        )}

        {!isFolder && !isTrash && media.resType !== MaterialResource.MediaType.AUDIO && (
          <div
            className={cn(
              'absolute z-3 text-gray-500 bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block',
              isEditItem ? 'text-[0.6rem] top-0 left-0' : 'text-xs top-2 left-2',
            )}
          >
            {`合成使用${media.useCount}次`}
          </div>
        )}

        <div
          onMouseDown={e => e.stopPropagation()}
          className={cn('absolute  z-3', isEditItem ? 'top-0 right-1' : 'top-2 right-2')}
        >
          <input
            type="checkbox"
            checked={isSelected}
            onChange={e => {
              e.stopPropagation()
              onToggleSelect(media.fileId)
            }}
            className="w-4 h-4 cursor-pointer accent-primary-highlight1"
          />
        </div>
        <div
          className={cn(
            'flex items-center justify-center relative',
            !isEditItem ? (orientation === 'horizontal' ? 'h-50' : 'h-64') : 'h-35',
          )}
        >
          <div className="w-full h-full flex items-center justify-center overflow-hidden">
            {media.resType === MaterialResource.MediaType.FOLDER && (
              <img src={folderIcon} alt="文件夹" className="w-[40%] h-[40%]" />
            )}
            {media.resType === MaterialResource.MediaType.VIDEO && <PreviewableVideoCard media={media} />}
            {media.resType === MaterialResource.MediaType.AUDIO && (
              <>
                <TeamSharedAudioItem
                  key={media.fileId}
                  audioUrl={media.url}
                  durations={media.duration ?? 50}
                  title={media.fileName}
                  id={media.fileId}
                  coverUrl={media.cover}
                  resourceType={ResourceCacheType.SUFFIXLESS_SOUND}
                  customExt="mp3"
                  className="w-full h-full"
                  isLocal={false}
                />
              </>
            )}
            {media.resType === MaterialResource.MediaType.IMAGE && (
              <AuthedImg src={media.cover} alt={media.fileName} className="w-full h-full object-cover" />
            )}
          </div>
          <div className={cn('z-4 absolute bottom-1 left-2 text-gray-500', isEditItem ? 'text-[0.6rem]' : 'text-xs')}>
            {media.resType === MaterialResource.MediaType.FOLDER ? (
              <div className="bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block">
                {isEditItem
                  ? `文件夹 ${media.childrenFolder}，素材 ${media.mediaNum}`
                  : `${media.childrenFolder}个文件夹，${media.mediaNum}个素材`}
              </div>
            ) : isEditItem ? (
              media.resType === MaterialResource.MediaType.IMAGE ? null : media.duration ? (
                <div className="bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block">
                  {formatDuration(media.duration, isEditItem)}
                </div>
              ) : null
            ) : (
              (media.duration || media.fileSize) && (
                <div className="bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block">
                  {media.resType === MaterialResource.MediaType.IMAGE || !media.duration
                    ? formatFileSize(media.fileSize)
                    : `${formatDuration(media.duration, isEditItem)}/${formatFileSize(media.fileSize)}`}
                </div>
              )
            )}
          </div>
          {isEditItem && media.resType === MaterialResource.MediaType.FOLDER && (
            <div
              className={cn(
                'z-5 absolute bottom-1 inset-x-1 rounded py-1 flex text-black items-center justify-evenly bg-blue-200 transition-opacity',
                editPopup ? 'opacity-100' : 'opacity-0 group-hover:opacity-100',
              )}
            >
              <FolderInput
                className="w-4 h-4"
                onClick={e => {
                  e.stopPropagation() // 防止触发父层点击
                  onbatchImportStoryboards?.()
                }}
              >
                {' '}
                <title>批量导入到分镜</title>
              </FolderInput>
              <div className="w-1px h-4 bg-black" />
              <div
                onClick={e => {
                  e.stopPropagation()
                }}
                onMouseEnter={() => setEditPopup(true)}
                onMouseLeave={() => setEditPopup(false)}
              >
                <div className="" ref={editEllipsisRef}>
                  <Ellipsis className="w-4 h-4" />
                </div>
                {editPopup &&
                  renderActionsMenu(editEllipsisRef, actions, isFolder, media, currentFolderId, () =>
                    setEditPopup(false),
                  )}
              </div>
            </div>
          )}
          {/* 素材的操作菜单 */}
          {isEditItem && media.resType !== MaterialResource.MediaType.FOLDER && (
            <div
              onClick={e => {
                e.stopPropagation()
              }}
              className="z-5 absolute right-2 bottom-1"
              onMouseEnter={() => setEditPopup(true)}
              onMouseLeave={() => setEditPopup(false)}
            >
              <div ref={editEllipsisRef} className="bg-black/50 px-2 py-0.5 rounded">
                <Ellipsis className="w-4 h-4 text-white" />
              </div>
              {editPopup &&
                renderActionsMenu(editEllipsisRef, actions, isFolder, media, currentFolderId, () =>
                  setEditPopup(false),
                )}
            </div>
          )}
        </div>
      </div>

      {/* 信息 */}
      <div className="flex-1 flex justify-between items-center py-2">
        <div className="flex-1 min-w-0 flex flex-col">
          <div className="text-sm font-medium truncate mb-1">{media.fileName}</div>
          {/* <div className="text-xs text-gray-400">
            {media.createTime ? new Date(media.createTime).toLocaleTimeString() : ''}
          </div> */}
        </div>

        {/* 右下角更多操作 */}
        {!isEditItem && (
          <button className="text-sm" onMouseEnter={() => setPopup(true)} onMouseLeave={() => setPopup(false)}>
            <div ref={ellipsisRef} className="hover:bg-gray-100 dark:hover:bg-neutral-700 rounded p-1">
              <EllipsisVertical className="w-5 h-5 text-gray-500" />
            </div>
            {popup &&
              renderActionsMenu(
                ellipsisRef as React.RefObject<HTMLElement | null>,
                actions,
                isFolder,
                media,
                currentFolderId,
                () => setPopup(false),
              )}
          </button>
        )}
      </div>
    </div>
  )
}

const MediaItem = React.memo(MediaItemComponent)

export default MediaItem
