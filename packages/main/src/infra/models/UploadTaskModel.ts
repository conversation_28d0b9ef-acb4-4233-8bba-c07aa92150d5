import { UploadTask } from '@app/shared/types/local-db/entities/upload-task.entity.js'

/**
 * 上传任务领域模型类
 * 专门用于管理文件上传任务
 */
export class UploadTaskModel {

  /**
   * 任务ID
   */
  id: number

  /**
   * 团队ID
   */
  team_id: number

  /**
   * 用户ID
   */
  uid: string

  /**
   * 任务名称（文件名）
   */
  name: string

  /**
   * 本地文件路径
   */
  local_path: string

  /**
   * 云端URL（上传完成后）
   */
  url: string

  /**
   * 文件哈希值
   */
  hash: string

  /**
   * 文件大小（字节）
   */
  size: number

  /**
   * 上传进度（0-100）
   */
  progress: number

  /**
   * 任务状态
   */
  status: UploadTask.Status

  /**
   * 任务类型
   */
  type: UploadTask.Type

  /**
   * 失败原因
   */
  reason: string

  /**
   * 所属文件夹ID
   */
  folder_id: string

  /**
   * OSS对象键
   */
  object_key: string

  /**
   * OSS对象ID
   */
  object_id: string

  /**
   * 上传模块类型
   */
  upload_module: string

  /**
   * OSS断点续传数据（JSON格式）
   */
  checkpoint_data: string

  /**
   * 删除时间戳
   */
  deleted_at: number

  /**
   * 更新时间戳
   */
  updated_at: number

  /**
   * 创建时间戳
   */
  created_at: number

  batch_id: number

  source_scene: UploadTask.UploadSourceScenes

  /**
   * 构造函数
   * @param data 上传任务数据
   */
  constructor(data: Partial<UploadTaskModel> = {}) {
    this.id = data.id ?? 0
    this.team_id = data.team_id ?? 0
    this.uid = data.uid ?? ''
    this.name = data.name ?? ''
    this.local_path = data.local_path ?? ''
    this.url = data.url ?? ''
    this.hash = data.hash ?? ''
    this.size = data.size ?? 0

    this.progress = data.progress ?? 0
    this.status = data.status ?? UploadTask.Status.PENDING
    this.type = data.type ?? UploadTask.Type.OTHER
    this.reason = data.reason ?? ''
    this.folder_id = data.folder_id ?? ''
    this.object_key = data.object_key ?? ''
    this.object_id = data.object_id ?? ''
    this.upload_module = data.upload_module ?? 'media'
    this.checkpoint_data = data.checkpoint_data ?? ''
    this.deleted_at = data.deleted_at ?? 0
    this.updated_at = data.updated_at ?? Date.now()
    this.created_at = data.created_at ?? Date.now()

    this.batch_id = data.batch_id ?? 1
    this.source_scene = data.source_scene ?? UploadTask.UploadSourceScenes.MATERIAL_LIBRARY
  }

  /**
   * 判断是否已删除
   */
  isDeleted(): boolean {
    return this.deleted_at > 0
  }

  /**
   * 判断是否属于指定用户
   * @param uid 用户ID
   */
  belongsToUser(uid: string): boolean {
    return this.uid === uid
  }

  /**
   * 判断是否属于指定团队
   * @param teamId 团队ID
   */
  belongsToTeam(teamId: number): boolean {
    return this.team_id === teamId
  }

  /**
   * 判断任务是否处于等待状态
   */
  isPending(): boolean {
    return this.status === UploadTask.Status.PENDING
  }

  /**
   * 判断任务是否正在上传
   */
  isUploading(): boolean {
    return this.status === UploadTask.Status.UPLOADING
  }

  /**
   * 判断任务是否已暂停
   */
  isPaused(): boolean {
    return this.status === UploadTask.Status.PAUSED
  }

  /**
   * 判断任务是否已完成
   */
  isCompleted(): boolean {
    return this.status === UploadTask.Status.COMPLETED
  }

  /**
   * 判断任务是否失败
   */
  isFailed(): boolean {
    return this.status === UploadTask.Status.FAILED
  }

  /**
   * 判断任务是否已取消
   */
  isCancelled(): boolean {
    return this.status === UploadTask.Status.CANCELLED
  }

  /**
   * 判断任务是否可以暂停
   */
  canPause(): boolean {
    return this.status === UploadTask.Status.UPLOADING
  }

  /**
   * 判断任务是否可以恢复
   */
  canResume(): boolean {
    return this.status === UploadTask.Status.PAUSED || this.status === UploadTask.Status.FAILED
  }

  /**
   * 判断任务是否可以取消
   */
  canCancel(): boolean {
    return this.status === UploadTask.Status.PENDING ||
           this.status === UploadTask.Status.UPLOADING ||
           this.status === UploadTask.Status.PAUSED
  }

  /**
   * 判断任务是否可以重试
   */
  canRetry(): boolean {
    return this.status === UploadTask.Status.FAILED
  }

  /**
   * 获取任务的显示名称
   */
  getDisplayName(): string {
    return this.name || '未命名文件'
  }

  /**
   * 获取任务状态文本
   */
  getStatusText(): string {
    const statusMap = {
      [UploadTask.Status.PENDING]: '等待上传',
      [UploadTask.Status.UPLOADING]: '上传中',
      [UploadTask.Status.PAUSED]: '已暂停',
      [UploadTask.Status.COMPLETED]: '上传完成',
      [UploadTask.Status.FAILED]: '上传失败',
      [UploadTask.Status.CANCELLED]: '已取消'
    }
    return statusMap[this.status] || '未知状态'
  }

  /**
   * 获取已上传字节数（通过小数进度计算）
   */
  getUploadedBytes(): number {
    return Math.round(this.progress * this.size)
  }

  /**
   * 更新上传进度
   */
  updateProgress(progress: number): void {
    // 直接使用小数进度值（0-1）
    this.progress = progress
    this.updated_at = Date.now()
  }

  /**
   * 设置任务状态
   */
  setStatus(status: UploadTask.Status, reason?: string): void {
    this.status = status
    if (reason) {
      this.reason = reason
    }
    this.updated_at = Date.now()
  }

  /**
   * 重置任务（用于重试）
   */
  reset(): void {
    this.progress = 0
    this.status = UploadTask.Status.PENDING
    this.reason = ''
    this.checkpoint_data = '' // 清空断点数据
    this.updated_at = Date.now()
  }

  /**
   * 将上传任务转换为JSON对象
   */
  toJSON(): UploadTask.IUploadTask {
    return {
      id: this.id,
      team_id: this.team_id,
      uid: this.uid,
      name: this.name,
      local_path: this.local_path,
      url: this.url,
      hash: this.hash,
      size: this.size,
      progress: this.progress,
      status: this.status,
      type: this.type,
      reason: this.reason,
      folder_id: this.folder_id,
      object_key: this.object_key,
      object_id: this.object_id,
      upload_module: this.upload_module,
      checkpoint_data: this.checkpoint_data,
      batch_id: this.batch_id,
      source_scene: this.source_scene,
      deleted_at: this.deleted_at,
      updated_at: this.updated_at,
      created_at: this.created_at
    }
  }
}

