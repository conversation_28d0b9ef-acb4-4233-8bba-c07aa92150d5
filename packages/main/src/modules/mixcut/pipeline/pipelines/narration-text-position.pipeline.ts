import { SimpleMixcutPipeline } from '@/modules/mixcut/pipeline/mixcut-pipeline.types.js'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { TextOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'
import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut.js'
import { MixcutPipelineUtils } from '@/modules/mixcut/pipeline/mixcut-pipeline.utils.js'

export class NarrationTextPositionPipeline extends SimpleMixcutPipeline<typeof MIXCUT_PIPELINES.narrationText.positionOffset, TextOverlay> {

  async processSingleOverlay(overlay: TextOverlay & MixcutableOverlay): Promise<TextOverlay & MixcutableOverlay> {
    const { minX, minY, maxX, maxY } = this._offsetBoundary

    const { allowHorizontalOffset, range } = this.context.params

    const offsetX = !allowHorizontalOffset
      ? 0
      : _.clamp(
        _.random(-range, range),
        minX,
        maxX
      )

    const offsetY = _.clamp(
      _.random(-range, range),
      minY,
      maxY
    )

    return {
      ...overlay,
      left: overlay.left + offsetX,
      top: overlay.top + offsetY,
    }
  }

  filter(overlay: MixcutableOverlay): overlay is TextOverlay & MixcutableOverlay {
    return SimpleMixcutPipeline.filters.narrationText(overlay)
  }

  private get _offsetBoundary() {
    return MixcutPipelineUtils.getOverlaysBoundary(
      this.context.overlays.filter(o => this.filter(o)),
      this.context.playerMetadata.width,
      this.context.playerMetadata.height
    )
  }
}
