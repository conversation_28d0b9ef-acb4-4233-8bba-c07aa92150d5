import { toast } from 'react-toastify'
import { useCallback } from 'react'
import { useMutation, UseMutationOptions, UseMutationResult } from '@tanstack/react-query'

interface RequestOptions<TData = any, TError = Error, TVariables = void, TContext = unknown>
  extends Omit<UseMutationOptions<TData, TError, TVariables, TContext>, 'mutationFn'> {
  /** 操作名称，用于自动生成成功和失败消息 */
  actionName?: string
  /** 成功时的 toast 消息，设置为 false 则不显示。如果设置了 actionName，会自动拼接为 "{actionName}成功" */
  successMessage?: string | false
  /** 失败时的 toast 消息，设置为 false 则不显示。如果设置了 actionName，会自动拼接为 "{actionName}失败" */
  errorMessage?: string | false
  /** 是否显示成功 toast，默认为 true */
  showSuccessToast?: boolean
  /** 是否显示失败 toast，默认为 true */
  showErrorToast?: boolean
  /** 自定义成功消息生成函数 */
  getSuccessMessage?: (data: TData, variables: TVariables) => string
  /** 自定义错误消息生成函数 */
  getErrorMessage?: (error: TError, variables: TVariables) => string
}

type RequestFunction<TData = any, TVariables = void> =
  (variables: TVariables) => Promise<TData>

type UseRequestResult<TData = any, TError = Error, TVariables = void, TContext = unknown> =
  UseMutationResult<TData, TError, TVariables, TContext>

export function useRequest<TData = any, TError = Error, TVariables = void, TContext = unknown>(
  mutationFn: RequestFunction<TData, TVariables>,
  options: RequestOptions<TData, TError, TVariables, TContext> = {}
): UseRequestResult<TData, TError, TVariables, TContext> {
  const {
    actionName,
    successMessage,
    errorMessage,
    showSuccessToast = true,
    showErrorToast = true,
    getSuccessMessage,
    getErrorMessage,
    onSuccess,
    onError,
    ...mutationOptions
  } = options

  // 根据 actionName 自动生成消息，如果没有明确设置 successMessage 或 errorMessage
  const finalSuccessMessage = successMessage !== undefined
    ? successMessage
    : actionName
      ? `${actionName}成功`
      : '操作成功'

  const finalErrorMessage = errorMessage !== undefined
    ? errorMessage
    : actionName
      ? `${actionName}失败`
      : '操作失败'

  // 处理成功回调
  const handleSuccess = useCallback(
    (data: TData, variables: TVariables, context: TContext) => {
      // 显示成功 toast
      if (showSuccessToast && finalSuccessMessage !== false) {
        let message = finalSuccessMessage

        if (getSuccessMessage) {
          message = getSuccessMessage(data, variables)
        }

        if (message) {
          toast.success(message)
        }
      }

      // 调用用户提供的成功回调
      if (onSuccess) {
        onSuccess(data, variables, context)
      }
    },
    [finalSuccessMessage, showSuccessToast, getSuccessMessage, onSuccess]
  )

  // 处理错误回调
  const handleError = useCallback(
    (error: TError, variables: TVariables, context: TContext | undefined) => {
      // 显示错误 toast
      if (showErrorToast && finalErrorMessage !== false) {
        let message = finalErrorMessage

        // 优先使用自定义错误消息生成函数
        if (getErrorMessage) {
          message = getErrorMessage(error, variables)
        }
        // 尝试从错误对象中提取消息
        else if (error && typeof error === 'object' && 'message' in error) {
          message = (error as any).message || finalErrorMessage
        }

        if (message) {
          toast.error(message)
        }
      }

      // 调用用户提供的错误回调
      if (onError) {
        onError(error, variables, context)
      }
    },
    [finalErrorMessage, showErrorToast, getErrorMessage, onError]
  )

  return useMutation({
    mutationFn,
    onSuccess: handleSuccess,
    onError: handleError,
    ...mutationOptions,
  })
}

export default useRequest
