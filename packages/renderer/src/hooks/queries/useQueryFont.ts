import { QUERY_KEYS } from '@/constants/queryKeys'
import { ResourceModule } from '@/libs/request/api/resource'
import { BaseResourceQueryParams, FontResource } from '@/types/resources'
import { useInfiniteQuery } from '../useInfiniteQuery'

import { PaginationParams } from '@app/shared/infra/request'

export const useInfiniteQueryBubbleText = (params: PaginationParams) => {
  return useInfiniteQuery<FontResource.BubbleLetters>(
    [QUERY_KEYS.BUBBLE_LETTERS],
    ResourceModule.font.bubbles,
    params,
    {
      pageSize: params.pageSize || 20,
    }
  )
}

export const useInfiniteQueryFontUnified = (params: BaseResourceQueryParams & {
  selectedCategory?: string,
}) => {
  const { selectedCategory, autoLoadAllPages, ...queryParams } = params

  // 判断是否为收藏分类
  const isCollectedCategory = selectedCategory === 'collected'

  // 根据分类决定查询参数
  const finalParams = isCollectedCategory
    ? queryParams // 收藏列表不需要 categoryIds
    : {
      ...queryParams,
      // 全部分类不传 categoryIds，其他分类传递对应的 categoryIds
      categoryIds: (!selectedCategory || selectedCategory === 'all')
        ? undefined
        : [selectedCategory]
    }

  // 根据分类决定使用哪个查询
  if (isCollectedCategory) {
    return useInfiniteQuery<FontResource.Font>(
      [QUERY_KEYS.FONT_COLLECTED, finalParams],
      ResourceModule.font.collected,
      finalParams,
      {
        pageSize: finalParams.pageSize || 20,
        autoLoadAll: autoLoadAllPages
      }
    )
  } else {
    return useInfiniteQuery<FontResource.Font>(
      [QUERY_KEYS.FONT_LIST, finalParams],
      ResourceModule.font.list,
      finalParams,
      {
        pageSize: finalParams.pageSize || 20,
        autoLoadAll: autoLoadAllPages
      }
    )
  }
}
