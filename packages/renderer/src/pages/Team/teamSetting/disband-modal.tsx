import { ModalContent } from '@/components/modal'
import { useQueryCurrentTeam } from '@/hooks/queries/useQueryTeam'
import { useModal, useModalContext  } from '@/libs/tools/modal'
import React, { useState } from 'react'

import { useForm, FormProvider } from 'react-hook-form'
import { Input } from '@/components/ui/input'

type DisbandFormValues = {
  teamName: string
  phone: string
}

function DisbandTeamModal({ onConfirm }: { onConfirm?: () => void }) {
  const { close } = useModalContext()
  const { data: team } = useQueryCurrentTeam()
  const [pending, setPending] = useState(false)
  const methods = useForm<DisbandFormValues>({
    defaultValues: {
      teamName: '',
    },
  })
  const {
    register,
    handleSubmit,
    setError,
    watch,
    formState: { errors },
  } = methods
  const teamName = watch('teamName')

  const onSubmit = (values: DisbandFormValues) => {
    console.log('提交的值', values)
    if (!team) return
    setPending(true)

    if (values.teamName !== team.name) {
      setError('teamName', { type: 'manual', message: '团队名称不匹配' })
      setPending(false)
      return
    }

    console.log('提交成功', values)
    setPending(false)
    onConfirm?.()
    close()
  }

  return (
    <FormProvider {...methods}>
      <form>
        <ModalContent
          title="解散团队"
          description="解散后，团队内所有内容都会彻底删除不可恢复，请谨慎操作"
          pending={pending}
          onConfirm={handleSubmit(onSubmit)}
        >
          <div className="flex flex-col gap-4 w-full py-4">
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">团队名称</label>
              <div className="relative">
                <Input
                  placeholder="请输入当前团队名称"
                  maxLength={20}
                  {...register('teamName', {
                    required: '请输入团队名称',
                    maxLength: { value: 20, message: '最多20个字符' },
                  })}
                />
                <span className="absolute top-1/2 -translate-y-1/2 right-2 text-muted-foreground text-sm">
                  {teamName.length}/20
                </span>
              </div>
              {errors.teamName && <p className="text-sm text-red-500">{errors.teamName.message}</p>}
            </div>
          </div>
        </ModalContent>
      </form>
    </FormProvider>
  )
}

export function useDisbandTeam() {
  const modal = useModal()

  return (options?: { onConfirm?: () => void }) =>
    modal({
      content: <DisbandTeamModal onConfirm={options?.onConfirm} />,
    })
}
