// 模糊转场组件
import React, { useMemo } from 'react'
import { ThreeCanvas, useVideoTexture } from '@remotion/three'
import { useRenderContext } from '../../render.context'
import { TransitionBaseProps } from '../../types'
import { VideoLayerContent } from '../layers'
import * as THREE from 'three'
import { EffectComposer } from '@react-three/postprocessing'
import { GaussianBlurPass } from 'postprocessing'

console.log(EffectComposer)
console.log(GaussianBlurPass)

// 模糊 Shader 材质
const BlurMaterial = {
  vertexShader: `
    varying vec2 vUv;

    void main() {
      vUv = uv;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,

  fragmentShader: `
    uniform sampler2D tDiffuse;
    uniform float uBlurAmount;
    uniform vec2 uResolution;
    varying vec2 vUv;

    void main() {
      vec2 texelSize = 1.0 / uResolution;
      vec4 color = vec4(0.0);

      // 高斯模糊采样
      float blurSize = uBlurAmount * 10.0;
      int samples = 9;

      for(int i = -4; i <= 4; i++) {
        for(int j = -4; j <= 4; j++) {
          vec2 offset = vec2(float(i), float(j)) * texelSize * blurSize;
          color += texture2D(tDiffuse, vUv + offset);
        }
      }

      color /= float(samples * samples);
      gl_FragColor = color;
    }
  `
}

// Three.js 模糊场景组件
const BlurScene: React.FC<{
  videoTexture: THREE.Texture
  blurAmount: number
  width: number
  height: number
}> = ({ videoTexture, blurAmount, width, height }) => {
  const material = useMemo(() => {
    return new THREE.ShaderMaterial({
      uniforms: {
        tDiffuse: { value: videoTexture },
        uBlurAmount: { value: blurAmount },
        uResolution: { value: new THREE.Vector2(width, height) }
      },
      vertexShader: BlurMaterial.vertexShader,
      fragmentShader: BlurMaterial.fragmentShader
    })
  }, [videoTexture, blurAmount, width, height])

  return (
    <mesh>
      <planeGeometry args={[2, 2]} />
      <primitive object={material} />
    </mesh>
  )
}

// 模糊视频场景组件
const BlurVideoScene: React.FC<{
  video: any
  blurAmount: number
  width: number
  height: number
}> = ({ video, blurAmount, width, height }) => {
  const videoTexture = useVideoTexture(video.localSrc || video.src)

  return (
    <>
      <ambientLight intensity={1} />
      <BlurScene
        videoTexture={videoTexture}
        blurAmount={blurAmount}
        width={width}
        height={height}
      />
    </>
  )
}

export const BlurTransition: React.FC<TransitionBaseProps> = ({
  fromVideo,
  toVideo,
  progress,
  overlay
}) => {
  console.log({ progress })
  const { playerMetadata: { width, height } } = useRenderContext()

  // 计算模糊强度和透明度
  const { fromBlur, fromOpacity, toBlur, toOpacity } = useMemo(() => {
    const maxBlur = overlay?.config?.intensity || 0.5 // 使用转场配置中的强度参数

    if (progress <= 0.5) {
      // 前半段：fromVideo 从清晰变模糊
      const halfProgress = progress * 2
      return {
        fromBlur: halfProgress * maxBlur,
        fromOpacity: 1,
        toBlur: maxBlur,
        toOpacity: 0
      }
    } else {
      // 后半段：toVideo 从模糊变清晰
      const halfProgress = (progress - 0.5) * 2
      return {
        fromBlur: maxBlur,
        fromOpacity: 1 - halfProgress,
        toBlur: maxBlur * (1 - halfProgress),
        toOpacity: halfProgress
      }
    }
  }, [progress, overlay?.config?.intensity])

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width,
    height,
    overflow: 'hidden',
  }

  const layerStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  }

  return (
    <div style={containerStyle} id={`blur-${fromVideo.id}-${toVideo.id}`}>
      {/* 源视频层 - 使用 Three.js 模糊效果 */}
      <div style={{ ...layerStyle, opacity: fromOpacity, zIndex: 1 }}>
        {fromBlur > 0.01 ? (
          <ThreeCanvas
            width={width}
            height={height}
            camera={{ position: [0, 0, 1] }}
            style={{ width: '100%', height: '100%' }}
          >
            <BlurVideoScene
              video={fromVideo}
              blurAmount={fromBlur}
              width={width}
              height={height}
            />
          </ThreeCanvas>
        ) : (
          <VideoLayerContent overlay={fromVideo} inTransition />
        )}
      </div>

      {/* 目标视频层 - 使用 Three.js 模糊效果 */}
      <div style={{ ...layerStyle, opacity: toOpacity, zIndex: 2 }}>
        {toBlur > 0.01 ? (
          <ThreeCanvas
            width={width}
            height={height}
            camera={{ position: [0, 0, 1] }}
            style={{ width: '100%', height: '100%' }}
          >
            <BlurVideoScene
              video={toVideo}
              blurAmount={toBlur}
              width={width}
              height={height}
            />
          </ThreeCanvas>
        ) : (
          <VideoLayerContent overlay={toVideo} inTransition />
        )}
      </div>
    </div>
  )
}
