import { useForm, UseFormReturn } from 'react-hook-form'
import { MIXCUT_PIPELINES, MixcutPipelines, MixcutPipelineSchemas } from '@app/shared/types/mixcut'
import { mapValues } from 'lodash'
import z from 'zod'

/**
 * 基于 zod schema 生成带有默认值的对象
 */
function generateDefaultValues(config: Record<MixcutRulesFormCategories, Array<MixcutPipelines>>) {
  function _generateFromSchema<T extends z.ZodTypeAny>(schema: T): z.infer<T> {
    if (schema instanceof z.ZodObject) {
      const shape = schema.shape
      const result: any = {}

      for (const [key, fieldSchema] of Object.entries(shape)) {
        result[key] = _generateFromSchema(fieldSchema as z.ZodTypeAny)
      }

      return result
    }

    if (schema instanceof z.ZodDefault) {
      return schema._def.defaultValue()
    }

    if (schema instanceof z.ZodOptional) {
      return undefined
    }

    if (schema instanceof z.ZodBoolean) {
      return false
    }

    if (schema instanceof z.ZodNumber) {
      return 0
    }

    if (schema instanceof z.ZodString) {
      return ''
    }

    if (schema instanceof z.ZodArray) {
      return []
    }

    // 对于其他类型，返回 undefined
    return undefined
  }

  return mapValues(config, pipelines => {
    return Object.fromEntries(
      pipelines.map(pipeline => {
        const schema = MixcutPipelineSchemas.schemaByPipeline[pipeline]
        const defaultValue = _generateFromSchema(schema)
        return [pipeline, defaultValue]
      })
    )
  })
}

export enum MixcutRulesFormCategories {
  // mixcutBaseRules = 'mixcutBaseRules',
  videoTransform = 'videoTransform',
  narrationText = 'narrationText',
  globalTextTransform = 'globalTextTransform',
  // globalBackgroundMusic = 'globalBackgroundMusic',
}

export type MixcutRulesFormValue = {
  [Category in MixcutRulesFormCategories]: Record<
    MixcutPipelines,
    {
      enabled: boolean,
      [property: string]: any
    }
  >
}

export const formItemsByCategory = {
  [MixcutRulesFormCategories.videoTransform]: [
    MIXCUT_PIPELINES.video.rotation,
    MIXCUT_PIPELINES.video.scale,
    MIXCUT_PIPELINES.video.positionOffset,
    MIXCUT_PIPELINES.video.trim,
    MIXCUT_PIPELINES.video.flip,
    MIXCUT_PIPELINES.video.smartClip,
    MIXCUT_PIPELINES.video.speed,
    MIXCUT_PIPELINES.video.masking,
  ] as const,

  [MixcutRulesFormCategories.narrationText]: [
    MIXCUT_PIPELINES.narrationText.positionOffset,
    MIXCUT_PIPELINES.narrationText.fontFamily,
    MIXCUT_PIPELINES.narrationText.styledText,
  ] as const,

  [MixcutRulesFormCategories.globalTextTransform]: [
    MIXCUT_PIPELINES.globalText.positionOffset,
    MIXCUT_PIPELINES.globalText.fontFamily,
    MIXCUT_PIPELINES.globalText.styledText,
  ] as const
}

export type MixcutRulesForm = UseFormReturn<MixcutRulesFormValue>

export const useMixcutRulesForm = () => {
  return useForm<MixcutRulesFormValue>({
    defaultValues: generateDefaultValues(formItemsByCategory as any)
  })
}
