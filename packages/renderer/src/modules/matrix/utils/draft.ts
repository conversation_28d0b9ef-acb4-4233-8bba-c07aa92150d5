import { PlatformKey } from '@/libs/request/api/generic-matrix'
import { PlatformDraftMap, DraftInfo } from '@/modules/matrix/types/shared/draft'
import { PublishMode, VideoMountMode, PublishSetting, PublishTimeType } from '../enums/shared'
import { DySubmitForm } from '../schemas/platforms/dy.schema'
import { XhsSubmitForm } from '../schemas/platforms/xhs.schema'
import { PlatformSchema, PublishData } from '../schemas/publish.schema'
import { TimeSetting } from '../schemas/shared.schema'

type PlatformTransformer<K extends PlatformKey> = (
  draft: DraftInfo[`${K}Draft`]
) => PlatformSchema[K]

const defaultTimeSetting = (timeSetting?: Partial<TimeSetting>) => ({
  publishTime: timeSetting?.publishTime ?? Date.now(),
  period: timeSetting?.period ?? 0,
  periodType: timeSetting?.periodType ?? [],
  loopDays: timeSetting?.loopDays ?? [Date.now()],
  loopTime: timeSetting?.loopTime ?? '08:00',
  numEachDay: timeSetting?.numEachDay ?? 1,
})

const platformTransformers: {  [K in PlatformKey]: PlatformTransformer<K> } = {
  [PlatformKey.dy]: draft => {
    return {
      ...draft!,
      // 抖音特有字段
      titles: draft?.titles?.map((t: string) => ({ title: t })) ?? [],
      
      // 必填枚举字段
      publishMode: draft?.publishMode ?? PublishMode.CHECK_IN,
      mountType: draft?.mountType ?? VideoMountMode.NONE,
      setting: draft?.setting ?? PublishSetting.ONE_ACCOUNT_ONE_VIDEO,
      timeType: draft?.timeType ?? PublishTimeType.IMMEDIATE,
      
      timeSetting: defaultTimeSetting(draft?.timeSetting),
      //TODO: accountProducts会在后续被覆盖， 应该考虑抽出到公共部分
      accountProducts: undefined,

      poi: draft?.poi ?? undefined,
    }
  },
  [PlatformKey.xhs]: draft => {
    return {
      ...draft!,
      setting: draft?.setting ?? PublishSetting.ONE_ACCOUNT_ONE_VIDEO,
      timeType: draft?.timeType ?? PublishTimeType.IMMEDIATE,
      timeSetting: defaultTimeSetting(draft?.timeSetting),
      //TODO: accountProducts会在后续被覆盖， 应该考虑抽出到公共部分
      accountProducts: undefined,
      poi: draft?.poi ?? undefined,
    }
  },
  [PlatformKey.ks]: draft => {
    return {
      ...draft!,
      setting: draft?.setting ?? PublishSetting.ONE_ACCOUNT_ONE_VIDEO,
      timeType: draft?.timeType ?? PublishTimeType.IMMEDIATE,
      timeSetting: defaultTimeSetting(draft?.timeSetting),
      //TODO: accountProducts会在后续被覆盖， 应该考虑抽出到公共部分
      accountProducts: undefined,
      poi: draft?.poi ?? undefined,
    }
  }
}

const draftKeys = (Object.values(PlatformKey) as string[]).map(
  value => `${value}Draft` as keyof PlatformDraftMap
)
const getFilledDrafts = (info: DraftInfo) => {
  const result: Partial<Record<keyof PlatformDraftMap, boolean>> = {}
 
  draftKeys.forEach(key => {
    const value = info[key] || {}
    result[key] = value !== null && Object.values(value).some(v => v !== null)
  })

  return result
}

const draftKeyToPlatformKey = (key: string): keyof PlatformKey => key.replace('Draft', '') as keyof PlatformKey

export const recoverFormFromDraft = (rootData: DraftInfo ) => {
  const filledDrafts = getFilledDrafts(rootData)

  const _platforms = Object.entries(filledDrafts).reduce((acc, [draftKey, filled]) => {
    if (filled) {
      const platformKey = draftKey.replace('Draft', '') as PlatformKey
      const draft: DySubmitForm | XhsSubmitForm = rootData[draftKey]

      if (draft) {
        const accountProducts = draft.accountProducts?.map(obj => {
          const accountInfo = draft.accountList.find( account => account.id === obj.accountId)!
          return { accountInfo, products: obj.products }
        })

        // 平台特定数据
        const platformData = platformTransformers[platformKey](draft as any)

        acc[platformKey] = {
          ...platformData,
          accountProducts,
        }
      }
    }
    return acc
  }, {} as Record<string, any>) 

  const selectedPlatforms = Object.entries(filledDrafts)
    .filter(([_, value]) => value === true)
    .map(([key]) => draftKeyToPlatformKey(key) as PlatformKey)

  const restoredData: PublishData = {
    channels: {
      selected: selectedPlatforms,
      active: selectedPlatforms?.[0] ?? PlatformKey.dy
    },
    common: {
      name: rootData.name || '',
      videoList: rootData.videoList || [],
    },
    platforms: _platforms,
  }
  return restoredData
}

// export const recoverFormFromTask = async (platform: PlatformKey, platId: string): Promise<PublishData> => {
//   const detail = await GenericMatrixModule.endpoints.taskDetail(platform, parseInt(platId))

//   const taskAsDraft = {
//     ...detail,
//      accountProducts : detail.accountProducts?.map(obj => {
//           const accountInfo = detail.accountList.find( account => account.id === obj.accountId)!
//           return { accountInfo, products: obj.products }
//         })

//   }

//   const platformData = platformTransformers[platform](taskAsDraft as any)

//   const accountProducts = detail.accountProducts?.map(obj => ({
//     accountInfo: obj.accountInfo,
//     products: obj.products
//   }))

//   const restoredData: PublishData = {
//     channels: {
//       selected: [platform],
//       active: platform
//     },
//     common: {
//       name: detail.name || '',
//       videoList: detail.videoList || [],
//     },
//     platforms: {
//       [platform]: {
//         ...platformData,
//         accountProducts,
//       }
//     },
//   }

//   return restoredData
// }