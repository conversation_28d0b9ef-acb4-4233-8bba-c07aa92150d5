import { useCallback, useRef } from 'react'
import {
  Overlay,
  OverlayType,
  SoundOverlay,
  StickerOverlay,
  TextOverlay,
  TransitionOverlay,
  TransitionType
} from '@app/shared/types/overlay'
import { useEditorContext } from '../../contexts/editor/context'
import { DEFAULT_OVERLAY, FPS, PIXELS_PER_FRAME, TEXT_DEFAULT_CLOUD_FONT_SRC } from '@/modules/video-editor/constants'
import { calculateTracksAfterOverlayUpdated, generateNewOverlayId } from '@/modules/video-editor/utils/track-helper'
import { IndexableTrack, PlayerDimensions, Track } from '../../types'
import {
  DroppableResource,
  OverlaysAdjustment,
  ResourceMeta,
  useTimelineContext,
  useTimelineDnd
} from '@/modules/video-editor/contexts'
import { buildOverlayUpdates, calculateDraggableStateForMoving, snapToGrid } from '../../contexts/timeline-dnd/utils'
import { cacheManager } from '@/libs/cache/cache-manager'
import { ResourceCacheType } from '@app/shared/types/resource-cache.types'
import { toast } from 'react-toastify'
import { calculateTextRenderInfo } from '@clipnest/overlay-renderer'
import { MaterialResource } from '@/types/resources'
import { buildTextOverlay } from '@/modules/video-editor/utils/text'
import { ResourcePlugins } from '@/modules/video-editor/resource-plugin-system'

interface ResourceDragInfo {
  isActive: boolean
  resource: DroppableResource & {
    meta: ResourceMeta
  }
  initialMouseX: number

  newOverlay: Overlay
  targetTrack: IndexableTrack | null
}

export interface ResourceDragHook {
  handleResourceDragStart(resource: DroppableResource, initialMouseX: number): void
  handleResourceDragMove(currentMouseX: number, targetTrack?: IndexableTrack): void
  handleResourceDragEnd(): void
}

const mapMediaTypeToOverlayType: Partial<Record<MaterialResource.MediaType, OverlayType>> = {
  [MaterialResource.MediaType.VIDEO]: OverlayType.VIDEO,
  [MaterialResource.MediaType.IMAGE]: OverlayType.STICKER,
  [MaterialResource.MediaType.AUDIO]: OverlayType.SOUND,
}

const mapMediaTypeToResourceType: Partial<Record<MaterialResource.MediaType, ResourceCacheType>> = {
  [MaterialResource.MediaType.VIDEO]: ResourceCacheType.VIDEO,
  [MaterialResource.MediaType.IMAGE]: ResourceCacheType.PASTER,
  [MaterialResource.MediaType.AUDIO]: ResourceCacheType.SOUND,
}

function materialToOverlay(
  media: MaterialResource.Media,
  from: number,
  tracks: Track[],
  dimension: PlayerDimensions
) {
  // 将秒转换为帧数
  const durationInFrames = snapToGrid((media.duration || 3000) / 1000 * FPS)

  // 根据资源类型确定 Overlay 类型
  const overlayType = media.resType !== undefined ? mapMediaTypeToOverlayType[media.resType] : undefined

  if (!overlayType) {
    return null
  }

  const { width: resourceWidth = dimension.playerWidth, height: resourceHeight = dimension.playerHeight } = media
  const scaleX = dimension.playerWidth / resourceWidth!
  const scaleY = dimension.playerHeight / resourceHeight!

  // 视频填满画幅, 其他类型则限制尺寸在画幅内
  const usingScale = overlayType === OverlayType.VIDEO
    ? Math.max(scaleX, scaleY)
    : Math.min(scaleX, scaleY)

  const width = Math.round(resourceWidth! * usingScale)
  const height = Math.round(resourceHeight! * usingScale)

  return {
    ...DEFAULT_OVERLAY,
    width,
    height,
    left: (dimension.playerWidth - width) / 2,
    top: (dimension.playerHeight - height) / 2,
    id: generateNewOverlayId(tracks),
    type: overlayType,
    from,
    durationInFrames,
    originalDurationFrames: durationInFrames,
    originalMeta: {
      width: media.width,
      height: media.height,
      tileUrl: media.trackFrame,
      coverUrl: media.cover,
    },
    content: media.fileName,
    src: media.url || '',
    styles: {
      ...(overlayType === OverlayType.SOUND && { volume: 1 }),
      ...(overlayType === OverlayType.VIDEO && { volume: 0 }),
    }
  } as Overlay
}

/**
 * 获取资源元数据
 */
export function getResourceMeta(resource: DroppableResource): ResourceMeta | undefined {
  if (resource.sourcePlugin === ResourcePlugins.MATERIAL_LIB) {
    const type = resource.data.resType !== undefined ? mapMediaTypeToResourceType[resource.data.resType] : undefined

    return type
      ? {
        type,
        url: resource.data.url,
        filename: resource.data.fileName
      }
      : undefined
  }

  if (resource.sourcePlugin === ResourcePlugins.STICKER) {
    return {
      type: ResourceCacheType.PASTER,
      url: resource.data.content.fileUrl,
    }
  }

  if (resource.sourcePlugin === ResourcePlugins.SOUND_EFFECT) {
    const url = resource.data.content.itemUrl
    const hasSuffix = url.split('/').pop()?.includes('.')

    return {
      type: hasSuffix ? ResourceCacheType.SOUND : ResourceCacheType.SUFFIXLESS_SOUND,
      url: resource.data.content.itemUrl,
      customExt: !hasSuffix ? '.mp3' : undefined
    }
  }

  if (resource.sourcePlugin === ResourcePlugins.TEXT) {
    return {
      type: ResourceCacheType.FONT,
      url: resource.data?.content.fontPath || TEXT_DEFAULT_CLOUD_FONT_SRC,
    }
  }

  if (resource.sourcePlugin === ResourcePlugins.TRANSITION) {
    // 转场效果不需要缓存资源，返回一个虚拟的元数据
    return {
      type: ResourceCacheType.PASTER, // 使用一个现有的类型作为占位
    }
  }
}

/**
 * 从 DroppableResource 构造临时 Overlay 对象用于拖拽预览
 */
function createOverlayFromResource(
  resource: DroppableResource,
  tracks: Track[],
  dimension: PlayerDimensions,
  from: number = 0
): Overlay | null {
  if (resource.sourcePlugin === ResourcePlugins.MATERIAL_LIB) {
    if ('resType' in resource.data && resource.data.resType === MaterialResource.MediaType.FOLDER) return null
    return materialToOverlay(resource.data, from, tracks, dimension)
  }

  if (resource.sourcePlugin === ResourcePlugins.STICKER) {
    const width = dimension.playerWidth / 2
    const height = resource.data.content.height / resource.data.content.height * width
    return {
      ...DEFAULT_OVERLAY,
      id: generateNewOverlayId(tracks),
      type: OverlayType.STICKER,
      from,
      durationInFrames: 90,
      content: resource.data.title,
      src: resource.data.content.fileUrl,
      width,
      height,
      left: (dimension.playerWidth - width) / 2,
      top: (dimension.playerHeight - height) / 2,
      styles: {}
    } satisfies StickerOverlay
  }

  if (resource.sourcePlugin === ResourcePlugins.SOUND_EFFECT) {
    const originalDurationFrames = resource.data.content.durationMsec / 1000 * FPS

    return {
      ...DEFAULT_OVERLAY,
      id: generateNewOverlayId(tracks),
      type: OverlayType.SOUND,
      from,
      originalDurationFrames,
      durationInFrames: originalDurationFrames,
      content: resource.data.title,
      src: resource.data.content.itemUrl,
      styles: {
        volume: 1,
      }
    } satisfies SoundOverlay
  }

  if (resource.sourcePlugin === ResourcePlugins.TEXT) {
    return {
      ...buildTextOverlay(resource.data, { textContent: '默认文字' }),
      id: generateNewOverlayId(tracks),
      from,
      width: dimension.playerWidth,
      height: dimension.playerHeight,
    } satisfies TextOverlay
  }

  if (resource.sourcePlugin === ResourcePlugins.TRANSITION) {
    return {
      ...DEFAULT_OVERLAY,
      type: OverlayType.TRANSITION,
      id: generateNewOverlayId(tracks),
      from,
      durationInFrames: FPS * 3, // 默认 1 秒转场时长
      prevStoryboardIndex: 0,
      config: {
        transitionType: resource.data?.type as TransitionType || TransitionType.DISSOLVE,
        easing: 'ease-in-out'
      }
    } satisfies TransitionOverlay
  }

  throw new Error(`Unknown resource type: ${(resource as any).resourceType}`)
}

/**
 * 资源区拖拽逻辑 Hook
 */
export const useResourceDrag = (): ResourceDragHook => {
  const { zoomScale, timelineGridRef } = useTimelineContext()
  const { tracks, getPlayerDimensions, updateTracks } = useEditorContext()

  const {
    dragInfoRef,
    setIsDragging,
    setMousePosition,
    setLandingPoint,
    setPreviewOverlaysAdjust,
    resetDragState,
    updateDraggableState
  } = useTimelineDnd()

  const resourceDragInfo = useRef<ResourceDragInfo | null>(null)

  const handleResourceDragStart = useCallback(
    (resource: DroppableResource, initialMouseX: number) => {
      const meta = getResourceMeta(resource)

      if (!meta) {
        if (
          resource.sourcePlugin === ResourcePlugins.MATERIAL_LIB
          && 'resType' in resource.data && resource.data.resType === MaterialResource.MediaType.FOLDER
        ) {
          resourceDragInfo.current = null
          return
        }

        resourceDragInfo.current = null
        toast('无效的资源类型')
        return
      }

      const newOverlay = createOverlayFromResource(resource, tracks, getPlayerDimensions())
      if (!newOverlay) {
        toast('无效的资源类型')
        return
      }

      resourceDragInfo.current = {
        isActive: true,
        resource: { ...resource, meta },
        initialMouseX,
        newOverlay,
        targetTrack: null,
      }

      // 拖动开始时预下载资源, 避免拖动到轨道上时的卡顿
      if (meta.url) {
        if (meta.type === ResourceCacheType.FONT) {
          void cacheManager.font.cacheFont(meta.url)
        } else {
          void cacheManager.resource.cacheResource(
            meta.type,
            meta.url,
            undefined,
            meta.customExt || meta.filename?.split('.')?.pop()
          )
        }
      }

      setIsDragging(true)
      setMousePosition(null)
      setLandingPoint(null)
      setPreviewOverlaysAdjust(new OverlaysAdjustment())
    },
    [tracks, getPlayerDimensions]
  )

  const handleResourceDragMove = useCallback(
    (currentMouseX: number, targetTrack?: IndexableTrack) => {
      if (!resourceDragInfo.current || !timelineGridRef.current) return

      // 不在轨道上，清除预览
      if (!targetTrack) {
        setMousePosition(null)
        setLandingPoint(null)
        setPreviewOverlaysAdjust(new OverlaysAdjustment())
        resourceDragInfo.current = {
          ...resourceDragInfo.current,
          targetTrack: null
        }
        return
      }

      const rect = timelineGridRef.current.getBoundingClientRect()
      const targetStartFrame = snapToGrid((currentMouseX - rect.left) / zoomScale / PIXELS_PER_FRAME)
      const newOverlay: Overlay = {
        ...resourceDragInfo.current.newOverlay,
        from: targetStartFrame
      }

      if (!dragInfoRef.current) {
        dragInfoRef.current = {
          overlay: newOverlay,
          initialFrom: 0,
          initialRow: 0,
          initialDurationInFrames: newOverlay.durationInFrames
        }
      }
      dragInfoRef.current.currentRow = targetTrack.index
      dragInfoRef.current.currentFrom = targetStartFrame

      updateDraggableState(
        calculateDraggableStateForMoving(
          tracks,
          newOverlay,
          targetStartFrame,
          targetTrack,
        )
      )
      resourceDragInfo.current = {
        ...resourceDragInfo.current,
        targetTrack,
      }
    },
    [tracks, zoomScale]
  )

  const handleResourceDragEnd = useCallback(
    async () => {
      if (!resourceDragInfo.current || !dragInfoRef.current) {
        return resetDragState()
      }

      const { draggableState, overlay: newOverlay } = dragInfoRef.current

      const { targetTrack, resource } = resourceDragInfo.current
      if (!resourceDragInfo.current || !draggableState || !targetTrack || !newOverlay || !resource) {
        return resetDragState()
      }

      const {
        adjustedDuration, adjustedStartFrame, targetStoryboardIndex, draggable, overlaysAdjust
      } = draggableState

      if (!draggable) return resetDragState()

      const localSrc = resource && resource.meta?.url
        ? await toast.promise(
          cacheManager.resource.waitForCachedResource(resource.meta.type, resource.meta.url),
          {
            pending: '资源加载中...',
          }
        )
        : undefined

      if (resource.meta?.url && !localSrc) {
        toast('资源加载失败')
        return resetDragState()
      }

      const finalOverlay: Overlay = {
        ...newOverlay,
        localSrc,
        from: adjustedStartFrame ?? newOverlay.from,
        durationInFrames: adjustedDuration ?? newOverlay.durationInFrames,
        ...(targetStoryboardIndex !== undefined && { storyboardIndex: targetStoryboardIndex })
      }

      if (resource.meta.type === ResourceCacheType.FONT && resource.meta.url) {
        const font = await cacheManager.font.cacheFont(resource.meta.url)

        if (!font) throw new Error('字体加载失败')

        const dimension = getPlayerDimensions()
        const { minWidth, minHeight } = calculateTextRenderInfo(font, finalOverlay as TextOverlay)
        finalOverlay.width = minWidth
        finalOverlay.height = minHeight
        finalOverlay.left = (dimension.playerWidth - minWidth) / 2
        finalOverlay.top = (dimension.playerHeight - minHeight) / 2
      }

      const updates = buildOverlayUpdates(tracks, overlaysAdjust)

      updateTracks(prevTracks => {
        const inserted = prevTracks.map((track, trackIndex) => {
          if (trackIndex === targetTrack.index) {
            return {
              ...track,
              overlays: [...track.overlays, finalOverlay]
            }
          }

          return track
        })

        return updates.reduce(
          (result, update) => {
            const { targetTrackIndex, ...updatedOverlay } = update
            return calculateTracksAfterOverlayUpdated(result, updatedOverlay, targetTrackIndex)
          },
          inserted
        )
      })

      return resetDragState()
    },
    [tracks, updateTracks, getPlayerDimensions]
  )

  return {
    handleResourceDragStart,
    handleResourceDragMove,
    handleResourceDragEnd
  }
}
