import React, { useState } from 'react'
import { ColorSlider } from '@/components/ui/color-slider'
import { cn } from '@/components/lib/utils'
import { VideoOverlay } from '@app/shared/types/overlay'
import { useOverlayEditing } from '@/modules/video-editor/contexts'
import { FilterHelper } from '../../../../../utils/filter-helper'

interface ColorCircleProps {
  color: string
  isSelected: boolean
  onClick: () => void
}

/**
 * 颜色圆圈组件
 * 用于选择预设颜色
 */
const ColorCircle: React.FC<ColorCircleProps> = ({ color, isSelected, onClick }) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        'w-6 h-6 rounded-full transition-all',
        'hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-white/20',
        isSelected && 'ring-2 ring-white/50 ring-offset-2 ring-offset-gray-900'
      )}
      style={{ backgroundColor: color }}
    />
  )
}

/**
 * 预设颜色数组
 */
const PRESET_COLORS = [
  '#FF0000', // Red
  '#FF8000', // Orange
  '#FFFF00', // Yellow
  '#00FF00', // Green
  '#00FFFF', // Cyan
  '#0000FF', // Blue
  '#8000FF', // Purple
  '#FF00FF', // Magenta
]

/**
 * 为每个预设颜色定义对应的渐变类
 */
const COLOR_GRADIENTS = {
  // 色相渐变：根据选中颜色调整饱和度和亮度的色相光谱
  hue: [
    'bg-gradient-to-r from-red-600 via-yellow-600 via-green-600 via-cyan-600 via-blue-600 via-purple-600 to-red-600',        // Red base
    'bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-cyan-500 via-blue-500 via-purple-500 to-red-500',        // Orange base
    'bg-gradient-to-r from-red-400 via-yellow-400 via-green-400 via-cyan-400 via-blue-400 via-purple-400 to-red-400',        // Yellow base
    'bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-cyan-500 via-blue-500 via-purple-500 to-red-500',        // Green base
    'bg-gradient-to-r from-red-400 via-yellow-400 via-green-400 via-cyan-400 via-blue-400 via-purple-400 to-red-400',        // Cyan base
    'bg-gradient-to-r from-red-600 via-yellow-600 via-green-600 via-cyan-600 via-blue-600 via-purple-600 to-red-600',        // Blue base
    'bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-cyan-500 via-blue-500 via-purple-500 to-red-500',        // Purple base
    'bg-gradient-to-r from-red-400 via-yellow-400 via-green-400 via-cyan-400 via-blue-400 via-purple-400 to-red-400',        // Magenta base
  ],
  // 饱和度渐变：从灰色到对应颜色
  saturation: [
    'bg-gradient-to-r from-gray-400 to-red-500',      // Red
    'bg-gradient-to-r from-gray-400 to-orange-500',   // Orange
    'bg-gradient-to-r from-gray-400 to-yellow-500',   // Yellow
    'bg-gradient-to-r from-gray-400 to-green-500',    // Green
    'bg-gradient-to-r from-gray-400 to-cyan-500',     // Cyan
    'bg-gradient-to-r from-gray-400 to-blue-500',     // Blue
    'bg-gradient-to-r from-gray-400 to-purple-500',   // Purple
    'bg-gradient-to-r from-gray-400 to-pink-500',     // Magenta
  ],
  // 亮度渐变：从黑色通过对应颜色到白色
  lightness: [
    'bg-gradient-to-r from-black via-red-500 to-white',      // Red
    'bg-gradient-to-r from-black via-orange-500 to-white',   // Orange
    'bg-gradient-to-r from-black via-yellow-500 to-white',   // Yellow
    'bg-gradient-to-r from-black via-green-500 to-white',    // Green
    'bg-gradient-to-r from-black via-cyan-500 to-white',     // Cyan
    'bg-gradient-to-r from-black via-blue-500 to-white',     // Blue
    'bg-gradient-to-r from-black via-purple-500 to-white',   // Purple
    'bg-gradient-to-r from-black via-pink-500 to-white',     // Magenta
  ]
}

export function HSLSetting() {
  const { localOverlay: videoOverlay, updateEditingOverlay } = useOverlayEditing<VideoOverlay>()

  const currentFilter = videoOverlay?.styles?.filter || ''

  const getFilterValue = (filterName: string, defaultValue: number = 100) => {
    return FilterHelper.extractFilterValue(currentFilter, filterName, defaultValue)
  }

  const updateFilterValue = (filterName: string, value: number, unit: string = '%', commit?: boolean) => {
    const newFilter = FilterHelper.updateFilter(currentFilter, filterName, value, unit)
    updateEditingOverlay({ styles: { filter: newFilter } }, commit)
  }

  const updateMultipleFilters = (updates: Array<{ filterName: string; value: number; unit?: string }>) => {
    const newFilter = FilterHelper.updateMultipleFilters(currentFilter, updates)
    updateEditingOverlay({ styles: { filter: newFilter } }, true)
  }

  // 本地状态
  const [selectedColor, setSelectedColor] = useState(0)

  // 从滤镜中获取当前值
  const hue = getFilterValue('hue-rotate', 0)
  const saturation = getFilterValue('saturate', 100)
  const lightness = getFilterValue('brightness', 100)

  return (
    <div className="p-4 space-y-6 bg-background text-foreground">
      {/* 颜色预设 */}
      <div className="overlay-setting-card">
        <div className="text-sm font-medium mb-3">HSL基础</div>
        <div className="flex gap-3">
          {PRESET_COLORS.map((color, index) => (
            <ColorCircle
              key={color}
              color={color}
              isSelected={selectedColor === index}
              onClick={() => {
                setSelectedColor(index)
                updateMultipleFilters([
                  { filterName: 'hue-rotate', value: 0, unit: 'deg' },
                  { filterName: 'saturate', value: 100 },
                  { filterName: 'brightness', value: 100 }
                ])
              }}
            />
          ))}
        </div>
      </div>

      {/* HSL 滑块控制 */}
      <div className="overlay-setting-card">
        <div className="text-sm font-medium mb-3">HSL 调整</div>
        <div className="space-y-4">
          {/* 色相 */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>色相</span>
              <span>{hue}°</span>
            </div>
            <ColorSlider
              value={hue}
              onChange={(value, commit) => updateFilterValue('hue-rotate', value, 'deg', commit)}
              max={360}
              trackClassName={COLOR_GRADIENTS.hue[selectedColor]}
            />
          </div>

          {/* 饱和度 */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>饱和度</span>
              <span>{saturation}%</span>
            </div>
            <ColorSlider
              value={saturation}
              onChange={(value, commit) => updateFilterValue('saturate', value, '%', commit)}
              max={200}
              trackClassName={COLOR_GRADIENTS.saturation[selectedColor]}
            />
          </div>

          {/* 亮度 */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>亮度</span>
              <span>{lightness}%</span>
            </div>
            <ColorSlider
              value={lightness}
              onChange={(value, commit) => updateFilterValue('brightness', value, '%', commit)}
              max={200}
              trackClassName={COLOR_GRADIENTS.lightness[selectedColor]}
            />
          </div>
        </div>
      </div>

      {/* 重置按钮 */}
      <div className="flex justify-end">
        <button
          onClick={() => {
            updateMultipleFilters([
              { filterName: 'hue-rotate', value: 0, unit: 'deg' },
              { filterName: 'saturate', value: 100 },
              { filterName: 'brightness', value: 100 }
            ])
          }}
          className="px-3 py-1.5 text-sm bg-primary/10 text-primary hover:bg-primary/20 rounded-md transition-colors"
        >
          重置
        </button>
      </div>
    </div>
  )
}
