import z from 'zod'
import { accountFormSchema, commonAccountProductSchema, commonPoiSchema, commonPublishPlanSchema, publishSettingSchema, publishTimeTypeSchema, timeSettingSchema, titleDescSchema, videoMountModeSchema } from '../shared.schema'
import { buildDetailDO } from '../../utils/shared'
import { timeSettingMap } from '../../constants/shared'
import { PlatformKey } from '@/libs/request/api/generic-matrix'

export const ksFormSchema = z.object({
  mountType: videoMountModeSchema,
  setting: publishSettingSchema,
  timeType: publishTimeTypeSchema,
  poi: commonPoiSchema.optional(),
  timeSetting: timeSettingSchema.optional(),
  
  titles: z.array(titleDescSchema).optional(),
  accountList: z.array(accountFormSchema).min(1, '至少选择一个发布账号'),
  accountProducts: z.array(commonAccountProductSchema).optional(),
})

export const ksMergedFormSchema = ksFormSchema.merge(commonPublishPlanSchema)

export const ksSubmitSchema = ksMergedFormSchema.transform(form => ({
  ...form,
  timeSetting: timeSettingMap[form.timeType]?.(form.timeSetting) ?? {},
  accountIds: form.accountList.map(a => a.id),
  accountProducts: form.accountProducts?.map(ap => ({
    accountId: ap.accountInfo.id,
    products: ap.products
  })),
  totalAccount: form.accountList.length,
  totalVideo: form.videoList.length,
  detailDOS: buildDetailDO(PlatformKey.ks, form)
}))

export type KsMergedForm = z.infer<typeof ksMergedFormSchema>
export type KsSubmitForm = z.infer<typeof ksSubmitSchema>

