import React, { useState, forwardRef, useImperative<PERSON><PERSON><PERSON>, useRef } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { RefreshCw, Eye } from 'lucide-react'
import { Controller, useFormContext } from 'react-hook-form'
import { PublishData, PublishPayload } from '@/modules/matrix/schemas/publish.schema'
import { buildPublishRequest, transformPublishDataToSubmitMap } from '@/modules/matrix/utils/shared'
import { Avatar, AvatarImage, AvatarFallback } from '@radix-ui/react-avatar'
import { AuthedImgByObjectId } from '@/components/authed-img'
import { PlatformKey } from '@/libs/request/api/generic-matrix'
import { PLATFORM_CONFIG } from '@/modules/matrix/constants/shared'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { cn } from '@/components/lib/utils'
import { AggregatedPreview } from '@/modules/matrix/schemas/preview.schema'
import { flatMap, groupBy, mapValues } from 'lodash'
import useRequest from '@/hooks/useRequest'

const generateDetailId = (url: string, index: number) => {
  return `${url}-${index}`
}

// 组件 ref 接口
export interface PreviewPublishDialogRef {
  open: () => void
  close: () => void
}

interface PreviewPublishDialogProps {
  onConfirm?: (data: any) => void
}

const buildAggregatedPreview = (data: PublishPayload): AggregatedPreview[] => {
  const platforms = Object.values(PlatformKey)
  const videoMap = Object.fromEntries(data.videoList.map(v => [v.url, v]))

  const resultMap = new Map<string, AggregatedPreview>()

  platforms.forEach(platform => {
    const platformData = data[platform]
    if (!platformData) return

    const { detailDOS, accountList } = platformData
    detailDOS.forEach(d => {
      const video = videoMap[d.url]
      const account = accountList.find(a => a.id === d.accountId)
      if (!video || !account) return

      let videoAgg = resultMap.get(video.url)
      if (!videoAgg) {
        videoAgg = {
          videoUrl: video.url,
          videoCover: video.cover,
          aggAccounts: [],
        }
        resultMap.set(video.url, videoAgg)
      }

      videoAgg.aggAccounts.push({
        platform,
        id: account.id,
        nickname: account.nickname,
        avatar: account.avatar,
        title: d.title,
        videoUrl: d.url,
        videoCover: d.cover
      })
    })
  })

  return Array.from(resultMap.values())
}

export const PreviewPublishDialog = forwardRef<PreviewPublishDialogRef, PreviewPublishDialogProps>(
  ({ onConfirm }, ref) => {
    const [open, setOpen] = useState(false)
    const { getValues, watch, control, setValue } = useFormContext<PublishData>()

    const [selectedDetailIds, setSelectedDetailIds] = useState<Set<string>>(new Set())
    const [previewData, setPreviewData] = useState<AggregatedPreview[]>()
    const [latestPublishPayload, setLatestPublishPayload] = useState<PublishPayload>()

    const detailIdMapRef = useRef<Map<string, AggregatedPreview>>(new Map())
  
    const planName = watch('common.name')

    const generatePreviewData = () => {
      const formValues = getValues()
      const data: PublishPayload = {
        name: formValues.common.name,
        videoList: formValues.common.videoList,
        ...transformPublishDataToSubmitMap(formValues),
      }
      setLatestPublishPayload(data)
      
      const preview = buildAggregatedPreview(data)

      // 构建 detailId 映射表
      const map = new Map<string, AggregatedPreview>()
      preview.forEach((item, videoIndex) => {
        const detailId = generateDetailId( item.videoUrl, videoIndex)
        map.set(detailId, item)
      })
      detailIdMapRef.current = map

      return preview
    }

    const handleOpen = () => {
      const _previewData = generatePreviewData()
      setPreviewData(_previewData)
      setOpen(true)
    }

    const handleRegenerate = () => {
      const _previewData = generatePreviewData()
      setPreviewData(_previewData)
    }

    const submitPublishMutation = useRequest(
      (data: PublishPayload) => buildPublishRequest(data),
      {
        actionName: '发布任务',        
      })

    const handleSelectAll = () => {
      if (!previewData) return
      const allIds = previewData.map((item, idx) => generateDetailId(item.videoUrl, idx))
      setValue('previewSelectedIds', allIds)
    }

    const handleUnselectAll = () => {
      setValue('previewSelectedIds', [])
    }

    const handleConfirm = () => {
      const formValues = getValues()

      const selectedIds = getValues('previewSelectedIds') || []
      const selectedData = selectedIds
        .map(id => detailIdMapRef.current.get(id))
        .filter(Boolean)

      const groupedPayload = mapValues(
        groupBy(
          flatMap(selectedData, data =>
            data?.aggAccounts.map(agg => {
              const targetPayload = latestPublishPayload?.[agg.platform]
              const selectedDetailDOS = targetPayload?.detailDOS.filter(
                dos => dos.accountId === agg.id && dos.url === agg.videoUrl
              )

              return {
                platform: agg.platform,
                detailDOS: selectedDetailDOS,
              }
            })
          ),
          'platform' 
        ),
        group => group
      )
     
      const updatedPlatforms = Object.fromEntries(
        Object.entries(groupedPayload).map(([platform, value]) => {
          const targetPlatformForm = formValues.platforms[platform] || {}

          return [
            platform,
            {
              ...targetPlatformForm,
              detailDOS: flatMap(value, val => val?.detailDOS || []),
            }
          ]
        })
      )

      console.log({ updatedPlatforms })

      const result: PublishData = {
        ...formValues,
        channels: {
          selected: Object.keys(updatedPlatforms) as PlatformKey[],
          active: formValues.channels.active
        },
        platforms: updatedPlatforms
      }

      const submitData = {
        ...formValues.common,
        ...transformPublishDataToSubmitMap(result),
      }

      submitPublishMutation.mutate(submitData)

      onConfirm?.({})
      // setOpen(false)
    }

    // if (!previewData) return null

    const selectedCount = selectedDetailIds.size

    useImperativeHandle(ref, () => ({
      open: handleOpen,
      close: () => {
        setOpen(false)
        setSelectedDetailIds(new Set())
      }
    }), [])

    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-6xl w-[1200px] max-h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              发布预览 - {planName || '未命名计划'}
            </DialogTitle>
            <DialogDescription>
              预览即将发布的视频分配情况，您可以重新分配或选择部分内容进行发布
            </DialogDescription>
          </DialogHeader>

          <div className="flex items-center justify-between py-2">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRegenerate}
                className="flex items-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                重新分配
              </Button>

              <div className="flex items-center gap-2">
                <Button size="sm" onClick={handleSelectAll}>全选</Button>
                <Button size="sm" onClick={handleUnselectAll}>取消全选</Button>
                <span className="text-sm">已选择 {getValues('previewSelectedIds')?.length || 0}</span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* <Badge variant="secondary">
                已选择 {selectedCount}/{totalCount}
              </Badge>
              <Badge variant="outline">
                {previewData.setting === PublishSetting.ONE_ACCOUNT_ONE_VIDEO ? '一账号一视频' : '一账号多视频'}
              </Badge> */}
            </div>
          </div>

          <Separator />

          <ScrollArea className="h-[500px]" >
            <div className="flex flex-wrap gap-3 p-2">
              {
                previewData?.map((item, index) => {
                  const key = generateDetailId( item.videoUrl, index)

                  return (
                    <Controller
                      key={key}
                      name="previewSelectedIds"
                      control={control}
                      render={({ field }) => {
                        const checked = field.value?.includes(key) || false

                        const onChange = (val: boolean) => {
                          const newValue = new Set(field.value || [])
                          if (val) newValue.add(key)
                          else newValue.delete(key)
                          field.onChange(Array.from(newValue))
                        }

                        return (
                          <Label 
                            htmlFor={key}
                            className={cn(
                              checked && 'ring-1 ring-neutral-500',
                              'rounded w-46 cursor-pointer bg-neutral-900 border p-3 shadow-md flex flex-col items-center justify-center gap-2'
                            )}
                          >
                            <Checkbox id={key} checked={checked} onCheckedChange={onChange} className="hidden"  />

                            <AuthedImgByObjectId
                              src={item.videoCover}
                              alt="视频"
                              className="w-full aspect-[9/16] object-cover rounded"
                            />
                    
                            <div className="flex flex-col gap-2  w-full">
                              {
                                item.aggAccounts.map(account => (
                                  <div key={account.id} className="flex gap-3 items-center">
                                    <div className="relative">
                                      <img className="w-3 h-3 absolute right-0 bottom-0 rounded" src={PLATFORM_CONFIG[account.platform].icon} />
                                      <Avatar >
                                        <AvatarImage className="w-7 h-7 rounded" src={account.avatar} />
                                        <AvatarFallback>{account.nickname?.charAt(0) || 'A'}</AvatarFallback>
                                      </Avatar>
                                    </div>
                                    <div className="flex flex-col ">
                                      <span className="text-sm font-medium">{account.nickname}</span>
                                      <span className="text-xs text-gray-500">
                                        {account.title}
                                      </span>
                                    </div>
                                  </div>
                                ))
                              }
                            </div>
                          </Label>
                        )
                      }}
                    />
                  )
                })
              }
            </div>
          </ScrollArea>

          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button onClick={handleConfirm}>
              确认发布 ({selectedCount})
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }
)
