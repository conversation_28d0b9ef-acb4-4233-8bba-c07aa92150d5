import { TooltipProvider, Toolt<PERSON>, Toolt<PERSON>Trigger, TooltipContent } from '@radix-ui/react-tooltip'
import { Info } from 'lucide-react'
import React from 'react'

export const LoopTimeTypeTooltip = () => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger type="button">
          <Info className="w-4 h-4" />
        </TooltipTrigger>
        <TooltipContent className="bg-neutral-900 p-3 rounded">
          <div className="space-y-2">
            <h4 className="font-medium">循环定时发布限制</h4>
            <p className="text-sm text-muted-foreground">
              • 最大支持14天内日期选择<br />
              • 最多可选择14天<br />
              • 超出范围的日期将无法选择
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}