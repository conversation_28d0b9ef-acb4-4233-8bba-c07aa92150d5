/**
 * 除口播轨道以外, 其他任意类型轨道的高度
 */
export const TIMELINE_TRACK_HEIGHT = 32

export const VIDEO_TRACK_HEIGHT = 48

/**
 * 口播轨道的高度
 */
export const NARRATOR_TIMELINE_TRACK_HEIGHT = TIMELINE_TRACK_HEIGHT * 1.75

/**
 * 在时间轴上渲染 Overlay 时, 每帧对应的像素数
 * TODO: 是否应该屏幕像素宽度、系统设置的缩放比例等外部参数做调整？
 */
export const PIXELS_PER_FRAME = 1.5

export const TIMELINE_GRID_GAP_PX = 8

export const TimelineZIndices = {
  draggingItem: 1,
  transitionItemDelta: 5,
  normalItem: 30,
  activeItem: 40,
  volumeIcon: 99
}
