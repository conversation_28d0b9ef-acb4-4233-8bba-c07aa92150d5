import { MIXCUT_PIPELINES, MixcutPipelineSchemas } from '@app/shared/types/mixcut.js'
import { MixcutPipelineContext, SimpleMixcutPipeline } from '../mixcut-pipeline.types.js'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { TextOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'
import z from 'zod'
import { MixcutPipelineUtils } from '@/modules/mixcut/pipeline/mixcut-pipeline.utils.js'

export class NarrationTextStyledTextPipeline extends SimpleMixcutPipeline<typeof MIXCUT_PIPELINES.narrationText.styledText, TextOverlay> {

  private readonly schema = MixcutPipelineSchemas.schemaByPipeline[MIXCUT_PIPELINES.narrationText.styledText]

  private readonly config?: z.infer<typeof this.schema>['options'][number]

  constructor(
    context: MixcutPipelineContext<typeof MIXCUT_PIPELINES.narrationText.styledText>,
  ) {
    super(context)
    this.config = _.sample(context.params.options)
  }

  async processSingleOverlay(overlay: TextOverlay & MixcutableOverlay) {
    if (!this.config) return overlay

    return {
      ...overlay,
      styles: MixcutPipelineUtils.mergeStyles(overlay.styles, this.config)
    }
  }

  filter(overlay: MixcutableOverlay): overlay is TextOverlay & MixcutableOverlay {
    return SimpleMixcutPipeline.filters.narrationText(overlay)
  }
}
