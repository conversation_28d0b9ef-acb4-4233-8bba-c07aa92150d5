import React, { useEffect, useRef, useState } from 'react'
import { ChevronDown, ChevronRight, Ellipsis } from 'lucide-react'
import { MaterialResource, FolderActionKeys } from '@/types/resources'
import { cn } from '@/components/lib/utils'
import { createPortal } from 'react-dom'
import { toast } from 'react-toastify'
import { Checkbox } from '@/components/ui/checkbox'

export type TreeNode = {
  id: string
  label: string
  type?: string
  children?: TreeNode[]
  raw?: any
}

export type TreeAction = {
  icon: React.ReactNode
  label: string
  value: any
  onClick?: (nodeId: string, parentId?: string, label?: string) => void
}

interface TreeListProps {
  /**
   * 树形结构的数据
   */
  data: TreeNode[]
  /**
   * 是否全部展开，默认不展开
   */
  defaultExpandAll?: boolean
  /**
   * 外层样式
   */
  className?: string
  /**
   * 节点的可用操作
   */
  actions?: TreeAction[]
  /**
   * 节点右侧的图标，鼠标悬浮时弹出操作菜单，在actions有值时才显示
   * false(默认)：直接显示第一个 action 的按钮
   * true：是显示图标 (⋯)，鼠标悬浮时弹出操作菜单
   */
  showEllipsis?: boolean
  /**
   * 当前选中的节点 ID
   */
  selectedId?: string
  /**
   * 搜索关键字
   */
  keyword?: string
  /**
   * 选中节点样式
   */
  selectStyle?: string
  /**
   * 点击某个节点时触发，返回被选中的 TreeNode 对象
   */
  onSelect?: (node: TreeNode) => void

  multiple?: boolean // 是否多选模式
  selectedIds?: string[] // 多选模式下的受控属性
  onChangeSelection?: (nodes: TreeNode[]) => void // 多选模式下选中列表回调
}

/**
 * 转为树结构
 */
export function buildTreeFromFlatList(list: MaterialResource.Directory[]): TreeNode[] {
  const map = new Map<string, TreeNode>()
  const tree: TreeNode[] = []

  // 初始化 TreeNode 节点
  list.forEach(item => {
    map.set(item.folderId, {
      id: item.folderId,
      label: item.folderName,
      children: [],
      raw: item,
    })
  })

  list.forEach(item => {
    const node = map.get(item.folderId)!
    if (!item.parentId) {
      // 顶级节点
      tree.push(node)
    } else if (!map.has(item.parentId)) {
      // 父节点 ID 不存在 → 屏蔽该节点
    } else {
      const parent = map.get(item.parentId)
      parent?.children?.push(node)
    }
  })

  return tree
}

// 根据树结构找到某个节点的完整路径链
export const getPathChain = (tree: any[], targetId: string, path: any[] = []): any[] | null => {
  for (const node of tree) {
    const newPath = [...path, node]
    if (node.id === targetId) return newPath
    if (node.children) {
      const result = getPathChain(node.children, targetId, newPath)
      if (result) return result
    }
  }
  return null
}

// 获取节点信息
export const findNodeById = (tree: TreeNode[], targetId: string): TreeNode | null => {
  for (const node of tree) {
    if (node.id === targetId) return node
    if (node.children) {
      const found = findNodeById(node.children, targetId)
      if (found) return found
    }
  }
  return null
}

// 获取父节点信息
export const findParentNode = (list: TreeNode[], targetId: string, parent: TreeNode | null = null): TreeNode | null => {
  for (const node of list) {
    if (node.id === targetId) {
      return parent
    }
    if (node.children) {
      const result = findParentNode(node.children, targetId, node)
      if (result) return result
    }
  }
  return null
}

// 获取指定节点的同级节点列表
export const getSiblings = (tree: TreeNode[], targetId: string): TreeNode[] | null => {
  const parent = findParentNode(tree, targetId)
  if (parent) {
    // 排除自己，返回其他同级节点
    return parent.children?.filter(child => child.id !== targetId) || []
  } else {
    // 没有父节点，说明是顶级节点
    return tree.filter(node => node.id !== targetId)
  }
}

//当前节点是否有效
export const isValidFolderId = (tree: any[], id: string): boolean => {
  for (const node of tree) {
    if (node.id === id) return true
    if (node.children?.length && isValidFolderId(node.children, id)) return true
  }
  return false
}

// 获取某节点及其所有子节点的 id
const collectNodeIds = (node: TreeNode): string[] => {
  let ids: string[] = [node.id]
  if (node.children && node.children.length > 0) {
    node.children.forEach(child => {
      ids = ids.concat(collectNodeIds(child))
    })
  }
  return ids
}

// 递归计算节点选中状态（多层半选）
const getCheckboxState = (node: TreeNode, selectedIds: string[]): boolean | 'indeterminate' => {
  if (!node.children || node.children.length === 0) {
    return selectedIds.includes(node.id)
  }

  const childStates = node.children.map(child => getCheckboxState(child, selectedIds))
  const allSelected = childStates.every(state => state === true)
  const someSelected = childStates.some(state => state === true || state === 'indeterminate')

  if (allSelected) return true
  if (someSelected) return 'indeterminate'
  return false
}

// 处理节点点击
const handleSelectNode = (node: TreeNode, currentSelected: string[]): string[] => {
  const nodeIds = collectNodeIds(node)
  const childIds = nodeIds.filter(id => id !== node.id)

  let newSelected: string[] = []

  if (childIds.length === 0) {
    // 叶子节点：切换自身
    if (currentSelected.includes(node.id)) {
      newSelected = currentSelected.filter(id => id !== node.id)
    } else {
      newSelected = [...currentSelected, node.id]
    }
  } else {
    // 非叶子节点：根据子节点是否全部选中切换
    const allChildrenSelected = childIds.every(id => currentSelected.includes(id))
    if (allChildrenSelected) {
      // 全选 → 点击取消父子节点
      newSelected = currentSelected.filter(id => !nodeIds.includes(id))
    } else {
      // 未全选 → 选中父子节点
      newSelected = Array.from(new Set([...currentSelected, ...nodeIds]))
    }
  }

  return newSelected
}

const TreeItem: React.FC<{
  node: TreeNode
  parentId?: string
  actions?: TreeAction[]
  showEllipsis?: boolean
  selectedIds?: string[]
  defaultExpandAll?: boolean
  keyword?: string
  selectStyle?: string
  multiple?: boolean
  onSelect?: (node: TreeNode) => void
  onToggleSelect?: (node: TreeNode, ids: string[]) => void
}> = ({
  node,
  parentId,
  actions,
  showEllipsis,
  selectedIds,
  defaultExpandAll,
  keyword,
  selectStyle,
  multiple,
  onSelect,
  onToggleSelect,
}) => {
  const [open, setOpen] = useState(defaultExpandAll ?? false)
  const [popup, setPopup] = useState(false)
  const hasChildren = node.children && node.children.length > 0
  const baseClass = showEllipsis ? '' : 'min-w-[120px]'
  const hasActions = actions && actions.length > 0
  const ellipsisRef = useRef<HTMLSpanElement | null>(null)

  let checkboxState: boolean | 'indeterminate' = false
  if (multiple) {
    // 多选模式
    checkboxState = getCheckboxState(node, selectedIds ?? [])
  } else {
    // 单选模式
    checkboxState = selectedIds?.includes(node.id) ?? false
  }

  const isSelected = checkboxState === true

  useEffect(() => {
    if (keyword && node.children?.length) {
      const hasMatch = node.children.some(child => child.label.includes(keyword))
      if (hasMatch) setOpen(true)
    }
  }, [keyword, node.children])

  return (
    <div className="ml-4 relative">
      <div
        className={cn(
          'inline-flex items-center cursor-pointer select-none rounded px-2 py-1 mb-1 relative',
          isSelected ? selectStyle || 'bg-accent' : 'hover:bg-primary/20',
        )}
      >
        {hasChildren && (
          <span className="mr-1" onClick={() => hasChildren && setOpen(!open)}>
            {open ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
          </span>
        )}

        {/* 多选模式下显示复选框 */}
        {multiple && (
          <div className="">
            <Checkbox 
              className="mr-2 cursor-pointer rounded hover:shadow-md hover:scale-120 transition-all dark:data-[state=checked]:bg-primary-highlight1 dark:data-[state=checked]:border-primary-highlight1 data-[state=checked]:text-white"
              checked={checkboxState}
              onCheckedChange={() => {
                const newSelectedIds = handleSelectNode(node, selectedIds ?? [])
                onToggleSelect?.(node, newSelectedIds)
              }}
            />
          </div>
        )}

        <div
          className="flex items-center"
          onClick={() => {
            if (node.type !== 'disabled') {
              if (!multiple) {
                onSelect?.(node)
              }
            } else {
              toast('该节点不可以被选择', { type: 'error' })
            }
          }}
        >
          <span className={cn('whitespace-nowrap', baseClass)}>{node.label}</span>

          {hasActions && !node.type && (
            <>
              {!showEllipsis && actions && (
                <span
                  className="ml-2 cursor-pointer"
                  onClick={e => {
                    e.stopPropagation()
                    actions[0].onClick?.(node.id, parentId, node.label)
                  }}
                >
                  {actions[0].icon}
                </span>
              )}
              {showEllipsis && actions && (
                <span
                  ref={ellipsisRef}
                  className="ml-2 cursor-pointer pr-2"
                  onMouseEnter={() => setPopup(true)}
                  onMouseLeave={() => setPopup(false)}
                  onClick={e => e.stopPropagation()}
                >
                  <Ellipsis className="w-4 h-4" />
                  {popup &&
                    createPortal(
                      (() => {
                        const rect = ellipsisRef.current?.getBoundingClientRect()
                        if (!rect) return null
                        return (
                          <div
                            style={{
                              position: 'absolute',
                              top: rect.bottom + window.scrollY,
                              left: rect.left + window.scrollX,
                              zIndex: 9999,
                            }}
                            className="bg-white dark:bg-neutral-800 border rounded shadow-lg min-w-[120px] py-2 px-4"
                          >
                            {actions
                              .filter(action => {
                                const hasParent = !(node.raw?.parentId === null || node.raw?.parentId === undefined)
                                const isNewFolder = action.value === FolderActionKeys.CREATE
                                const isNotDetail = action.value !== FolderActionKeys.DETAILS
                                return isNotDetail && (hasParent || isNewFolder)
                              })
                              .map((action, idx) => (
                                <div
                                  key={idx}
                                  className="flex items-center px-3 py-1 cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-700"
                                  onClick={e => {
                                    e.stopPropagation()
                                    action.onClick?.(node.id, node.raw?.parentId, node.label)
                                  }}
                                >
                                  {action.icon}
                                  <span className="ml-2">{action.label}</span>
                                </div>
                              ))}
                          </div>
                        )
                      })(),
                      document.body,
                    )}
                </span>
              )}
            </>
          )}
        </div>
      </div>
      {hasChildren && open && (
        <div>
          {node.children!.map(child => (
            <TreeItem
              key={child.id}
              node={child}
              parentId={node.id}
              actions={actions}
              showEllipsis={showEllipsis}
              selectedIds={selectedIds}
              selectStyle={selectStyle}
              multiple={multiple}
              onSelect={onSelect}
              onToggleSelect={onToggleSelect}
            />
          ))}
        </div>
      )}
    </div>
  )
}

const TreeList: React.FC<TreeListProps> = ({
  data,
  defaultExpandAll = false,
  className,
  actions = [],
  showEllipsis = false,
  selectedId: externalSelectedId,
  keyword = '',
  selectStyle = '',
  multiple = false,
  selectedIds: externalSelectedIds,
  onSelect,
  onChangeSelection,
}) => {
  const [internalSelectedId, setInternalSelectedId] = useState<string | undefined>(undefined)
  const [internalSelectedIds, setInternalSelectedIds] = useState<string[]>([])

  const handleSelect = (node: TreeNode, ids?: string[]) => {
    console.log('handleSelect', node, ids)

    if (multiple) {
      const current = externalSelectedIds ?? internalSelectedIds
      const newSelection = ids ?? handleSelectNode(node, current)

      if (externalSelectedIds === undefined) setInternalSelectedIds(newSelection)
      onChangeSelection?.(newSelection.map(id => findNodeById(data, id)!).filter(Boolean) as TreeNode[])
    } else {
      // 如果是非受控组件，就自己管理状态
      if (externalSelectedId === undefined) {
        setInternalSelectedId(node.id)
      }
      onSelect?.(node)
    }
  }

  const effectiveSelectedIds = multiple
    ? (externalSelectedIds ?? internalSelectedIds)
    : externalSelectedId
      ? [externalSelectedId]
      : internalSelectedId
        ? [internalSelectedId]
        : []

  if (!data || data.length === 0) {
    return <div className={className}>暂无数据</div>
  }

  return (
    <div className={className}>
      {data.map(node => (
        <TreeItem
          key={node.id}
          node={node}
          parentId={undefined} // 顶层节点没有父 ID
          actions={actions}
          showEllipsis={showEllipsis}
          selectedIds={effectiveSelectedIds}
          defaultExpandAll={defaultExpandAll}
          keyword={keyword}
          selectStyle={selectStyle}
          multiple={multiple}
          onSelect={handleSelect}
          onToggleSelect={(node, ids) => handleSelect(node, ids)}
        />
      ))}
    </div>
  )
}

export default TreeList
