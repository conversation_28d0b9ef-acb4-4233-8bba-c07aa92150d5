import { PaginationParams } from '@app/shared/infra/request'

/**
 * 基础创建分组参数
 */
export interface BaseCreateGroup {
  /** 分组名称 */
  name: string
  /** 父分组ID，顶级分组不传 */
  parentId?: number
  /** 标签 */
  tags: string
}

/**
 * 基础更新分组参数
 */
export interface BaseUpdateGroup {
  /** 分组ID */
  id: number
  /** 分组名称 */
  name: string
  /** 标签 */
  tags: string
}

/**
 * 基础分组项
 */
export interface BaseGroupItem {
  /** 分组ID */
  id: number
  /** 分组名称 */
  name: string
  /** 父分组ID */
  parentId: number
  /** 标签 */
  tags: string
  /** 账号数量 */
  accountNum: number
  /** 子分组列表 */
  children: BaseGroupItem[]
}

/**
 * 基础分组账号搜索参数
 */
export interface BaseGroupAccountSearchParams extends PaginationParams {
  /** 分组ID */
  groupId: number
  /** 搜索关键词 */
  search?: string
}

/**
 * 基础分组账号
 */
export interface BaseGroupAccount {
  /** 账号ID */
  id: number
  /** 头像 */
  avatar?: string
  /** 昵称 */
  nickname?: string
  /** 创建时间 */
  createTime?: number
  /** 分组ID */
  groupId?: number
  /** 分组名称 */
  groupName?: string
}
