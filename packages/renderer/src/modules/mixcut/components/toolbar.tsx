import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Settings } from 'lucide-react'
import { RangeInput } from '@/components/ui/number-input'
import { SavedMixcutDeleteButton } from './saved-mixcut-delete-button'
import { SavedMixcutExportButton } from './saved-mixcut-export-button'
import { MixcutRulesDrawer } from './mixcut-rule-drawer/mixcut-rule-drawer'
import { useMixcutContext } from '../context/context'
import { generateRenderRequestPayload } from '@/modules/mixcut/utils'

export const Toolbar = () => {
  const {
    state,
    activeTab,
    setActiveTab,
    generation: {
      duplicateRateFilter, selectedIndices, generatedMixcuts,
      uploadSelectedPreviews, setDuplicateRateFilter,
    }
  } = useMixcutContext()

  return (
    <div className="flex items-center justify-between p-4 border-b border-border bg-background">
      <div className="flex items-center space-x-4">
        {/* 视图切换按钮 */}
        {/*<div className="flex items-center space-x-1">*/}
        {/*  <Button*/}
        {/*    variant={viewMode === 'grid' ? 'default' : 'ghost'}*/}
        {/*    size="sm"*/}
        {/*    onClick={() => setViewMode('grid')}*/}
        {/*    className="h-8 w-8 p-0"*/}
        {/*  >*/}
        {/*    <Grid3X3 className="h-4 w-4" />*/}
        {/*  </Button>*/}
        {/*  <Button*/}
        {/*    variant={viewMode === 'list' ? 'default' : 'ghost'}*/}
        {/*    size="sm"*/}
        {/*    onClick={() => setViewMode('list')}*/}
        {/*    className="h-8 w-8 p-0"*/}
        {/*  >*/}
        {/*    <List className="h-4 w-4" />*/}
        {/*  </Button>*/}
        {/*</div>*/}

        {/* 搜索框 */}
        {/*<div className="relative">*/}
        {/*  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />*/}
        {/*  <Input*/}
        {/*    placeholder="搜索视频..."*/}
        {/*    className="pl-9 w-64"*/}
        {/*  />*/}
        {/*</div>*/}

        {/* 重复率筛选 */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground whitespace-nowrap">重复率筛选:</span>
          <RangeInput
            minValue={duplicateRateFilter.minRate}
            maxValue={duplicateRateFilter.maxRate}
            onMinChange={minRate => setDuplicateRateFilter({ ...duplicateRateFilter, minRate })}
            onMaxChange={maxRate => setDuplicateRateFilter({ ...duplicateRateFilter, maxRate })}
            min={0}
            max={100}
            step={1}
            suffix="%"
            className="w-auto"
          />
        </div>

        {/*<Select defaultValue="invisible">*/}
        {/*  <SelectTrigger className="w-40">*/}
        {/*    <SelectValue placeholder="" />*/}
        {/*  </SelectTrigger>*/}
        {/*  <SelectContent>*/}
        {/*    <SelectItem value="invisible">当前脚本内去重</SelectItem>*/}
        {/*    <SelectItem value="visible">当前项目内去重</SelectItem>*/}
        {/*  </SelectContent>*/}
        {/*</Select>*/}

        {/*<Select defaultValue="basic">*/}
        {/*  <SelectTrigger className="w-40">*/}
        {/*    <SelectValue placeholder="" />*/}
        {/*  </SelectTrigger>*/}
        {/*  <SelectContent>*/}
        {/*    <SelectItem value="basic">严谨去重算法</SelectItem>*/}
        {/*    <SelectItem value="advanced">标准去重算法</SelectItem>*/}
        {/*  </SelectContent>*/}
        {/*</Select>*/}
      </div>

      <div className="flex items-center space-x-2">
        {activeTab === 'generation' ? (
          <>
            <MixcutRulesDrawer>
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4 mr-2" />
                混剪规则
              </Button>
            </MixcutRulesDrawer>

            {selectedIndices.size > 0 && (
              <Button
                className="bg-gradient-brand "
                size="sm"
                onClick={() => uploadSelectedPreviews().then(() => setActiveTab('saved'))}
                disabled={selectedIndices.size === 0}
              >
                保存所选{selectedIndices.size}个混剪
              </Button>
            )}

            {import.meta.env.DEV && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const combo = generatedMixcuts[selectedIndices.values().next().value]
                  return navigator.clipboard.writeText(JSON.stringify(
                    generateRenderRequestPayload(state.tracks, combo, state.playerMetadata)
                  ))
                }}
              >
                EXPORT
              </Button>
            )}
          </>
        ) : (
          <>
            <SavedMixcutDeleteButton />
            <SavedMixcutExportButton />
          </>
        )}
      </div>
    </div>
  )
}
