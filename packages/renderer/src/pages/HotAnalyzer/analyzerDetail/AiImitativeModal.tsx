import React, { useState, useRef, useEffect } from 'react'
import { Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { SettingsTabs, TabItem } from '@/modules/video-editor/shared'
import { <PERSON><PERSON>, CustomDialogContent, DialogHeader, DialogFooter } from '@/components/ui/dialog'
import { usePending } from '@/hooks/usePending'
import { AiImitativeForm, FieldConfig, AiImitativeFormRef } from '@/pages/HotAnalyzer/analyzerDetail/AiImitativeForm'
import { HotAnalyzerModule } from '@/libs/request/api/analyzer'
import { HotAnalyzerInfo } from '@/types/analyzer'
import SaveScriptDialog from '@/pages/HotAnalyzer/analyzerDetail/SaveScriptDialog'

type AiImitativeModalProps = {
  open: boolean
  onOpenChange: (open: boolean) => void
  hotCopyScriptId?: string
}

const AiImitativeModal: React.FC<AiImitativeModalProps> = ({ open, onOpenChange, hotCopyScriptId }) => {
  const [activeTab, setActiveTab] = useState<string>('')
  const [tabs, setTabs] = useState<TabItem[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [openSaveDialog, setOpenSaveDialog] = useState(false)
  const [scriptData, setScriptData] = useState<HotAnalyzerInfo>()
  // 每个表单的 ref
  const formRefs = useRef<Record<string, React.RefObject<AiImitativeFormRef | null>>>({})
  const { pending, withPending } = usePending()

  // 获取接口分类
  useEffect(() => {
    async function fetchTabs() {
      try {
        setLoading(true) 
        const categories = await HotAnalyzerModule.formCategories({ id: 10000000020 }) // 获取分类,TODO: 先写死夫分类id
        const tabsData: TabItem[] = []

        // 并行获取所有表单字段
        await Promise.all(
          categories.map(async (cat: { id: number; name: string }) => {
            // 创建 ref
            formRefs.current[cat.id] = React.createRef<AiImitativeFormRef>()
            // 获取表单字段
            const res = await HotAnalyzerModule.getForm({ id: cat.id })
            const formFields: FieldConfig[] = JSON.parse(res.formContent || '[]')
            // const formFields: FieldConfig[] = [
            //   { name: 'merchantName', label: '商家名称', type: 'text', required: true },
            //   { name: 'merchantAddress', label: '商家地址', type: 'text', required: true },
            //   { name: 'targetAudience', label: '目标人群', type: 'text', required: true },
            //   { name: 'serviceFeatures', label: '服务特色', type: 'textarea', required: true },
            //   {
            //     name: 'merchantTags',
            //     label: '商家名称的标签列表',
            //     type: 'tags-from-api',
            //     showLabel: false,
            //     dependOn: 'merchantName',
            //     targetField: 'serviceFeatures',
            //   },
            //   { name: 'promotionInfo', label: '优惠信息', type: 'textarea', required: true },
            //   { name: 'dnaSwitch', label: 'DNA', type: 'switch', labelPosition: 'left' },
            //   {
            //     name: 'dnaContent',
            //     label: 'DNA的tab',
            //     type: 'tabs',
            //     showLabel: false,
            //     switchTabs: [
            //       {
            //         value: 'link',
            //         label: '视频链接',
            //         type: 'text',
            //         contentKey: 'url',
            //         placeholder: '请输入视频链接',
            //       },
            //       {
            //         value: 'file',
            //         label: '视频文件',
            //         type: 'file',
            //         contentKey: 'url',
            //       },
            //     ],
            //   },
            // ]
            tabsData.push({
              value: String(cat.id),
              label: cat.name,
              content: (
                <div className="max-h-[500px] max-w-[400px] overflow-y-auto p-[10px]">
                  <AiImitativeForm ref={formRefs.current[cat.id]} fields={formFields} promptCode={res.promptCode} />
                </div>
              ),
            })
          }),
        )

        setTabs(tabsData)
        if (tabsData.length > 0) setActiveTab(tabsData[0].value)
      } catch (err) {
        console.error('获取分类或表单失败', err)
      } finally {
        setLoading(false)
      }
    }

    fetchTabs()
  }, [])

  // 底部统一提交逻辑
  const handleSubmit = async () => {
    const currentRef = formRefs.current[activeTab]?.current
    if (!currentRef) return

    const { values, isValid } = currentRef.validate()
    if (!isValid) return

    const currentTab = tabs.find(t => t.value === activeTab)
    if (!currentTab) return

    try {
      const params = JSON.stringify({
        ...values.finalValues, // 表单值
        hotCopyScriptId, // 当前 脚本id
      })

      const res = await HotAnalyzerModule.createForm({ promptCode: values.promptCode, params })
      // const res = {
      //   id: 10000000952,
      //   content:
      //     '{\n    "cover_url": "",\n    "des": {\n        "产品名称": "专业三脚架",\n        "卖点词": [\n            "高稳定性",\n            "多角度调节",\n            "便携设计",\n            "专业拍摄"\n        ],\n        "受众分析": "视频主要面向摄影爱好者，他们追求高质量的拍摄设备和便捷的拍摄体验。视频将展示产品在各种拍摄场景下的表现，突出其专业性和实用性。",\n        "受众群体": [\n            "摄影爱好者",\n            "专业摄影师",\n            "旅行摄影师"\n        ],\n        "时长": 60000,\n        "语言": "中文"\n    },\n    "script": [\n        {\n            "scene": [\n                {\n                    "notes": "一位摄影师在户外拍摄，背景是美丽的日落景色，他正在调整三脚架的高度。",\n                    "shotType": "medium",\n                    "url": ""\n                }\n            ],\n            "script_name": "专业场景开场",\n            "text": "拍摄大片，怎么能少了一款专业的三脚架？"\n        },\n        {\n            "scene": [\n                {\n                    "notes": "摄影师手持三脚架，展示其轻便的设计和坚固的材质。",\n                    "shotType": "closeup",\n                    "url": ""\n                }\n            ],\n            "script_name": "产品特性展示",\n            "text": "这款三脚架不仅轻便，还非常稳固，适合各种户外拍摄。"\n        },\n        {\n            "scene": [\n                {\n                    "notes": "摄影师在三脚架上安装相机，准备拍摄。",\n                    "shotType": "close",\n                    "url": ""\n                }\n            ],\n            "script_name": "使用场景描述",\n            "text": "无论是风景摄影还是人像拍摄，它都能轻松应对。"\n        },\n        {\n            "scene": [\n                {\n                    "notes": "摄影师调整三脚架的角度，展示其多角度调节功能。",\n                    "shotType": "medium",\n                    "url": ""\n                }\n            ],\n            "script_name": "功能优势展示",\n            "text": "360度旋转设计，让你轻松找到最佳拍摄角度。"\n        },\n        {\n            "scene": [\n                {\n                    "notes": "摄影师用三脚架拍摄一张完美的日落照片。",\n                    "shotType": "long",\n                    "url": ""\n                }\n            ],\n            "script_name": "拍摄效果展示",\n            "text": "用它拍出的照片，每一张都是大片。"\n        },\n        {\n            "scene": [\n                {\n                    "notes": "摄影师收起三脚架，展示其便携性。",\n                    "shotType": "closeup",\n                    "url": ""\n                }\n            ],\n            "script_name": "便携性展示",\n            "text": "轻松收纳，随身携带，让你的拍摄之旅更轻松。"\n        }\n    ],\n    "source_link": "",\n    "title": "专业三脚架拍摄体验",\n    "video_url": ""\n}',
      //   promptTokens: 2628,
      //   completionTokens: 625,
      //   reasoningTokens: null,
      //   totalTokens: 3253,
      //   runTime: 24976,
      //   createTime: 1756645398819,
      // }
      const script = JSON.parse(res.content || '[]')
      // 打开保存脚本弹窗
      setOpenSaveDialog(true)
      setScriptData(script)
    } catch (err) {
      console.error('生成失败，请重试', err)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <CustomDialogContent>
        <DialogHeader />
        {loading ? (
          <div className="flex flex-col items-center justify-center h-64">
            <Loader2 className="animate-spin w-8 h-8 mb-2" />
            <span>加载中...</span>
          </div>
        ) : (
          <div className="relative">
            <SettingsTabs tabs={tabs} onTabChange={(value: string) => setActiveTab(value)} />
          </div>
        )}

        <SaveScriptDialog
          open={openSaveDialog} // 控制弹窗是否显示
          onOpenChange={setOpenSaveDialog} // 弹窗自己关闭时会回调
          scriptData={scriptData} // 传入参考视频脚本数据
          description="脚本生成成功，请选择一个项目来保存您的脚本"
          onParentClose={() => onOpenChange(false)}
        />
        <DialogFooter className="mt-4 flex justify-end gap-2">
          <Button className="w-full" disabled={pending} onClick={withPending(handleSubmit)}>
            {pending ? (
              <div className="flex">
                <Loader2 className="animate-spin size-4" />
                生成中,请稍等...
              </div>
            ) : (
              '立即生成'
            )}
          </Button>
        </DialogFooter>
      </CustomDialogContent>
    </Dialog>
  )
}

export default AiImitativeModal
