import { ModalContent } from '@/components/modal'
import { Separator } from '@/components/ui/separator'
import { useQueryTeamMemberList } from '@/hooks/queries/useQueryTeam'
import { useModal, useModalContext } from '@/libs/tools/modal'
import React from 'react'
import { cn } from '@/components/lib/utils'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { User2Icon } from 'lucide-react'

function SelectMemberModal({
  title,
  description,
  onConfirm,
}: {
  title: string
  description: string
  onConfirm?: (selectedMember: number | null) => void
}) {
  const { close } = useModalContext()
  const { data: members } = useQueryTeamMemberList('')
  const [selectedMember, setSelectedMember] = React.useState<number | null>(null)

  return (
    <ModalContent
      title={title}
      description={description}
      onConfirm={() => {
        onConfirm?.(selectedMember) // 点击确定时调用父组件传入的操作
        close()
      }}
    >
      <Separator />
      <div className="flex flex-col gap-2 w-100">
        <div className="min-h-60 max-h-80 overflow-y-auto -mr-4.5 pr-3">
          <RadioGroup value={selectedMember?.toString()} onValueChange={value => setSelectedMember(parseInt(value))}>
            {members?.map((member, index) => (
              <div
                className={cn(
                  'flex items-center justify-between py-4 px-2',
                  selectedMember === member.id ? 'bg-primary-highlight1/10' : '',
                  'hover:bg-primary-highlight1/10 hover:border-b hover:border-primary-border',
                )}
                key={member.id}
                onClick={() => {
                  setSelectedMember(member.id)
                }}
              >
                <div className="flex items-center">
                  {/* 头像 */}
                  {member.avatar ? (
                    <img src={member.avatar} alt={member.nickname} className="w-10 h-10 rounded-full" />
                  ) : (
                    <div className="w-10 h-10 rounded-full border bg-blue flex items-center justify-center">
                      <User2Icon className="w-7 h-7 text-muted-foreground" />
                    </div>
                  )}
                  <span className="ml-4">{member.nickname}</span>
                </div>
                <RadioGroupItem value={member.id.toString()} id={`storyboard-${index}`} />
              </div>
            ))}
          </RadioGroup>
        </div>
      </div>
    </ModalContent>
  )
}

export function useSelectMember() {
  const modal = useModal()

  return (options?: { title?: string; description?: string; onConfirm?: (memberId: number) => void }) =>
    modal({
      content: (
        <SelectMemberModal
          title={options?.title ?? '移交/解散团队'}
          description={options?.description ?? '请选择一位成员成为团队拥有者'}
          onConfirm={options?.onConfirm}
        />
      ),
    })
}
