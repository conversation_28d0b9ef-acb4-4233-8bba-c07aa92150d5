import { Inject, Injectable } from '@nestjs/common'
import { CrudableBaseIPCHandler } from '@/infra/types/CrudableBaseIPCHandler.js'
import { UploadTaskService } from './upload-task.service.js'

/**
 * 上传任务 IPC 处理器
 */
@Injectable()
export class UploadTask<PERSON>CHandler extends CrudableBaseIPCHandler<'uploadTask'> {

  constructor(
    @Inject(UploadTaskService) readonly uploadTaskService: UploadTaskService
  ) {
    super(uploadTaskService, 'uploadTask')
  }

  /**
   * 注册额外的 IPC 处理程序
   */
  protected registerExtraHandlers(): void {
    // 获取用户上传任务
    this.registerHandler('getUserTasks', data => this.uploadTaskService.getUserTasks(data))

    // 获取文件夹下的上传任务
    this.registerHandler('getTasksByFolder', data => this.uploadTaskService.getTasksByFolder(data))

    // 搜索上传任务
    this.registerHandler('searchTasks', data => this.uploadTaskService.searchTasks(data))

    // 获取上传任务统计
    this.registerHandler('getTaskStats', data => this.uploadTaskService.getTaskStats(data))

    // 开始上传任务
    this.registerHandler('startUpload', data => this.uploadTaskService.startUpload(data))

    // 暂停上传任务
    this.registerHandler('pauseUpload', data => this.uploadTaskService.pauseUpload(data))

    // 恢复上传任务
    this.registerHandler('resumeUpload', data => this.uploadTaskService.resumeUpload(data))

    // 取消上传任务
    this.registerHandler('cancelUpload', data => this.uploadTaskService.cancelUpload(data))

    // 重试上传任务
    this.registerHandler('retryUpload', data => this.uploadTaskService.retryUpload(data))

    // 批量操作上传任务
    this.registerHandler('batchOperation', data => this.uploadTaskService.batchOperation(data))

    // 获取上传队列配置
    this.registerHandler('getQueueConfig', () => this.uploadTaskService.getQueueConfig())

    // 更新上传队列配置
    this.registerHandler('updateQueueConfig', config => this.uploadTaskService.updateQueueConfig(config))

    // 获取当前上传队列状态
    this.registerHandler('getQueueStatus', () => this.uploadTaskService.getQueueStatus())

    // 清理已完成的任务
    this.registerHandler('cleanupCompleted', data => this.uploadTaskService.cleanupCompleted(data))

    // 选择文件
    this.registerHandler('selectFiles', data => this.uploadTaskService.selectFiles(data))

    // 从本地路径上传文件
    this.registerHandler('uploadFromPath', data => this.uploadTaskService.uploadFromPath(data))

    // 上传文件夹
    this.registerHandler('uploadFolder', data => this.uploadTaskService.uploadFolder(data))
  }
}
