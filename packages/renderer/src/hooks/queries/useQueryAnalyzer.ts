import { QUERY_KEYS } from '@/constants/queryKeys'
import { HotAnalyzerModule } from '@/libs/request/api/analyzer'
import { useInfiniteQuery } from '../useInfiniteQuery'
import { useQuery } from '@tanstack/react-query'
import { HotAnalyzerInfo } from '@/types/analyzer'

/**
 * 使用无限查询获取爆款解析列表，支持无限滚动加载
 */
export const useInfiniteQueryHotAnalyzer = ()  => {
  return useInfiniteQuery(
    [QUERY_KEYS.HOT_ANALYZER_LIST],
    HotAnalyzerModule.page,
    {},
    {
      pageSize: 12,
      refetchInterval: 20_000
    }
  )
}

/**
 * 使用查询获取解析详情
 * @param id 脚本ID
 * @returns 查询结果
 */
export const useQueryAnalyzer = (id?: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.HOT_ANALYZER_DETAIL, id],
    queryFn: async (): Promise<HotAnalyzerInfo> => {
      if (!id) throw new Error('缺少ID，请重试')
      
      return HotAnalyzerModule.get({ id })
    },
    enabled: !!id,
  })
}
