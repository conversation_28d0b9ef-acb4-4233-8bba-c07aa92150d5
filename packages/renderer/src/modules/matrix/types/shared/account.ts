import { AccountTokenStatus } from '../../enums/shared'
import { PaginationParams } from '@app/shared/infra/request'

/**
 * 基础账号分组列表
 */
export interface BaseAccountGroupList {
  accountId?: number
  groupId?: number
  name?: string
}

/**
 * 基础账号信息
 */
export interface Account {
  /** 账号ID */
  id: number
  /** 头像 */
  avatar?: string
  /** 昵称 */
  nickname?: string
  /** 原始昵称 */
  orgNickname?: string
  /** 绑定手机号 */
  bindPhone?: string | null
  /** 渠道类型 */
  channelType?: number
  /** 城市 */
  city?: string
  /** 省份 */
  province?: string
  /** 创建时间 */
  createTime?: number
  /** 过期时间 */
  expiredTime?: number
  /** 粉丝数 */
  fanNum?: number | null
  /** 点赞数 */
  likeNum?: number | null
  /** 评论数 */
  commentNum?: number | null
  /** 分享数 */
  shareNum?: number | null
  /** 发布数 */
  publishNum?: number
  /** 发布状态 */
  publishStatus?: number
  /** 备注 */
  remark?: string
  /** 开放ID */
  openId?: string
  /** 原始账号类型 */
  orgAccountType?: number
  /** 原始会话ID */
  orgSessionId?: string
  /** 会话ID */
  sessionId?: string
  /** 子账号ID */
  subAccountId?: number
  /** 访问令牌状态 */
  accessTokenStatus?: number
  /** 原始访问令牌状态 */
  accessTokenStatusOrigin?: number
  /** 广告主ID */
  advertiserId?: number
  /** Plus访问令牌状态 */
  plusAccessTokenStatus?: number
  /** POI城市代码 */
  poiCityCode?: number
  /** POI ID */
  poiId?: number
  /** POI名称 */
  poiName?: string
  /** 分组列表 */
  groupList?: BaseAccountGroupList[]
  uniqueId?: string
}

/**
 * 基础账号搜索参数
 */
export interface AccountSearchParams extends PaginationParams {
  /**
     * 状态，1-已授权，2-授权到期, null - 全部
     */
  accessTokenStatus: AccountTokenStatus | null;
  /**
     * 0-二维码短信授权，1-智能AI授权
     */
  channelType?: number;
  createTime?: number[];
  province?: string;
  /**
     * 模糊搜索
     */
  search?: string;
}
