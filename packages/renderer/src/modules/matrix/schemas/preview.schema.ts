import z from 'zod'
import { platformEnum } from './shared.schema'
import { PlatformKey } from '@/libs/request/api/generic-matrix'

const aggAccountSchema = z.object({
  platform: platformEnum,
  nickname: z.string().optional(),
  avatar: z.string().optional(),
  id: z.number(),
  title: z.string(),
  videoUrl: z.string(),
  videoCover: z.string(),
})

export const aggregatedPreviewSchema = z.object({
  videoUrl: z.string(),
  videoCover: z.string(),
  aggAccounts: z.array(aggAccountSchema),
})

export const platformPreviewSchema = z.record(
  platformEnum,
  aggAccountSchema 
).optional()

export type AggAccount = z.infer<typeof aggAccountSchema>
export type AggregatedPreview = z.infer<typeof aggregatedPreviewSchema>
export type PlatformPreview = Partial<Record<PlatformKey, AggAccount>>
