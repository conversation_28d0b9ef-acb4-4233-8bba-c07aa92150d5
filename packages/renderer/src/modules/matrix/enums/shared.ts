/**
   * 挂载模式枚举
   */
export enum VideoMountMode {
  /** 不挂载 */
  NONE = 0,
  /** 位置 */
  LOCATION = 1,
  /** 购物车 */
  CART = 2
}

/**
   * 发布模式枚举
   */
export enum PublishMode {
  /** 打卡 */
  CHECK_IN = 1,
  /** 带货 */
  GOODS = 2
}

/**
   * 发布设置枚举
   */
export enum PublishSetting {
  /** 一个账号一个视频 */
  ONE_ACCOUNT_ONE_VIDEO = 1,
  /** 一个账号多个视频 */
  ONE_ACCOUNT_MULTIPLE_VIDEOS = 2
}

/**
   * 发布时间类型枚举
   */
export enum PublishTimeType {
  /** 立即 */
  IMMEDIATE = 1,
  /** 定时 */
  SCHEDULED = 2,
  /** 循环 */
  LOOP = 3
}

/**
   * 定时模式间隔类型枚举
   */
export enum PeriodType {
  /** 单账号间隔 */
  SINGLE_ACCOUNT = 1,
  /** 多账号间隔 */
  MULTIPLE_ACCOUNTS = 2
}

export enum AccountTokenStatus {
  /** 已授权 */
  AUTHORIZED = 1,
  /** 授权到期 */
  EXPIRED = 2,
  /** 授权中 */
  ALL = 0,
}

export enum PublishStatus {
  ALL = -1,
  AWAITING = 0,
  PUBLISHING = 1,
  FAILED = 2,
  SUCCESS = 3,
  PART_SUCCESS = 4
}