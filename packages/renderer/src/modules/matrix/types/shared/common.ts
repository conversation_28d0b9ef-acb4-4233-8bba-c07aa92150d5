import { DetailPlanDO } from '@/modules/matrix/schemas/shared.schema'
import { PaginationParams } from '@app/shared/infra/request'

export interface PushPlanListSearchParams extends PaginationParams {
  /**
     * 0号元素-开始时间，1号元素，结束时间
     */
  createTime?: number[];
  /**
     * 计划ID
     */
  planId?: number;
  /**
     * 账号昵称模糊查询
     */
  search?: string;
  /**
     * 状态,同发布计划分页状态说明
     */
  status?: number;
}

/**
 * 基础时间范围参数
 */
export interface BaseTimeRangeParams {
  startTime?: number
  endTime?: number
}

/**
 * 基础产品信息
 */
export interface BaseProduct {
  title: string
  url: string
}

/**
 * 基础账号概览统计
 */
export interface BaseAccountOverview {
  totalAccounts: number
  activeAccounts: number
  totalPublish: number
  totalLikes: number
  totalComments: number
  totalShares: number
  totalFans: number
  authAccount: number;
  changeFans: number;
  commentNum: number;
  diggNum: number;
  fans: number;
  playNum: number;
  shareNum: number;
  totalVideo: number;
}

/**
 * 基础推送计划
 */
export interface BasePushPlan {
  id: number
  name: string
  status: number
  statusText?: string
  totalAccount: number
  publishedCount?: number
  failedCount?: number
  createTime: number
  updateTime?: number
  marketingTarget?: string
  publishTime?: number
  detailVOList: (DetailPlanDO & { planId: number })[]
}

/**
 * 基础推送详情
 */
export interface BasePushDetail {
  id: number
  accountId: number
  accountName?: string
  title?: string
  url?: string
  publishTime?: number
  status: number
  statusText?: string
  createTime: number
  error?: string | null
  /** 广告ID */
  adId?: number
  /** 宽高比 */
  aspectRatio?: string
  /** @用户 */
  atUsers?: string
  /** 是否取消 */
  cancel?: boolean
  /** 挑战/话题 */
  challenges?: string
  /** 城市 */
  city?: string
  /** 省份 */
  province?: string
  /** 评论数 */
  commentNum?: number | null
  /** 创意ID */
  creativeId?: number
  /** 描述 */
  description?: string
  /** 点赞数 */
  diggNum?: number | null
  /** 播放数 */
  playNum?: number | null
  /** 分享数 */
  shareNum?: number | null
  /** 文件来源 */
  fileSource?: number
  /** 最终状态 */
  finalStatus?: number
  /** 是否POI商业 */
  isPoiCommerce?: number
  /** 提及 */
  mentions?: string
  /** 名称 */
  name?: string | null
  /** 昵称 */
  nickname?: string
  /** 计划ID */
  planId?: number
  /** POI城市代码 */
  poiCityCode?: string
  /** POI ID */
  poiId?: string
  /** POI名称 */
  poiName?: string
  /** 制作人广告主ID */
  producerAdvertiserId?: number
  /** 制作人名称 */
  producerName?: string
  /** 制作人子账号ID */
  producerSubaccountId?: number
  /** 项目ID */
  projectId?: number
  /** 文本额外信息 */
  textExtra?: string
  /** 上传状态 */
  uploadStatus?: number
  /** 视频封面 */
  videoCover?: string
  /** 视频时长 */
  videoDuration?: number
  /** 视频ID */
  videoId?: number
  /** 视频名称 */
  videoName?: string
}

/**
 * 基础发布表单详情
 */
export interface BasePublishFormDetail {
  id: number
  name: string
  channelType: number
  publishType: number
  marketingTarget: string
  accountIds: number[]
  titles: string[]
  businessId: any
  publishMode: number
  setting: number
  timeType: number
  createTime: number
  updateTime: number
  /** 挂载模式 */
  mountType: number
}
