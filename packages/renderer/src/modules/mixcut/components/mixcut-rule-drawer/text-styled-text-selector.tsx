import React from 'react'
import { StyledTextResource } from '@/types/resources'
import { useInfiniteQueryFontStyleList } from '@/hooks/queries/useQueryTextStyle'
import { StyledTextSelector } from '@/modules/video-editor/components/common/styled-text-selector'
import { SelectableInfinitePanel } from './selectable-infinite-panel'

export const TextStyledTextSelector: React.FC<{
  onChange?: (val: StyledTextResource.StyledText[]) => void
}> = ({ onChange }) => {
  const query = useInfiniteQueryFontStyleList({
    pageNo: 1,
    pageSize: 50,
    autoLoadAllPages: true
  })

  return (
    <div>
      <SelectableInfinitePanel
        query={query}
        itemName="样式"
        scrollAreaHeight={368}
        onChange={onChange}
        renderItem={item => (
          <StyledTextSelector.ItemRenderer
            {...item}
            className="border-none"
          />
        )}
      />
    </div>
  )
}
