import React from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { DataTable } from '@/components/ui/data-table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Eye, RefreshCw } from 'lucide-react'

import {  useNavigate } from 'react-router'
import { usePagination } from '@/hooks/usePagination'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { GenericMatrixModule } from '@/libs/request/api/generic-matrix'
import { BasePushPlan } from '@/modules/matrix/types/shared'
import { usePlatform } from '@/modules/matrix/context/context'
import { publishStatusMap } from '@/modules/matrix/constants/shared'

export const PushPlanTable = () => {
  const { currentPlatform } = usePlatform()
  
  const {
    data,
    pagination: paginationInfo,
    setPagination,
    isLoading,
    refetch
  } =  usePagination({
    queryKey: [QUERY_KEYS.DY_PUSH_PLAN_LIST, currentPlatform],
    queryFn: params => GenericMatrixModule.endpoints.pushPlanList(currentPlatform, params),
    searchParams: {},
    initialPageSize: 10,
    refetchInterval: 5000,
  })

  const navigate = useNavigate()
  const getStatusBadge = (status: number) => {
    const statusInfo =  publishStatusMap[status]
    return (
      <Badge variant={statusInfo.variant}>
        {statusInfo.label}
      </Badge>
    )
  }

  const formatDate = (timestamp?: number) => {
    if (!timestamp) return '-'
    return new Date(timestamp).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const columns: ColumnDef<BasePushPlan>[] = [
    {
      accessorKey: 'id',
      header: 'ID',
      cell: ({ row }) => (
        <div className="">
          {row.getValue('id') || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'name',
      header: '计划名称',
      cell: ({ row }) => (
        <div className="font-medium max-w-[200px] truncate">
          {row.getValue('name') || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'totalAccount',
      header: '账号数',
      cell: ({ row }) => (
        <div className="">
          {row.getValue('totalAccount') || 0}
        </div>
      ),
    },
    {
      accessorKey: 'totalVideo',
      header: '视频数',
      cell: ({ row }) => (
        <div className="">
          {row.getValue('totalVideo') || 0}
        </div>
      ),
    },
    {
      accessorKey: 'wait',
      header: '待发布',
      cell: ({ row }) => (
        <div className=" ">
          {row.getValue('wait') || 0}
        </div>
      ),
    },
    {
      accessorKey: 'doing',
      header: '发布中',
      cell: ({ row }) => (
        <div className=" text-blue-600">
          {row.getValue('doing') || 0}
        </div>
      ),
    },
    {
      accessorKey: 'success',
      header: '成功',
      cell: ({ row }) => (
        <div className=" text-green-600">
          {row.getValue('success') || 0}
        </div>
      ),
    },
    {
      accessorKey: 'fail',
      header: '失败',
      cell: ({ row }) => (
        <div className=" text-red-600">
          {row.getValue('fail') || 0}
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ row }) => getStatusBadge(row.getValue('status')),
    },
    {
      accessorKey: 'publishTime',
      header: '发布时间',
      cell: ({ row }) => (
        <div className="text-sm  min-w-[120px]">
          {formatDate(row.getValue('publishTime'))}
        </div>
      ),
    },
    {
      accessorKey: 'createTime',
      header: '创建时间',
      cell: ({ row }) => (
        <div className="text-sm  min-w-[120px]">
          {formatDate(row.getValue('createTime'))}
        </div>
      ),
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const plansId = row.original.detailVOList?.[0].planId

        return (
          <Button variant="link"
            disabled={!plansId}
            size="sm"
            onClick={() => {
              navigate(`/home/<USER>/planDetail?ids=${encodeURIComponent(JSON.stringify([plansId]))}`)
            }}
          >
            <Eye className="mr-2 h-4 w-4" />
            查看详情
          </Button>

        )
      }
    },
  ]

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between mb-4 flex-shrink-0">
        <div className="flex  items-center gap-3">
          <span className="text-lg font-semibold text-gradient-brand">视频发布计划</span>
          <div className="text-sm text-gray-400">
            共 {paginationInfo.total} 条记录
          </div>
        </div>

        <Button size="sm" onClick={refetch} >
          <RefreshCw className="size-4 mr-2"  />
          刷新
        </Button>
      </div>
     
      <div className="flex-1 min-h-0">
        <DataTable
          columns={columns}
          data={data}
          loading={isLoading}
          pagination={paginationInfo}
          onPaginationChange={setPagination}
          emptyMessage="暂无发布计划"
          className="h-full"
        />
      </div>
    </div>
  )
}
