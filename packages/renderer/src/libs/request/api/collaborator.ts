import { requestCurrying } from '../request'

type Collaborator = {
  createTime: number
  id: number
  memberId: number
  projectId: number
  source: string
}

export const CollaboratorAPI = {
  list: requestCurrying.get<{ projectId: number }, Collaborator[]>('/app-api/creative/project-member/list'),
  save: requestCurrying.post<{ projectId: number; memberIds: number[] }, boolean>(
    '/app-api/creative/project-member/save',
  ),
}
