import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Footer, DialogHeader, DialogTitle, } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/useToast'
import { AlertCircle, CheckCircle, Download, RefreshCw } from 'lucide-react'

interface CheckUploadProps {
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 是否显示文本标签
   */
  showLabel?: boolean
  /**
   * 自定义标签文本
   */
  labelText?: string
}

/**
 * 更新检测组件
 * 显示更新状态徽章，点击后弹出更新确认对话框
 */
export const AppUpdateChecker: React.FC<CheckUploadProps> = ({
  className = '',
  showLabel = true,
  labelText = '检查更新'
}) => {
  const { toast } = useToast()

  // 更新状态
  const [hasUpdate, setHasUpdate] = useState(false)
  const [updateVersion, setUpdateVersion] = useState<string>()
  const [checking, setChecking] = useState(false)
  const [updating, setUpdating] = useState(false)

  // 对话框状态
  const [dialogOpen, setDialogOpen] = useState(false)

  // 下载进度状态
  const [downloadProgress, setDownloadProgress] = useState(0)
  const [downloadComplete, setDownloadComplete] = useState(false)
  const [downloadError, setDownloadError] = useState<string | null>(null)

  // 监听更新下载进度事件
  useEffect(() => {
    const handleDownloadProgress = (_event: any, data: { progress: number }) => {
      setDownloadProgress(data.progress)
    }

    const handleDownloadComplete = (_event: any, data: { version: string }) => {
      setDownloadComplete(true)
      setUpdating(false)
      toast({
        title: '更新下载完成',
        description: `版本 ${data.version} 已下载完成，点击安装重启应用`
      })
    }

    const handleDownloadError = (_event: any, data: { error: string }) => {
      setDownloadError(data.error)
      setUpdating(false)
      toast({
        title: '更新下载失败',
        description: data.error,
        variant: 'destructive'
      })
    }

    // 添加事件监听器
    window.electronAPI?.ipcRenderer.on('update-download-progress', handleDownloadProgress)
    window.electronAPI?.ipcRenderer.on('update-downloaded', handleDownloadComplete)
    window.electronAPI?.ipcRenderer.on('update-error', handleDownloadError)

    // 清理函数
    return () => {
      window.electronAPI?.ipcRenderer.removeListener('update-download-progress', handleDownloadProgress)
      window.electronAPI?.ipcRenderer.removeListener('update-downloaded', handleDownloadComplete)
      window.electronAPI?.ipcRenderer.removeListener('update-error', handleDownloadError)
    }
  }, [toast])

  // 检查更新
  const handleCheckUpdate = async () => {
    setChecking(true)
    // 重置更新状态
    setDownloadProgress(0)
    setDownloadComplete(false)
    setDownloadError(null)
    setUpdating(false)

    try {
      const result = await window.autoUpdater.checkForUpdates()

      setHasUpdate(result.hasUpdate)
      setUpdateVersion(result.version)
      setDialogOpen(true)

      if (!result.hasUpdate) {
        toast({
          title: '已是最新版本',
          description: '当前已是最新版本，无需更新'
        })
      }
    } catch (e) {
      console.error('检查更新失败:', e)
      toast({
        title: '检查更新失败',
        description: '请稍后重试',
        variant: 'destructive'
      })
    } finally {
      setChecking(false)
    }
  }

  // 开始更新
  const handleStartUpdate = async () => {
    setUpdating(true)
    setDownloadProgress(0)
    setDownloadComplete(false)
    setDownloadError(null)

    try {
      await window.autoUpdater.startUpdate()
      toast({
        title: '开始下载更新',
        description: '正在下载新版本...'
      })
    } catch (e) {
      setUpdating(false)
      toast({
        title: '更新失败',
        description: '请稍后重试',
        variant: 'destructive'
      })
    }
  }

  // 安装更新
  const handleInstallUpdate = async () => {
    try {
      await window.autoUpdater.installUpdate()
    } catch (e) {
      toast({
        title: '安装失败',
        description: '请稍后重试',
        variant: 'destructive'
      })
    }
  }

  return (
    <>
      {/* 更新检测按钮 */}
      <button
        onClick={handleCheckUpdate}
        disabled={checking}
        className={`relative flex items-center gap-2 px-2 py-1 rounded-md hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors ${className}`}
        title={hasUpdate ? `发现新版本 ${updateVersion}` : '检查更新'}
      >
        <RefreshCw
          className={`w-3 h-3 ${checking ? 'animate-spin' : ''}`}
        />
        {showLabel && (
          <span className="text-xs">
            {checking ? '检查中...' : labelText}
          </span>
        )}

        {/* 更新徽章 */}
        {hasUpdate && !checking && (
          <Badge
            variant="success"
            className="absolute -top-1 -right-1 h-2 w-2 p-0 text-[8px]"
          >
            <span className="sr-only">有新版本</span>
          </Badge>
        )}
      </button>

      {/* 更新对话框 */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <RefreshCw className="w-4 h-4" />
              检查更新
            </DialogTitle>
          </DialogHeader>

          {hasUpdate ? (
            <>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-orange-500" />
                  <span>检测到新版本：{updateVersion}</span>
                </div>

                {/* 下载进度显示 */}
                {updating && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="flex items-center gap-2">
                        <Download className="w-4 h-4" />
                        正在下载更新...
                      </span>
                      <span>{downloadProgress}%</span>
                    </div>
                    <Progress value={downloadProgress} className="w-full" />
                  </div>
                )}

                {/* 下载完成显示 */}
                {downloadComplete && (
                  <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                    <CheckCircle className="w-4 h-4" />
                    <span>下载完成，准备安装</span>
                  </div>
                )}

                {/* 错误显示 */}
                {downloadError && (
                  <div className="text-red-600 dark:text-red-400 text-sm">
                    下载失败: {downloadError}
                  </div>
                )}
              </div>

              <DialogFooter>
                {downloadComplete ? (
                  <Button onClick={handleInstallUpdate} >
                    安装并重启
                  </Button>
                ) : (
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setDialogOpen(false)}
                      disabled={updating}
                      className="flex-1"
                    >
                      稍后更新
                    </Button>
                    <Button
                      onClick={handleStartUpdate}
                      loading={updating}
                      disabled={updating}
                    >
                      {updating ? '正在下载…' : '立即更新'}
                    </Button>
                  </div>
                )}
              </DialogFooter>
            </>
          ) : (
            <>
              <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                <CheckCircle className="w-4 h-4" />
                <span>当前已是最新版本</span>
              </div>
              <DialogFooter>
                <Button onClick={() => setDialogOpen(false)}>
                  确定
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}

export default AppUpdateChecker
