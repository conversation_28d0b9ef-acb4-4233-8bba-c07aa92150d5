import request, { fetchPagination, fetchPaginationGet, requestCurrying } from '../request'
import {
  CommonCategory,
  FontResource,
  MaterialResource,
  PasterResource,
  SoundResource,
  StyledTextResource,
  TimbreResource,
  UploadLocal
} from '@/types/resources'
import { OssModule } from '@/libs/request/api/oss'
import { PaginationParams } from '@app/shared/infra/request'
import { CloudLibraries } from '@app/shared/types/cloud-library'

export namespace GenericResourceModule {

  export interface ResourceConfigMap {
    [CloudLibraries.paster]: {
      resource: PasterResource.Paster
      listParams: PaginationParams & { categoryIds?: string[] }
      category: CommonCategory[]
    }
    [CloudLibraries.music]: {
      resource: SoundResource.Sound
      listParams: PaginationParams
      category: CommonCategory[]
    }
    [CloudLibraries.voice]: {
      resource: SoundResource.Sound
      listParams: PaginationParams & { categoryIds?: string[] }
      category: CommonCategory[]
    }
    [CloudLibraries.font]: {
      resource: FontResource.Font
      listParams: PaginationParams
      category: CommonCategory[]
    }
    [CloudLibraries.styledText]: {
      resource: StyledTextResource.StyledText
      listParams: PaginationParams
      category: never
    }
    [CloudLibraries.bubbleText]: {
      resource: FontResource.BubbleLetters
      listParams: PaginationParams
      category: never
    }
    [CloudLibraries.timbre]: {
      resource: TimbreResource.Timbre
      listParams: PaginationParams
      category: never
    }
  }

  export const endpoints = {
    // ===== Library 列表 =====
    list<K extends CloudLibraries>(kind: K, params: ResourceConfigMap[K]['listParams']) {
      return fetchPagination<ResourceConfigMap[K]['resource']>(`/app-api/creative/library/${kind}/search`, params as any)
    },
    category<K extends CloudLibraries>(kind: K) {
      type CategoryType = ResourceConfigMap[K]['category']
      return request.get<CategoryType>(`/app-api/creative/library/${kind}/category`)
    },
    collected<K extends CloudLibraries>(kind: K, params: PaginationParams) {
      return fetchPagination<ResourceConfigMap[K]['resource']>(
        `/app-api/creative/library/${kind}/collect-page`,
        params
      )
    },

    // ===== 团队资源 =====
    teamList<K extends CloudLibraries>(kind: K, params: PaginationParams & { folderUuid: string }) {
      return requestCurrying.post<typeof params>(`/app-api/creative/team/library/${kind}/page`)(params)
    },
    teamCreate<K extends CloudLibraries>(kind: K, payload: UploadLocal) {
      return requestCurrying.post<UploadLocal>(`/app-api/creative/team/library/${kind}/create`)(payload)
    },
    teamRename<K extends CloudLibraries>(kind: K, payload: { fileId: string; fileName: string }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/library/${kind}/rename`)(payload)
    },
    teamDelete<K extends CloudLibraries>(kind: K, payload: { fileIds: string[] }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/library/${kind}/delete`)(payload)
    },
    teamMove<K extends CloudLibraries>(kind: K, payload: { fileIds: string[]; folderUuid: string }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/library/${kind}/move`)(payload)
    },

    // ===== 文件夹操作 =====
    dirList<K extends CloudLibraries>(kind: K) {
      return request.get<MaterialResource.Directory[]>(`/app-api/creative/team/directory/${kind}/list`)
    },
    dirCreate<K extends CloudLibraries>(kind: K, payload: Pick<MaterialResource.Directory, 'parentId' | 'folderName'>) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/directory/${kind}/create`)(payload)
    },
    dirRename<K extends CloudLibraries>(kind: K, payload: Pick<MaterialResource.Directory, 'folderId' | 'folderName'>) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/directory/${kind}/rename`)(payload)
    },
    dirDelete<K extends CloudLibraries>(kind: K, payload: { folderIds: string[] }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/directory/${kind}/delete`)(payload)
    },
    dirMove<K extends CloudLibraries>(kind: K, payload: { folderIds: string[]; parentId: string }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/directory/${kind}/move`)(payload)
    },

    // ===== 回收站 =====
    recycle<K extends CloudLibraries>(kind: K, payload: { fileIds?: string[]; folderIds?: string[] }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/storage/${kind}/recycle`)(payload)
    },
    recycleList<K extends CloudLibraries>(kind: K, params?: any) {
      return fetchPaginationGet<ResourceConfigMap[K]['resource']>(`/app-api/creative/storage/${kind}/recycle-page`)(params)
    },
    back<K extends CloudLibraries>(kind: K, payload: { fileIds?: string[]; folderIds?: string[] }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/storage/${kind}/back`)(payload)
    },

    cover(objectId: string) {
      return OssModule.getObjectHref(objectId)
    }
  }
}
