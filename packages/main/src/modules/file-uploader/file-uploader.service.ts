import { basename, extname } from 'path'
import {
  FileUploaderIPCClient,
  OSSModules,
  UploadBufferPayload,
  UploadResult
} from '@app/shared/types/ipc/file-uploader.js'
import OSS, { MultipartUploadOptions } from 'ali-oss'
import { Inject, Injectable, Logger } from '@nestjs/common'
import { BrowserWindow } from 'electron'
import { RequestService } from '../global/request.service.js'
import { EventEmitter } from 'events'

// 全局事件发射器，用于主进程内部通信
const globalEventEmitter = new EventEmitter()

// 导出全局事件发射器
export { globalEventEmitter }

/**
 * OSS签名响应接口
 */
interface STSSignature {
  accessKeyId: string
  secretAccessKey: string
  securityToken: string
  expiration: string
  platform: number
  callback: string
  callbackVar: string
  objectKey: string
  endpoint: string
  secure: boolean
  regionId: string
  bucket: string
  body: any
  objectId: string
}

/**
 * 解密后的callback配置接口
 */
interface CallbackConfig {
  callbackUrl: string
  callbackBody: string
  callbackBodyType: string
}

/**
 * 解密后的callbackVar配置接口
 */
interface CallbackVarConfig {
  bizId: string
  md5Hash: string
  name: string
  ntrRegion: string
  rat: string
  region: string
  rid: string
  sign: string
  source: string
  tid: string
  uid: string
}

/**
 * 文件上传服务
 * 提供文件和文件夹选择功能
 */
@Injectable()
export class FileUploaderService implements FileUploaderIPCClient {

  private readonly logger = new Logger(FileUploaderService.name)

  constructor(
    @Inject(RequestService) private readonly requestService: RequestService
  ) {
  }

  /**
   * 上传Buffer到OSS（支持断点续传）
   */
  public async uploadBufferToOSS(data: UploadBufferPayload): Promise<UploadResult> {
    if (!data?.buffer || !data?.fileName) {
      return { success: false, error: 'Buffer和文件名不能为空' }
    }

    if (!data.module) {
      return { success: false, error: '`module` 不能为空' }
    }

    const {
      buffer,
      fileName,
      uploadId,
      folderUuid,
      fileMd5,
      module,
      taskId,
      enableResume = true,
      partSize = 1024 * 1024 // 默认 1MB
    } = data

    try {
      if (!buffer || buffer.length === 0) {
        return { success: false, error: '文件内容不能为空' }
      }

      if (!fileName || fileName.trim() === '') {
        return { success: false, error: '文件名不能为空' }
      }

      const signature = await this.generateSTSUrl(
        module,
        this.generateFileName(fileName),
        folderUuid,
        fileMd5,
      )
      // this.logger.debug('STS签名参数:', signature)

      if (signature instanceof Error) {
        const errorMsg = `获取STS签名失败: ${signature.message}`
        this.logger.error(`[FileUploaderService] ${errorMsg}`)
        return {
          success: false,
          error: `${errorMsg}`
        }
      }

      if (!signature) {
        const errorMsg = 'STS签名参数为空'
        this.logger.error(`${errorMsg}`)
        return { success: false, error: errorMsg }
      }

      let callbackConfig: CallbackConfig
      let callbackVarConfig: CallbackVarConfig

      try {
        callbackConfig = this.decodeCallback(signature.callback)
        callbackVarConfig = this.decodeCallbackVar(signature.callbackVar)
      } catch (error) {
        const errorMsg = `解密callback配置失败: ${error instanceof Error ? error.message : '未知错误'}`
        this.logger.error(`${errorMsg}`)
        return { success: false, error: errorMsg }
      }

      const ossOptions = this.buildOSSOptions(callbackConfig, callbackVarConfig)

      try {
        const client = this.createOSSClient(signature)
        let lastProgress: number = 0

        // 创建进度回调函数
        const progressCallback = (progress: any, checkpoint: any) => {
          if (progress) {
            lastProgress = progress
            // console.log('[FileUploaderService] 上传进度:', progress)

            // 如果有 uploadId，通过 IPC 发送进度事件
            if (uploadId) {
              // 发送进度事件到渲染进程
              const allWindows = BrowserWindow.getAllWindows()
              allWindows.forEach((window: any) => {
                window.webContents.send('upload-progress', {
                  uploadId,
                  progress: lastProgress,
                  checkpoint: checkpoint
                })
              })
            }

            // 如果有 taskId，通知队列管理器更新任务进度
            if (taskId) {
              this.logger.debug(`发送进度事件: 任务${taskId}, 进度${lastProgress}`)
              // 使用全局事件发射器通知队列管理器
              globalEventEmitter.emit('upload-task-progress', {
                taskId,
                progress: lastProgress,
                checkpoint: checkpoint
              })
            }
          }
        }

        const ossOptionsWithProgress: MultipartUploadOptions = {
          ...ossOptions,
          progress: progressCallback,
          partSize: partSize
        }

        const bufferData = this.convertToBuffer(buffer)
        const response = await client.multipartUpload(signature.objectKey, bufferData, ossOptionsWithProgress)

        // 构建文件URL - 使用固定的region
        // const fileUrl = `https://${response.bucket}.oss-cn-hangzhou.aliyuncs.com/${fileName}`
        const url = new URL((response.res as any)?.requestUrls?.[0])
        const fileUrl = `${url.protocol}//${url.host}/${url.pathname}`

        // this.logger.log('上传成功:', {
        //   url: fileUrl,
        //   name: fileName,
        //   res: response.res?.status,
        //   response: JSON.stringify(response)
        // })

        return {
          success: true,
          url: fileUrl,
          fileName: fileName,
          progress: lastProgress,
          objectId: signature.objectId,
          folderUuid: folderUuid
        }
      }
      catch (ossError) {
        const errorMsg = `OSS上传失败: ${ossError instanceof Error ? ossError.message : '未知错误'}`
        this.logger.error(`${errorMsg}`, ossError)
        return { success: false, error: errorMsg }
      }
    } catch (error) {
      const errorMsg = `上传过程中发生未预期的错误: ${error instanceof Error ? error.message : '未知错误'}`
      this.logger.error(`${errorMsg}`, error)
      return {
        success: false,
        error: errorMsg
      }
    }
  }

  public async generateSTSUrl(
    module: OSSModules,
    fileName: string,
    folderUuid: string = '',
    fileMd5: string = '',
  ): Promise<STSSignature | Error> {
    try {
      const params = new URLSearchParams({ fileName })

      if (module !== OSSModules.ai) {
        params.append('fileMd5', fileMd5)

        if (module !== OSSModules.publishing) {
          params.append('folderUuid', folderUuid)
        }
      }

      const url = `/app-api/creative/oss/${module}/upload-param?${params.toString()}`

      this.logger.debug(`正在从后端获取OSS签名: ${url}`)

      const responseData = await this.requestService.get(url)

      if (!responseData) {
        throw new Error('API响应格式错误：缺少data字段')
      }

      return responseData as STSSignature
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误'
      this.logger.error('生成OSS预签名URL失败:', errorMsg)
      return new Error(errorMsg)
    }
  }

  /**
   * 生成唯一文件名
   */
  private generateFileName(originalName: string): string {
    const timestamp = Date.now()
    const ext = extname(originalName)
    const baseName = basename(originalName, ext)

    return `${timestamp}-${baseName}${ext}`
  }

  /**
   * 解密base64编码的callback配置
   */
  private decodeCallback(callbackBase64: string): CallbackConfig {
    try {
      if (!callbackBase64 || callbackBase64.trim() === '') {
        throw new Error('callback参数为空')
      }

      const decoded = Buffer.from(callbackBase64, 'base64').toString('utf-8')
      const parsed = JSON.parse(decoded) as CallbackConfig

      if (!parsed.callbackUrl || !parsed.callbackBody || !parsed.callbackBodyType) {
        throw new Error('callback配置缺少必要字段')
      }

      return parsed
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误'
      this.logger.error('解密callback失败:', errorMsg)
      throw new Error(`解密callback配置失败: ${errorMsg}`)
    }
  }

  /**
   * 解密base64编码的callbackVar配置
   */
  private decodeCallbackVar(callbackVarBase64: string): CallbackVarConfig {
    try {
      if (!callbackVarBase64 || callbackVarBase64.trim() === '') {
        throw new Error('callbackVar参数为空')
      }

      const decoded = Buffer.from(callbackVarBase64, 'base64').toString('utf-8')

      return JSON.parse(decoded) as CallbackVarConfig
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误'
      this.logger.error('解密callbackVar失败:', errorMsg)
      throw new Error(`解密callbackVar配置失败: ${errorMsg}`)
    }
  }

  /**
   * 转换输入buffer为标准Buffer格式
   */
  private convertToBuffer(buffer: number[] | Uint8Array | Buffer): Buffer {
    if (Array.isArray(buffer)) {
      return Buffer.from(buffer)
    } else if (buffer instanceof Uint8Array) {
      return Buffer.from(buffer)
    } else {
      return buffer
    }
  }

  /**
   * 构建OSS callback选项
   */
  private buildOSSOptions(
    callbackConfig: CallbackConfig,
    callbackVarConfig: CallbackVarConfig
  ): MultipartUploadOptions {
    return {
      callback: {
        url: callbackConfig.callbackUrl,
        body: callbackConfig.callbackBody,
        contentType: callbackConfig.callbackBodyType,
        customValue: callbackVarConfig
      },
    }
  }

  /**
   * 创建OSS客户端
   */
  private createOSSClient(stsParams: STSSignature): OSS {
    const { bucket, accessKeyId, secretAccessKey, securityToken } = stsParams

    return new OSS({
      region: 'oss-cn-hangzhou',
      bucket: bucket,
      accessKeyId: accessKeyId,
      accessKeySecret: secretAccessKey,
      stsToken: securityToken,
      secure: true,
    })
  }
}
