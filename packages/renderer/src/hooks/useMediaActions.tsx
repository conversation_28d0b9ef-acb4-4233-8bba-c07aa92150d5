// hooks/useMediaActions.ts
import React, { useMemo } from 'react'
import { Edit, FolderInput, Trash } from 'lucide-react'
import { FolderActionKeys, actionLabels, NormalModeParams, RecycleModeParams } from '@/types/resources'
import { useItemActions } from '@/hooks/useItemActions'
import { MediaAction } from '@/components/material/MediaItem'

export type UseMediaActionsParams = NormalModeParams | RecycleModeParams

// 判断当前是否为回收站模式
function isRecycleMode(
  params: UseMediaActionsParams
): params is RecycleModeParams {
  return 'isRecycle' in params && params.isRecycle === true
}

export function useMediaActions(params: UseMediaActionsParams): MediaAction[] {
  const { renameItem, deleteItem, completeDeleteItem, backItem } = useItemActions()

  return useMemo<MediaAction[]>(() => {
    if (isRecycleMode(params)) {
      return [
        {
          icon: <FolderInput className="w-4 h-4" />,
          label: actionLabels[FolderActionKeys.BACK],
          value: FolderActionKeys.BACK,
          onClick: (fileId, fileName, _folderUuid) => {
            backItem(params.type, fileId, fileName)
          },
        },
        {
          icon: <Trash className="w-4 h-4" />,
          label: actionLabels[FolderActionKeys.COMPLETELY_DELETE],
          value: FolderActionKeys.COMPLETELY_DELETE,
          onClick: (fileId, fileName, _folderUuid) => {
            completeDeleteItem(params.type, fileId, fileName, true)
          },
        },
      ]
    }

    return [
      {
        icon: <Edit className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.RENAME],
        value: FolderActionKeys.RENAME,
        onClick: (fileId, fileName) => {
          renameItem(params.type, fileId, fileName, {
            label: '',
            headerTitle: '素材',
          })
        },
      },
      {
        icon: <FolderInput className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.MOVE],
        value: FolderActionKeys.MOVE,
        onClick: fileId => {
          params.setMoveType(params.type)
          params.setMoveId(fileId)
          params.setMoveDialogOpen(true)
        },
      },
      {
        icon: <Trash className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.DELETE],
        value: FolderActionKeys.DELETE,
        onClick: (fileId, fileName) => {
          if (params.isLocal) {
            return completeDeleteItem(params.type, fileId, fileName)
          }
          return deleteItem(params.type, fileId, fileName)
        },
      },
    ]
  }, [
    params,
    renameItem,
    deleteItem,
    completeDeleteItem,
    backItem,
  ])
}
