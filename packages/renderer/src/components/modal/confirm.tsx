import React, { useCallback } from 'react'
import { useModal, useModalContext } from '@/libs/tools/modal'
import { ModalContent, ModalContentProps } from '../modal'

function ConfirmContent(
  props: Pick<ModalContentProps, 'title' | 'description' | 'children' | 'onCancel' | 'onConfirm'>,
) {
  const { close } = useModalContext()

  return (
    <ModalContent
      {...props}
      onConfirm={() => {
        close()
        props.onConfirm?.()
      }}
    />
  )
}

export function useModalConfirm() {
  const modal = useModal()
  return useCallback(
    (props: Pick<ModalContentProps, 'title' | 'description' | 'children'>) =>
      new Promise<boolean>(resolve =>
        modal({
          content: <ConfirmContent {...props} onConfirm={() => resolve(true)} onCancel={() => resolve(false)} />,
        }),
      ),
    [],
  )
}
