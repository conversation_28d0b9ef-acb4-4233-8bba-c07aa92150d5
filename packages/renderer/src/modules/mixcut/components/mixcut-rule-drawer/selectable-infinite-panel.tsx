import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { InfiniteData, UseInfiniteQueryResult } from '@tanstack/react-query'
import { PaginatedResult } from '@app/shared/infra/request'
import { cn } from '@/components/lib/utils'
import { CheckedState } from '@radix-ui/react-checkbox'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import InfiniteResourceList from '@/components/InfiniteResourceList'

export function SelectableInfinitePanel<TItem extends { id: number }>(props: {
  onChange?: (val: TItem[]) => void,
  renderItem(item: TItem): React.ReactNode
  scrollAreaHeight?: number
  itemName?: string
  query: UseInfiniteQueryResult<InfiniteData<PaginatedResult<TItem>, unknown>, Error>
}) {
  const { query, renderItem, onChange, itemName = '选项', scrollAreaHeight = 60 * 4 } = props

  const checkboxId = 'NarrationTextFontFamilySelector'
  const [selected, setSelected] = useState(new Map<number, TItem>())

  const _renderItem = useCallback((item: TItem) => {
    return (
      <div
        className={cn(
          'cursor-pointer rounded-md border-2 flex justify-center',
          selected.has(item.id) && 'border-primary-accent/50'
        )}
        onClick={() => setSelected(prev => {
          const newMap = new Map(prev)
          if (prev.has(item.id)) {
            newMap.delete(item.id)
          } else {
            newMap.set(item.id, item)
          }

          return newMap
        })}
      >
        {renderItem(item)}
      </div>
    )
  }, [selected.keys(), renderItem])

  const checked = useMemo<CheckedState>(() => {
    if (!query?.data?.pages?.length) return false

    if (selected.size === 0) return false

    if (selected.size === query.data.pages[0].total) return true

    return 'indeterminate'
  }, [selected, query.data])

  const handleCheckedChange = (checked: CheckedState) => {
    if (checked === true) {
      const newMap = new Map()

      query.data?.pages.forEach(page => {
        page.list.forEach(font => {
          if (newMap.has(font.id)) return
          newMap.set(font.id, font)
        })
      })

      setSelected(newMap)
    } else {
      setSelected(new Map())
    }
  }

  useEffect(() => {
    onChange?.(Array.from(selected.values()))
  }, [selected])

  return (
    <div>
      <div className={cn('flex items-center space-x-2 mt-3')}>
        <Checkbox
          id={checkboxId}
          checked={checked}
          onCheckedChange={handleCheckedChange}
        />
        <Label htmlFor={checkboxId} className={cn('text-sm font-normal cursor-pointer')}>
          已选择 {selected.size} {`个${itemName}`}
        </Label>
      </div>

      <div className="overflow-y-auto" style={{ height: scrollAreaHeight }}>
        <div className="p-1 space-y-1">
          <InfiniteResourceList
            queryResult={query}
            renderItem={_renderItem}
            itemsContainerClassName="p-2 grid grid-cols-3 gap-2"
          />
        </div>
      </div>
    </div>
  )
}
