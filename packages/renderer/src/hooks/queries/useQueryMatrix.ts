import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { usePagination } from '../usePagination'
import { GenericMatrixModule, PlatformKey } from '@/libs/request/api/generic-matrix'
import {  BasePushDetail, BaseTimeRangeParams, PushPlanListSearchParams } from '@/modules/matrix/types/shared'

/**
 * 获取抖音账号概览数据
 * @param params 时间范围参数
 * @param enabled 是否启用查询
 */
export const useQueryDyAccountOverview = (
  platformKey: PlatformKey,
  params?: BaseTimeRangeParams,
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.DY_ACCOUNT_OVERVIEW, params, platformKey],
    queryFn: () => GenericMatrixModule.endpoints.accountOverview(platformKey, params),
  })
}

export const usePaginationPlanDetailList = (data: {
  platform: PlatformKey
  searchParams: PushPlanListSearchParams,
  initialPageSize: number,
  enabled?: boolean,
  refetchInterval?: number
}) => {
  const { searchParams, initialPageSize = 20, enabled, refetchInterval, platform } = data
  return usePagination<BasePushDetail, PushPlanListSearchParams>({
    queryKey: [QUERY_KEYS.ACCOUNT_PUSH_DETAIL, platform],
    queryFn: params =>  GenericMatrixModule.endpoints.pushInfoList(platform, params),
    searchParams,
    initialPageSize,
    enabled: enabled ? enabled : true,
    staleTime: 0,
    gcTime: 0,
    refetchInterval: refetchInterval || undefined
  })
}

