// 模糊转场组件
import React, { Suspense, useMemo, useRef } from 'react'
import { ThreeCanvas, useOffthreadVideoTexture, useVideoTexture } from '@remotion/three'
import { useRenderContext } from '../../render.context'
import { TransitionBaseProps } from '../../types'
import * as THREE from 'three'
import { Texture } from 'three'
import { getRemotionEnvironment, useVideoConfig, Video } from 'remotion'

// --- 简单的 9-tap 高斯模糊（单 pass 近似，够当 DEMO 用）---
const VERT = /* glsl */ `
  varying vec2 vUv;
  void main() {
    vUv = uv;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`

const FRAG = /* glsl */ `
  precision highp float;
  varying vec2 vUv;
  uniform sampler2D u_tex;
  uniform vec2 u_res;      // 纹理分辨率（像素）
  uniform float u_radius;  // 模糊半径（像素）

  // 经典 9-tap 高斯权重（来源于常用的快速高斯模糊写法）
  vec4 blur9(sampler2D img, vec2 uv, vec2 res, float radius) {
    vec2 off1 = vec2(1.3846153846) * radius / res;
    vec2 off2 = vec2(3.2307692308) * radius / res;

    // 横向
    vec4 h = texture2D(img, uv) * 0.2270270270;
    h += texture2D(img, uv + vec2(off1.x, 0.0)) * 0.3162162162;
    h += texture2D(img, uv - vec2(off1.x, 0.0)) * 0.3162162162;
    h += texture2D(img, uv + vec2(off2.x, 0.0)) * 0.0702702703;
    h += texture2D(img, uv - vec2(off2.x, 0.0)) * 0.0702702703;

    // 纵向
    vec4 v = texture2D(img, uv) * 0.2270270270;
    v += texture2D(img, uv + vec2(0.0, off1.y)) * 0.3162162162;
    v += texture2D(img, uv - vec2(0.0, off1.y)) * 0.3162162162;
    v += texture2D(img, uv + vec2(0.0, off2.y)) * 0.0702702703;
    v += texture2D(img, uv - vec2(0.0, off2.y)) * 0.0702702703;

    return 0.5 * (h + v); // 合并横+纵
  }

  void main() {
    gl_FragColor = blur9(u_tex, vUv, u_res, u_radius);
  }
`

type VideoRef = React.RefObject<HTMLVideoElement | null>

// 选择在预览或渲染时使用哪种视频纹理 Hook
const useSmartVideoTexture = (src: string, videoRef: VideoRef): Texture | null => {
  const env = getRemotionEnvironment()
  return env.isRendering
    ? useOffthreadVideoTexture({ src })
    : useVideoTexture(videoRef)
}

const VideoPlane: React.FC<{ src: string; videoRef: VideoRef; blurRadius?: number }> = ({ src, videoRef, blurRadius = 6 }) => {
  const tex = useSmartVideoTexture(src, videoRef)
  const materialRef = useRef<THREE.ShaderMaterial>(null)
  const { width, height } = useVideoConfig()

  // 纹理属性：线性过滤让模糊更顺滑
  // useMemo(() => {
  //   if (!tex) return
  //   tex.wrapS = tex.wrapT = THREE.ClampToEdgeWrapping
  //   tex.minFilter = THREE.LinearFilter
  //   tex.magFilter = THREE.LinearFilter
  //   tex.generateMipmaps = false
  //   tex.needsUpdate = true
  // }, [tex])

  const uniforms = useMemo(
    () => ({
      u_tex: { value: tex },
      u_res: { value: new THREE.Vector2(width, height) },
      u_radius: { value: blurRadius }, // 以“像素”为单位; 可用 props 控制
    }),
    [tex, width, height, blurRadius]
  )

  // 让平面基本铺满视口：用 16:9 / 9:16 都能良好覆盖
  const aspect = width / height
  const planeW = 2.0            // 按相机视口高度=2 的近似
  const planeH = planeW / aspect // 等比

  return (
    <mesh>
      <planeGeometry args={[planeW, planeH]} />
      {/*<shaderMaterial*/}
      {/*  ref={materialRef}*/}
      {/*  vertexShader={VERT}*/}
      {/*  fragmentShader={FRAG}*/}
      {/*  uniforms={uniforms}*/}
      {/*  transparent={false}*/}
      {/*  depthTest={false}*/}
      {/*  depthWrite={false}*/}
      {/*  toneMapped={false}*/}
      {/*/>*/}
    </mesh>
  )
}

export const BlurTransition: React.FC<TransitionBaseProps> = ({
  fromVideo,
  toVideo,
  // progress,
  // overlay
}) => {
  const { playerMetadata: { width, height } } = useRenderContext()

  // 计算模糊强度和透明度
  // const { fromBlur, fromOpacity, toBlur, toOpacity } = useMemo(() => {
  //   const maxBlur = overlay?.config?.intensity || 0.5 // 使用转场配置中的强度参数
  //
  //   if (progress <= 0.5) {
  //     // 前半段：fromVideo 从清晰变模糊
  //     const halfProgress = progress * 2
  //     return {
  //       fromBlur: halfProgress * maxBlur,
  //       fromOpacity: 1,
  //       toBlur: maxBlur,
  //       toOpacity: 0
  //     }
  //   } else {
  //     // 后半段：toVideo 从模糊变清晰
  //     const halfProgress = (progress - 0.5) * 2
  //     return {
  //       fromBlur: maxBlur,
  //       fromOpacity: 1 - halfProgress,
  //       toBlur: maxBlur * (1 - halfProgress),
  //       toOpacity: halfProgress
  //     }
  //   }
  // }, [progress, overlay?.config?.intensity])

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width,
    height,
    overflow: 'hidden',
  }

  // const layerStyle: React.CSSProperties = {
  //   position: 'absolute',
  //   top: 0,
  //   left: 0,
  //   width: '100%',
  //   height: '100%',
  // }

  const videoRef = useRef<HTMLVideoElement | null>(null)

  const src = fromVideo.localSrc

  if (!src) return null

  return (
    <div style={containerStyle} id={`blur-${fromVideo.id}-${toVideo.id}`}>
      <ThreeCanvas width={width} height={height} linear>
        {/* 这里使用透视相机，离平面近一些即可让 plane 基本填满画面 */}
        {/*<PerspectiveCamera makeDefault position={[0, 0, 1.5]} fov={50} />*/}
        <Suspense fallback={<span className="text-2xl text-red-500">FALLBACK</span>}>
          <VideoPlane src={src} videoRef={videoRef} blurRadius={4} />
        </Suspense>
      </ThreeCanvas>

      <Video
        ref={videoRef}
        src={src}
        style={{ position: 'absolute', opacity: 0 }}
      />
    </div>
  )
}
