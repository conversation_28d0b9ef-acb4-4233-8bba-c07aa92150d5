import React from 'react'
import {
  <PERSON><PERSON>,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  CustomDialogContent
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { cn } from '@/components/lib/utils'

interface TipModalProps {
  open: boolean
  title: string
  description: string
  confirmText?: string
  onConfirm: () => void
  onCancel?: () => void
}

export const TipModal: React.FC<TipModalProps> = ({
  open,
  title,
  description,
  confirmText = '确定',
  onConfirm,
  onCancel,
}) => {
  return (
    <Dialog open={open} onOpenChange={val => !val && onCancel?.()}>
      <CustomDialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex justify-end gap-2">
          {onCancel && (
            <Button variant="outline" className={cn('bg-white/5 hover:bg-white/10')} onClick={onCancel}>
              取消
            </Button>
          )}
          <Button
            onClick={onConfirm}
            className="min-w-[80px] h-8 bg-primary-highlight1 hover:bg-primary-highlight1/80 text-white"
          >
            {confirmText}
          </Button>
        </DialogFooter>
      </CustomDialogContent>
    </Dialog>
  )
}
