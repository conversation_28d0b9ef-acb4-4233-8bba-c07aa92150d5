import { PlatformKey } from '@/libs/request/api/generic-matrix'
import { PublishMode, PublishSetting, PublishTimeType, PeriodType, VideoMountMode } from '../enums/shared'
import { z } from 'zod'

export const platformEnum = z.enum([PlatformKey.dy, PlatformKey.xhs, PlatformKey.ks])

/**
 * 视频数据
 */
export const commonVideoDataSchema = z.object({
  cover: z.string(),
  url: z.string(),
  name: z.string(),
  customCover: z.string().optional(),
  orgCover: z.string().optional(),
})
export type CommonVideoData = z.infer<typeof commonVideoDataSchema>

/**
 * 发布计划公共配置
 */
export const commonPublishPlanSchema = z.object({
  name: z.string().min(1, '计划名称不能为空').max(30, '计划名称不能超过30个字符'),
  videoList: z.array(commonVideoDataSchema).min(1, '请至少选择一个视频')
})
export type CommonPublishPlan = z.infer<typeof commonPublishPlanSchema>

/**
 * 账号信息
 */
export const accountFormSchema = z.object({
  id: z.number(),
  avatar: z.string().optional(),
  nickname: z.string().optional()
})
export type AccountForm = z.infer<typeof accountFormSchema>

/**
 * 枚举 Schema
 */
export const videoMountModeSchema = z.nativeEnum(VideoMountMode)
export const publishModeSchema = z.nativeEnum(PublishMode)
export const publishSettingSchema = z.nativeEnum(PublishSetting)
export const publishTimeTypeSchema = z.nativeEnum(PublishTimeType)
export const periodTypeSchema = z.nativeEnum(PeriodType)

/**
 * 时间设置
 */
export const timeSettingSchema = z.object({
  /* 定时模式：发布时间 */
  publishTime: z.number().optional(),
  /* 定时模式： 间隔 */
  period: z.number().optional(),
  /* 定时模式间隔类型： 1-单账号间隔，2-多账号间隔 */
  periodType: z.array(periodTypeSchema).optional(),
  /* 循环模式：发布日期时间戳*/
  loopDays: z.array(z.number()).optional(),
  /* 循环模式：发布时间，格式为08:00:00  */
  loopTime: z.string().optional(),
  /* 循环模式： 循环发布间隔 */
  loopPeriod: z.number().optional(),
  /* 循环模式：每天发布视频数 */
  numEachDay: z.number().optional()
})

export const commonAccountProductInfoSchema = z.object({
  title: z.string(),
  url: z.string()
})

export const commonDetailPlanDOSchema = z.object({
  accountId: z.number(),
  cover: z.string(),
  description: z.string(),
  poiId: z.string().optional(),
  poiName: z.string().optional(),
  products: z.array(commonAccountProductInfoSchema).optional(),
  title: z.string(),
  url: z.string()
})

export const commonPoiSchema = z.object({
  poiId: z.string().optional(),
  poiName: z.string().optional(),
  
})

export const commonAccountProductSchema = z.object({
  accountInfo: accountFormSchema,
  products: z.array(commonAccountProductInfoSchema)
})

export const titleDescSchema = z.object({
  title: z.string().min(1, '标题不能为空'),
  desc: z.string().optional(),
})

export type DetailPlanDO = z.infer<typeof commonDetailPlanDOSchema>

export type TimeSetting = z.infer<typeof timeSettingSchema>
export type PositionInfo = z.infer<typeof commonPoiSchema>
export type AccountProductInfo = z.infer<typeof commonAccountProductInfoSchema>
export type AccountProduct = z.infer<typeof commonAccountProductSchema>

