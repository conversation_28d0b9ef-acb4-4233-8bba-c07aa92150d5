import { fetchInstance } from '../instance'
import { fetchPaginationGet, requestCurrying } from '../request'

export type ScriptSession = {
  id: number
  content: string
  title: string
  createTime: number
}

export type ChatMessage = {
  id: number
  modelId: number
  sessionId: number
  parentId: number | null
  content: string
  role: 'user' | 'assistant'
  runTime: number | null
  completionTokens: null | number
  promptTokens: null | number
  totalTokens: null | number
  createTime: number
  pending?: boolean
  isForm: boolean
}

export type ChatModel = {
  id: number
  name: string
  description: string
  icon: string
}

export type SSEMessage = {
  event: 'text'
  data: {
    id: number
    content: string
    promptTokens: number
    completionTokens: number
    totalTokens: number
    runTime: number
    createTime: number
  }
}

export type SendReq = {
  sessionId: number
  modelId: number
  content: string
}

export type PromptReq = {
  promptCode: string
  content: string
}

export type FormReq = {
  sessionId: number
  promptCode: string
  params: string
}

export type FormConfigItem = {
  promptCode: string
  formContent: string
}

export type FormExampleItem = {
  name: string
  promptCode: string
  formContent: string
  remark: string
}

export type FormItem = {
  label: string
  type: 'input' | 'textarea' | 'select' | 'radio' | 'checkbox'
  placeholder?: string
  options?: string[]
}

const requestSSE = <D extends {}, T extends { event: string; data: any }>(url: string) => {
  return async (data: D, { onMessage }: { onMessage: (data: T) => void }) => {
    const res: ReadableStream = await fetchInstance.post(url, data, { responseType: 'stream', timeout: 0 })
    const reader: ReadableStreamDefaultReader<Uint8Array> = res.getReader()
    const decoder = new TextDecoder('utf-8')

    let buffer = ''
    const processStream = async () => {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        if (buffer.includes('\n\n')) {
          const parts = buffer.split('\n\n')
          buffer = parts.pop()!
          parts.forEach(part => {
            const lines = part
              .split('\n')
              .map(line => line.trim())
              .filter(line => line)
            const event =
              lines
                .find(line => line.startsWith('event:'))
                ?.slice(6)
                .trim() ?? 'message'
            const dataText = lines
              .find(line => line.startsWith('data:'))
              ?.slice(5)
              .trim()
            const data: T['data'] = dataText ? JSON.parse(dataText) : undefined
            onMessage({ event, data } as T)
          })
        }
      }
    }

    return processStream()
  }
}

export const ScriptChatAPI = {
  create: requestCurrying.post<{ scriptId: number }, { sessionId: number }>('/app-api/script/session/create'),
  list: fetchPaginationGet<ScriptSession, { scriptId: number }>('/app-api/script/session/page'),
  update: requestCurrying.post<{ id: number; title: string }, boolean>('/app-api/script/session/title'),
  delete: requestCurrying.delete<{ id: number }, boolean>('/app-api/script/session/delete'),
  models: requestCurrying.get<{}, ChatModel[]>('/app-api/script/model/list'),
  messages: fetchPaginationGet<ChatMessage, { sessionId: number }>('/app-api/script/message/page'),
  send: requestSSE<SendReq, SSEMessage>('/app-api/script/message/send'),
  prompt: requestSSE<PromptReq, SSEMessage>('/app-api/script/message/prompt'),
  genForm: requestSSE<FormReq, SSEMessage>('/app-api/script/message/form'),
  categories: requestCurrying.get<{ id: number }, { id: number; name: string }[]>('/app-api/script/category/list'),
  formConfig: requestCurrying.get<{ id: number }, FormConfigItem>('/app-api/script/form/form_config'),
  formExample: requestCurrying.get<{ id: number }, FormExampleItem[]>('/app-api/script/form/form_example'),
}
