import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, CustomDialog<PERSON>ontent, <PERSON>alog<PERSON>ooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import TreeList, { TreeNode } from '@/components/TreeList'

interface PermissionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const PermissionDialog: React.FC<PermissionDialogProps> = ({ open, onOpenChange }) => {
  const [selectedNode, setSelectedNode] = useState<any>(null)
  const [treeData, setTreeData] = useState<TreeNode[]>([])

  useEffect(() => {
    if (open) {
      const fetchData = async () => {
        const data = [
          {
            id: '1',
            label: 'Folder 1',
            children: [
              {
                id: '2',
                label: 'Subfolder 1-1',
                children: [
                  {
                    id: '4',
                    label: 'Subfolder 1-1-1',
                    children: [],
                  },
                ],
              },
              {
                id: '3',
                label: 'Subfolder 1-2',
                children: [],
              },
            ],
          },
          {
            id: '22',
            label: 'Folder 1',
            children: [
              {
                id: '221',
                label: 'Subfolder 22-1',
                children: [],
              },
              {
                id: '223',
                label: 'Subfolder 22-3',
                children: [],
              },
            ],
          },
        ]
        setTreeData(data)
      }
      fetchData()
    }
  }, [open])

  const handleConfirm = async () => {
    if (!selectedNode || selectedNode.type === 'project') return

    try {
      // onConfirm?.(selectedNode)
      onOpenChange(false)
    } catch (error) {
      throw new Error(error.message)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <CustomDialogContent>
        <DialogHeader>
          <DialogTitle>权限设置</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-auto py-4">
          <TreeList
            data={treeData}
            className="w-full flex-1 max-h-[500px] overflow-auto"
            selectStyle="text-white"
            showEllipsis={false}
            onSelect={node => {
              setSelectedNode(node)
            }}
            multiple={true}
            defaultExpandAll
            onChangeSelection={nodes => {
              console.log('选中的节点: ', nodes)
            }}
          />
        </div>

        <DialogFooter>
          <button onClick={() => onOpenChange(false)} className="px-4 py-2 bg-primary/20 rounded-md">
            取消
          </button>
          <button onClick={handleConfirm} className="px-4 py-2 bg-primary-highlight1 text-white rounded-md">
            确定
          </button>
        </DialogFooter>
      </CustomDialogContent>
    </Dialog>
  )
}

export default PermissionDialog
