import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut.js'
import { SimpleMixcutPipeline } from '../mixcut-pipeline.types.js'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { TextOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'
import { MixcutPipelineUtils } from '@/modules/mixcut/pipeline/mixcut-pipeline.utils.js'

export class GlobalTextStyledTextPipeline extends SimpleMixcutPipeline<typeof MIXCUT_PIPELINES.globalText.styledText, TextOverlay> {

  async processSingleOverlay(overlay: TextOverlay & MixcutableOverlay) {
    const config = _.sample(this.context.params.options)
    if (!config) return overlay

    return {
      ...overlay,
      styles: MixcutPipelineUtils.mergeStyles(overlay.styles, config)
    }
  }

  filter(overlay: MixcutableOverlay): overlay is TextOverlay & MixcutableOverlay {
    return SimpleMixcutPipeline.filters.globalText(overlay)
  }
}
