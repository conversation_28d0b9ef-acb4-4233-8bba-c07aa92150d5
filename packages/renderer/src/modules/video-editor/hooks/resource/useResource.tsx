import { useCallback } from 'react'
import { ResourceCacheType } from '@app/shared/types/resource-cache.types'
import { cacheManager } from '@/libs/cache/cache-manager'
import { PasterResource } from '@/types/resources'
import { useStickerLoadingStore } from './useStickerLoadingStore'

const loadingResources: Record<string, Record<string, boolean>> = {}

Object.values(ResourceCacheType).forEach(type => {
  loadingResources[type] = {}
})

const preloadedImages: Record<string, HTMLImageElement> = {}

type DownloadResourceToCacheProps = {
  url: string;
  resourceType: ResourceCacheType;
  version?: string;
  customExt?: string;
  id?: string | number;
}

export const useResource = () => {
  // 获取 Zustand store 的方法
  const { getStickerState, startStickerLoading, endStickerLoading } = useStickerLoadingStore()

  /**
   * 下载资源到本地缓存
   */
  const downloadResourceToCache = async (data: DownloadResourceToCacheProps): Promise<string | null> => {
    // 参数验证
    if (!data || !data.url || !data.resourceType) {
      console.error('[资源下载] 参数无效:', data)
      return null
    }

    // 验证 ResourceType 枚举值
    if (!Object.values(ResourceCacheType).includes(data.resourceType)) {
      console.error('[资源下载] 无效的资源类型:', data.resourceType)
      return null
    }

    try {
      return cacheManager
        .resource
        .cacheResource(data.resourceType, data.url, data.version, data.customExt)
    }
    catch (error) {
      console.error('获取资源路径失败:', error)
      return null
    }
  }

  /**
   * 获取贴纸的加载状态
   */
  const getStickerLoadingState = useCallback((stickerId: string | number): PasterResource.StickerLoadingState => {
    const state = getStickerState(stickerId)

    return {
      coverId: state.stickerId,
      coverLoaded: state.coverLoaded,
      thumbLoaded: state.thumbLoaded,
      thumbLoading: state.thumbLoading,
      fileLoaded: state.fileLoaded,
      fileLoading: state.fileLoading,
      currentLayer: state.currentLayer as PasterResource.LoadingLayer
    }
  }, [getStickerState])

  const updateStickerLoadingState = useCallback((
    stickerId: string | number,
    updates: Partial<PasterResource.StickerLoadingState>
  ) => {
    // 根据更新内容智能调用对应的 API

    // 处理 thumb 层级
    if (updates.thumbLoading === true) {
      startStickerLoading(stickerId, 'thumb')
    } else if (updates.thumbLoading === false) {
      const success = updates.thumbLoaded === true
      endStickerLoading(stickerId, 'thumb', success)
    } else if (updates.thumbLoaded === true) {
      endStickerLoading(stickerId, 'thumb', true)
    }

    // 处理 file 层级
    if (updates.fileLoading === true) {
      startStickerLoading(stickerId, 'file')
    } else if (updates.fileLoading === false) {
      const success = updates.fileLoaded === true
      endStickerLoading(stickerId, 'file', success)
    } else if (updates.fileLoaded === true) {
      endStickerLoading(stickerId, 'file', true)
    }

    // 处理 cover 层级
    if (updates.coverLoaded === true) {
      endStickerLoading(stickerId, 'cover', true)
    }
  }, [startStickerLoading, endStickerLoading])

  /**
   * 预加载图片（用于悬停预览）
   */
  const preloadImage = useCallback(async (url: string): Promise<HTMLImageElement> => {
    // 如果已经预加载过，直接返回
    if (preloadedImages[url]) {
      return preloadedImages[url]
    }

    // 检查 IndexedDB 中是否已缓存
    const isPreloaded = await cacheManager.image.isImagePreloaded(url)
    if (isPreloaded) {
      const cachedData = await cacheManager.image.getPreloadedImage(url)
      if (cachedData) {
        const img = new Image()
        img.src = cachedData
        preloadedImages[url] = img
        return img
      }
    }

    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = async () => {
        preloadedImages[url] = img

        // 将图片数据缓存到 IndexedDB
        try {
          await cacheManager.image.setPreloadedImage(url, img.src)
        } catch (error) {
          console.warn('缓存预加载图片失败:', error)
        }

        resolve(img)
      }
      img.onerror = reject
      img.src = url
    })
  }, [])

  /**
   * 贴纸悬停预览加载
   */
  const loadStickerPreview = useCallback(async (
    sticker: PasterResource.Paster | PasterResource.SharedPaster
  ): Promise<void> => {
    const stickerId = 'fileId' in sticker ? sticker.fileId : sticker.id
    const thumbUrl = sticker.content.thumbUrl

    // 获取当前状态
    const currentState = getStickerLoadingState(stickerId)

    // 如果已经加载过或正在加载，直接返回
    if (currentState.thumbLoaded || currentState.thumbLoading) {
      return
    }

    // 设置加载状态
    updateStickerLoadingState(stickerId, {
      thumbLoading: true,
      currentLayer: PasterResource.LoadingLayer.THUMB
    })

    try {
      // 预加载缩略图
      await preloadImage(thumbUrl)

      // 更新状态为已加载
      updateStickerLoadingState(stickerId, {
        thumbLoaded: true,
        thumbLoading: false
      })

      console.log(`贴纸预览加载完成: ${thumbUrl}`)
    } catch (error) {
      console.error('贴纸预览加载失败:', error)

      // 重置加载状态
      updateStickerLoadingState(stickerId, {
        thumbLoading: false,
        currentLayer: PasterResource.LoadingLayer.COVER
      })
    }
  }, [getStickerLoadingState, updateStickerLoadingState, preloadImage])

  /**
   * 贴纸完整资源缓存（用于编辑器）
   */
  const cacheStickerForEditor = useCallback(async (
    sticker: PasterResource.Paster | PasterResource.SharedPaster
  ): Promise<string | null> => {
    const stickerId = 'fileId' in sticker ? sticker.fileId : sticker.id
    const fileUrl = sticker.content.fileUrl

    // 获取当前状态
    const currentState = getStickerLoadingState(stickerId)

    // 如果已经缓存过，直接返回路径
    if (currentState.fileLoaded) {
      return cacheManager.resource.getResourcePathSync(ResourceCacheType.PASTER, fileUrl)
    }

    // 如果正在加载，等待完成
    if (currentState.fileLoading) {
      // 这里可以实现等待逻辑，或者直接返回null
      return null
    }

    // 设置加载状态
    updateStickerLoadingState(stickerId, {
      fileLoading: true,
      currentLayer: PasterResource.LoadingLayer.FILE
    })

    try {
      // 下载并缓存完整资源
      const localPath = await downloadResourceToCache({
        url: fileUrl,
        resourceType: ResourceCacheType.PASTER,
        id: stickerId
      })

      // 更新状态为已缓存
      updateStickerLoadingState(stickerId, {
        fileLoaded: true,
        fileLoading: false
      })

      console.log(`贴纸完整资源缓存完成: ${fileUrl} -> ${localPath}`)
      return localPath
    } catch (error) {
      console.error('贴纸完整资源缓存失败:', error)

      // 重置加载状态
      updateStickerLoadingState(stickerId, {
        fileLoading: false
      })

      return null
    }
  }, [getStickerLoadingState, updateStickerLoadingState, downloadResourceToCache])

  return {
    downloadResourceToCache,
    // 三层加载策略相关方法
    getStickerLoadingState,
    updateStickerLoadingState,
    loadStickerPreview,
    cacheStickerForEditor,
    preloadImage
  }
}
