import { useQueryClient } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import { TeamAPI } from '@/libs/request/api/team'
import { TeamManager } from '@/libs/storage'
import { toast } from 'react-toastify'
import { QUERY_KEYS } from '@/constants/queryKeys'

/**
 * 团队相关操作的 Hook
 */
export function useTeam() {
  const queryClient = useQueryClient()
  const navigate = useNavigate()

  const switchTeam = async (teamId: number) => {
    try {
      TeamManager.switch(teamId)
      await queryClient.resetQueries()
      navigate('/', { replace: true })
    } catch (error) {
      toast.error('切换团队失败')
    }
  }

  const joinTeam = async (inviteCode: string, redirect = true) => {
    try {
      const teamId = await TeamAPI.join({ inviteCode })
      if (redirect) {
        switchTeam(teamId)
      } else {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.TEAM_LIST] })
      }
      return teamId
    } catch (error: any) {
      toast.error(error?.message || '加入团队失败')
    }
  }

  // const leaveTeam = async (teamId: string, redirect = true) => {
  //   await leaveTeamCore(teamId, queryClient)
  //   if (redirect) navigate('/teams', { replace: true })
  // }

  // const createTeam = async (data: { name: string }, redirect = true) => {
  //   const teamId = await createTeamCore(data, queryClient)
  //   if (redirect) navigate(`/teams/${teamId}`, { replace: true })
  //   return teamId
  // }

  return {
    switchTeam,
    joinTeam,
    
    // leaveTeam,
    // createTeam,
  }
}