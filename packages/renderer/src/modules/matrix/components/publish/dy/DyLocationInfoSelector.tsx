import React from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { CHINA_PROVINCES } from '@/constants/system'
import { PublishData } from '@/modules/matrix/schemas/publish.schema'

export const DyLocationInfoSection = () => {
  const { control, formState: { errors } } = useFormContext<PublishData>()
  return (
    <Card>
      <CardHeader>
        <CardTitle>位置信息</CardTitle>
        <CardDescription>
          配置位置挂载相关信息
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center gap-6">
          <div className="space-y-2">
            <Label htmlFor="poiId" className="flex items-center gap-2">
              省份 <span className="text-red-500">*</span>
            </Label>
            <Controller
              control={control}
              name="platforms.dy.poi.poiId"
              render={({ field: { value, onChange } }) => (
                <Select onValueChange={onChange} value={value}>
                  <SelectTrigger className={errors?.platforms?.dy?.poi?.poiId ? 'border-red-500' : ''}>
                    <SelectValue placeholder="请选择省份" />
                  </SelectTrigger>
                  <SelectContent>
                    {CHINA_PROVINCES.map(province => (
                      <SelectItem key={province.id} value={province.id}>
                        {province.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                  
              )}
            />
            {errors.platforms?.dy?.poi?.poiId && (
              <p className="text-sm text-red-500">{errors.platforms?.dy?.poi?.poiId?.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="poiName" className="flex items-center gap-2">
              详细地址 <span className="text-red-500">*</span>
            </Label>
            <Input
              id="poiName"
              placeholder="请输入详细地址"
              {...control.register('platforms.dy.poi.poiName')}
              className={errors.platforms?.dy?.poi?.poiName ? 'border-red-500' : ''}
            />
            {errors.platforms?.dy?.poi?.poiName && (
              <p className="text-sm text-red-500">{errors.platforms?.dy?.poi?.poiName?.message}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}