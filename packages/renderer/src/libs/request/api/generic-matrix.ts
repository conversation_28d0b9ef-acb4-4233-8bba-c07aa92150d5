import request, { fetchPagination } from '../request'
import { DyPlanInfo, } from '@/modules/matrix/types/dy'

import { XhsPlanInfo, } from '@/modules/matrix/types/xhs'
import {
  Account,
  AccountSearchParams,
  BaseAccountOverview,
  BaseCreateGroup,
  BaseGroupAccount,
  BaseGroupAccountSearchParams,
  BaseGroupItem,
  BasePushDetail,
  BasePushPlan,
  BaseTimeRangeParams,
  BaseUpdateGroup,
  PushPlanListSearchParams
} from '@/modules/matrix/types/shared'
import { PlatformPublishForm } from '@/modules/matrix/schemas/publish.schema'
import { DraftInfo } from '@/modules/matrix/types/shared/draft'
import { PaginationParams } from '@app/shared/infra/request'

/**
 * @description: 平台类型
 * @TODO_PLATFORM: 新增平台时，需要在@/modules/下位置添加对应平台类型信息：
 *   1. PlatformKey 枚举
 *   2. PublishChannelSelector.tsx
 *   3. matrix/constants/shared.ts - platformIconMap, PLATFORM_CONFIG
 *   4. matrix/schemas/publish.schema.ts - platformEnum
 *   5. matrix/types/shared/draft.ts - PlatformDraftMap
 *   6. matrix/utils/shared.ts - MergedFormMap, platformVideoTitleMap
 *   请确保同步更新。
 */
export enum PlatformKey {
  /**  抖音 */
  dy ='dy',
  /** 小红书 */
  xhs = 'xhs',
  /** 快手 */
  ks = 'ks'
}

// 公共基础类型
type CommonPlatformTypes = {
  Account: Account
  AccountSearchParams: AccountSearchParams
  AccountOverview: BaseAccountOverview
  PushPlan: BasePushPlan
  PushDetail: BasePushDetail
  PushDetailListRequestParams: PushPlanListSearchParams
  GroupAccount: BaseGroupAccount
  GroupAccountSearchParams: BaseGroupAccountSearchParams
  GroupItem: BaseGroupItem
  CreateGroup: BaseCreateGroup
  UpdateGroup: BaseUpdateGroup
  TimeRangeParams: BaseTimeRangeParams
}

// 平台特有类型映射
interface PlatformSpecificTypes {
  dy: {
    PublishFormDetail: DyPlanInfo
  }
  xhs: {
    PublishFormDetail: XhsPlanInfo
  }
  ks: {
    PublishFormDetail: XhsPlanInfo
  }
}

// 泛型类型组合
type PlatformConfigOptimized<T extends PlatformKey> = CommonPlatformTypes & PlatformSpecificTypes[T] & {
  PublishParams: PlatformPublishForm<T>
}

// 最终配置映射
export type ConfigByPlatform = {
  [K in PlatformKey]: PlatformConfigOptimized<K>
}

export namespace GenericMatrixModule {

  // 通用接口方法
  export const endpoints = {
    // ===== 分组管理 =====
    createGroup<P extends PlatformKey>(platform: P, data: ConfigByPlatform[P]['CreateGroup']) {
      return request.post(`/app-api/publishing/${platform}-group/add`, data)
    },

    updateGroup<P extends PlatformKey>(platform: P, data: ConfigByPlatform[P]['UpdateGroup']) {
      return request.put(`/app-api/publishing/${platform}-group/update`, data)
    },

    deleteGroup<P extends PlatformKey>(platform: P, id: number) {
      return request.delete(`/app-api/publishing/${platform}-group/delete?id=${id}`)
    },

    groupList<P extends PlatformKey>(platform: P) {
      return request.get<ConfigByPlatform[P]['GroupItem'][]>(`/app-api/publishing/${platform}-group/list`)
    },

    groupAccount<P extends PlatformKey>(platform: P, data: ConfigByPlatform[P]['GroupAccountSearchParams']) {
      return fetchPagination<ConfigByPlatform[P]['GroupAccount']>(`/app-api/publishing/${platform}-group/account-page`, data)
    },

    addAccountToGroup<P extends PlatformKey>(platform: P, data: { groupId: number, accountIds: number[] }) {
      return request.post(`/app-api/publishing/${platform}-group/add-account`, data)
    },

    deleteAccountFromGroup<P extends PlatformKey>(platform: P, data: { groupId: number, accountIds: number[] }) {
      return request.post(`/app-api/publishing/${platform}-group/delete-account`, data)
    },

    // ===== 账号管理 =====
    accountList<P extends PlatformKey>(platform: P, params: ConfigByPlatform[P]['AccountSearchParams']) {
      return fetchPagination<ConfigByPlatform[P]['Account']>(`/app-api/publishing/${platform}-account/page`, params)
    },

    accountPlanList<P extends PlatformKey>(platform: P, data: { accountIds: number[], limit: number }) {
      return request.post<{ id: number, name: string }>(`/app-api/publishing/${platform}-push-plan/list-plan`, data)
    },

    getAuthCode<P extends PlatformKey>(platform: P) {
      return request.get<string>(`/app-api/publishing/${platform}-auth/get-code`)
    },

    accountOverview<P extends PlatformKey>(platform: P, data?: ConfigByPlatform[P]['TimeRangeParams']) {
      return request.post<ConfigByPlatform[P]['AccountOverview']>(`/app-api/publishing/${platform}-account/overview`, data)
    },

    deleteAccount<P extends PlatformKey>(platform: P, id: number) {
      return request.delete(`/app-api/publishing/${platform}-account/delete?id=${id}`)
    },

    // ===== 推送计划管理 =====
    pushPlanList<P extends PlatformKey>(platform: P, data: PaginationParams) {
      return fetchPagination<ConfigByPlatform[P]['PushPlan']>(`/app-api/publishing/${platform}-push-plan/page`, data)
    },

    publish<P extends PlatformKey>(platform: P, data: ConfigByPlatform[P]['PublishParams']) {
      return request.post(`/app-api/publishing/${platform}-push-plan/create`, data)
    },

    taskDetail<P extends PlatformKey>(platform: P, id: number) {
      return request.get<ConfigByPlatform[P]['PublishFormDetail']>(`/app-api/publishing/${platform}-push-plan/detail?id=${id}`)
    },

    pushInfoList<P extends PlatformKey>(platform: P, data: ConfigByPlatform[P]['PushDetailListRequestParams']) {
      return fetchPagination<ConfigByPlatform[P]['PushDetail']>(`/app-api/publishing/${platform}-push-detail/page`, data)
    },

    // ===== 推送详情管理 =====
    deletePushDetail<P extends PlatformKey>(platform: P, id: number) {
      return request.delete(`/app-api/publishing/${platform}-push-detail/delete?id=${id}`)
    }
  }

  export const common = {
    // ===== 草稿管理 =====
    saveDraft(data: Omit<DraftInfo, 'id'>) {
      return request.post('/app-api/publishing/push-draft/save', data)
    },
    draftList() {
      return request.get<DraftInfo[]>('/app-api/publishing/push-draft/list')
    },
    draftDetail(id: string) {
      return request.get<DraftInfo>(`/app-api/publishing/push-draft/detail?id=${id}`)
    },
    deleteDraft(id: number) {
      return request.delete(`/app-api/publishing/push-draft/delete?id=${id}`)
    },
    batchDeleteDraft( params: { ids: number[] }) {
      return request.delete(`/app-api/publishing/push-draft/delete-list?ids=${params.ids}`)
    },
  }
}
