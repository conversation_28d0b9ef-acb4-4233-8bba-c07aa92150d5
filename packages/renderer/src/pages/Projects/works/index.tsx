import React, { useEffect, useMemo, useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  ArrowDownAz,
  ClipboardCheck,
  Download,
  Edit3,
  EllipsisVertical,
  Link,
  Loader2,
  <PERSON>f<PERSON><PERSON><PERSON>,
  Share2,
  Trash,
} from 'lucide-react'
import { DateRangePicker } from '@/components/date-range-picker'
import { DateRange } from 'react-day-picker'
import { useParams, useSearchParams } from 'react-router'
import { useInfiniteQueryScriptList } from '@/hooks/queries/useQueryScript'
import { cn } from '@/components/lib/utils'
import { Work } from '@/types/project'
import { useInfiniteQueryWorkList } from '@/hooks/queries/useQueryWork'
import { Selects } from './selects'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { useQuerySystemDict } from '@/hooks/queries/useQuerySystemDict'
import { useDownload } from './modals'
import { useProjectsContext } from '../context'
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver'
import { CheckboxHeader, CheckboxItem, CheckboxProvider, useCheckboxContext } from '@/components/checkbox'
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore'
import { toast } from 'react-toastify'
import { Separator } from '@/components/ui/separator'
import { useModalWorkEdit } from './modal-work-edit'
import { ResourceModule } from '@/libs/request/api/resource'
import { AuthedImg } from '@/components/authed-img'
import { VideoPreviewPopover } from '@/modules/matrix/components/VideoPreviewModal'
import { EditorModule } from '@/libs/request/api/editor'

function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

function WorkCard({ work }: { work: Work }) {
  const { data: composeStatusOptions } = useQuerySystemDict('project_work_select_composeStatus')
  const { pushNamedTab } = useVirtualTabsStore()
  const [open, setOpen] = useState(false)
  const [previewOpen, setPreviewOpen] = useState(false)
  const queryClient = useQueryClient()
  const modalDownload = useDownload()
  const modalWorkEdit = useModalWorkEdit()

  return (
    <div className="group aspect-[3/4] flex flex-col rounded-lg bg-muted-foreground/10 backdrop-blur-xs shadow-sm border hover:shadow-md transition-all">
      <div className="relative flex-1 w-full rounded-t-lg overflow-hidden group aspect-auto">
        {work.composeStatus === 2 && work.cover ? (
          <AuthedImg src={work.cover} alt={work.name} className="h-full w-full object-cover" draggable="false" />
        ) : (
          <>
            {work.composeStatus === 1 && (
              <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 animate-spin">
                <Loader2 className="size-8 text-muted-foreground/50" />
              </div>
            )}
          </>
        )}

        <VideoPreviewPopover
          open={previewOpen}
          onOpenChange={setPreviewOpen}
          trigger={<div className="absolute inset-0" />}
          video={{ url: work.url.split('/').at(-1)!, name: work.name, cover: work.cover }}
          className="w-[300px]"
        />
        <div
          className="absolute inset-0 z-10 flex flex-col gap-1 p-2 text-xs text-white leading-none
          [&_.tag]:bg-black/40 [&_.tag]:rounded [&_.tag]:py-0.5 [&_.tag]:px-1 cursor-pointer"
          onClick={e => work.composeStatus === 2 && setPreviewOpen(e.target !== e.currentTarget ? false : !previewOpen)}
        >
          <div className="flex gap-1 items-center">
            <span className="tag">重复率 {work.repetitionRate / 100}%</span>
            <span className="tag">
              {composeStatusOptions?.find(item => item.value === work.composeStatus.toString())?.label || '未知状态'}
            </span>
            <CheckboxItem
              value={work}
              id={work.id.toString()}
              className="ml-auto border-primary bg-background/20! aria-checked:bg-primary/50!"
            />
          </div>
          <div className="flex gap-1 items-center">
            <span className="tag flex gap-0.5 items-center">
              <Link className="size-3" />
              <span>关联任务</span>
              <span>1</span>
            </span>
            <span className="tag">ID: {work.id}</span>
          </div>
          <div className="mt-auto flex items-center">
            <span className="tag">{formatDuration(work.duration)}</span>
            <span className="tag ml-auto flex gap-0.5 items-center">
              <ClipboardCheck className="size-3" />
              <span>审片批注</span>
              <span>0</span>
            </span>
          </div>
        </div>
      </div>

      {/* 作品信息 */}
      <div className="p-2 w-full min-w-0 flex items-center justify-between">
        <div className="w-full min-w-0 flex flex-col items-start justify-between gap-1">
          <span className="block text-sm truncate w-full font-semibold" title={work.name}>
            {work.name}
          </span>
          <span className="block border border-primary bg-primary/10 text-primary text-xs leading-none p-0.5 rounded">
            未审核
          </span>
        </div>
        <DropdownMenu open={open} onOpenChange={setOpen}>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="shrink-0">
              <EllipsisVertical className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="flex flex-col gap-1 min-w-36 *:flex *:gap-2 *:items-center *:justify-start"
            onClick={() => setOpen(false)}
          >
            <Button variant="ghost" onClick={() => modalWorkEdit(work)}>
              <Edit3 className="size-4" />
              重命名
            </Button>
            <Button
              variant="ghost"
              onClick={() => {
                pushNamedTab('Script', { id: work.scriptId.toString() })
              }}
            >
              <Link className="size-4" />
              查看关联脚本
            </Button>
            <Button variant="ghost" onClick={() => toast.info('即将上线，敬请期待')}>
              <Share2 className="size-4" />
              分享视频
            </Button>
            {/* 下载视频 */}
            <Button
              variant="ghost"
              onClick={() => {
                modalDownload([{ url: work.url, filename: `${work.name}.mp4` }])
              }}
            >
              <Download className="size-4" />
              下载视频
            </Button>
            <Separator orientation="horizontal" />
            <Button
              variant="ghost"
              className="text-destructive hover:text-destructive"
              onClick={async () => {
                await ResourceModule.work.delete({ ids: [work.id] })
                await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.WORK_LIST] })
                toast.success('删除成功')
              }}
            >
              <Trash className="size-4" />
              删除
            </Button>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}

function Sidebar() {
  const params = useParams()
  const { data } = useInfiniteQueryScriptList({ projectId: params.projectId })
  const scripts = useMemo(() => data?.pages.flatMap(v => v.list) || [], [data])
  const [searchParams, setSearchParams] = useSearchParams()
  const selectedScriptId = searchParams.get('scriptId') ?? undefined

  function Item({ title, id }: { title: string; id?: string }) {
    return (
      <Button
        variant="ghost"
        size="lg"
        className={cn(
          'flex justify-start px-4 hover:bg-primary/10',
          selectedScriptId === id && 'bg-primary/20 hover:bg-primary/17 text-primary',
        )}
        onClick={() => setSearchParams(id ? `?scriptId=${id}` : '')}
      >
        {title}
      </Button>
    )
  }

  return (
    <div className="w-60 border-r flex flex-col p-4 gap-1">
      <Item title="全部脚本" />
      {scripts.map(script => (
        <Item key={script.id} title={script.title} id={script.id.toString()} />
      ))}
    </div>
  )
}

export default function WorksWrapper() {
  return (
    <CheckboxProvider>
      <Works />
    </CheckboxProvider>
  )
}

function Works() {
  const params = useParams()
  const [searchParams] = useSearchParams()
  const { keyword } = useProjectsContext()
  const [range, setRange] = useState<DateRange | undefined>(undefined)
  const modalDownload = useDownload()
  const { selected } = useCheckboxContext<Work>()

  const createAt = useMemo((): string | undefined => {
    if (!range?.from || !range?.to) return undefined
    return `${range.from.getTime()},${range.to.getTime() + 86400000}`
  }, [range])

  const { data: selects } = useQuerySystemDict('project_work_select')
  const tags = useMemo(() => {
    const getTag = (v: string) => v.split('_').at(-1) ?? v
    const tags = selects?.map(v => getTag(v.value)) || []
    const entries = tags.map(tag => [tag, searchParams.get(tag) ?? undefined])
    return Object.fromEntries(entries.filter(([, value]) => value !== undefined))
  }, [selects, searchParams])
  const { data, fetchNextPage, refetch } = useInfiniteQueryWorkList({
    ...tags,
    projectId: params.projectId,
    scriptId: searchParams.get('scriptId') ?? undefined,
    keyword,
    createAt,
    distStatus: searchParams.get('dist') ?? undefined,
  })
  const scrollRef = React.useRef<HTMLDivElement>(null)
  const NextPageFetcher = useIntersectionObserver({
    onIntersect: () => fetchNextPage(),
    deps: [data],
    root: scrollRef.current,
  })
  const works = useMemo(() => data?.pages.flatMap(v => v.list) || [], [data])
  const timerRef = React.useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    let ids = works.filter(work => work.composeStatus === 1).map(work => work.composeTaskNo)
    if (ids.length === 0) return
    const clear = () => {
      if (timerRef.current) clearInterval(timerRef.current)
      timerRef.current = null
    }
    if (timerRef.current) clear()
    timerRef.current = setInterval(async () => {
      const res = await EditorModule.listRender(ids)
      if (res.some(v => v.status === 4)) refetch()
      ids = res.filter(v => [0, 1, 2, 3, 5].includes(v.status)).map(v => v.taskNo)
      if (ids.length === 0) return clear()
    }, 3000)
    return clear
  }, [works])

  return (
    <div className="flex flex-col flex-1 min-h-0">
      <div className="flex flex-wrap items-center gap-4 p-4 border-b">
        <div className="flex w-full items-center gap-2 p-px">
          <DateRangePicker value={range} onChange={setRange} />
          <Button
            variant="default"
            className="ml-auto"
            disabled={Object.keys(selected).length === 0}
            onClick={() => toast.info('即将上线，敬请期待')}
          >
            分享视频
          </Button>
          <Button
            variant="default"
            disabled={Object.keys(selected).length === 0}
            onClick={() => toast.info('即将上线，敬请期待')}
          >
            合成视频
          </Button>
          <Button
            variant="default"
            disabled={Object.keys(selected).length === 0}
            onClick={() => {
              modalDownload(Object.values(selected).map(work => ({ url: work.url, filename: `${work.name}.mp4` })))
            }}
          >
            下载视频
          </Button>
        </div>

        <Selects data={selects} />
      </div>

      <div className="flex flex-1 overflow-auto">
        <Sidebar />

        <div className="flex-1 flex flex-col overflow-auto" ref={scrollRef}>
          <div className="flex gap-4 items-center justify-between sticky top-0 z-9 bg-main p-4">
            <span className="text-sm text-muted-foreground">作品总数: {data?.pages[0]?.total ?? 0}</span>
            <Button size="sm" variant="outline" className="ml-auto">
              <ArrowDownAz className="size-3.5 mr-1" />
              排序
            </Button>
            <div className="flex items-center gap-2">
              <CheckboxHeader />
              <span className="text-sm">已选择: {Object.keys(selected).length}个</span>
            </div>
            <Button variant="ghost" size="sm" className="-ml-2" onClick={() => refetch()}>
              <RefreshCw className="size-3.5 mr-1 rotate-x-180 text-primary" />
              <span className="text-muted-foreground">刷新</span>
            </Button>
          </div>

          {works.length > 0 ? (
            <>
              <div className="px-4 pb-4 grid gap-4 grid-cols-[repeat(auto-fill,minmax(12rem,1fr))]">
                {works.map(work => (
                  <WorkCard key={work.id} work={work} />
                ))}
              </div>
              <NextPageFetcher />
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
              <div className="text-lg mb-2">暂无作品</div>
              <div className="text-sm">请调整筛选条件或创建新的作品</div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
