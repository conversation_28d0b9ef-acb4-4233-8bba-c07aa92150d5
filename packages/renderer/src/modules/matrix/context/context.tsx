import  { createContext, useContext } from 'react'
import { PlatformKey } from '@/libs/request/api/generic-matrix'
import { PLATFORM_CONFIG } from '../constants/shared'

export type PlatformConfig = typeof PLATFORM_CONFIG[PlatformKey]

interface PlatformContextType {
  currentPlatform: PlatformKey
  platformConfig: PlatformConfig
  setPlatform: (platform: PlatformKey) => void
}

export const PlatformContext = createContext<PlatformContextType | undefined>(undefined)

export const usePlatform = (): PlatformContextType => {
  const context = useContext(PlatformContext)
  if (context === undefined) {
    throw new Error('usePlatform must be used within a PlatformProvider')
  }
  return context
}

export const usePlatformSwitch = () => {
  const { setPlatform } = usePlatform()

  const switchPlatform = async (platform: PlatformKey, onSwitch?: (platform: PlatformKey) => Promise<void>) => {
    try {
      setPlatform(platform)
      if (onSwitch) {
        await onSwitch(platform)
      }
    } catch (error) {
      console.error('Platform switch error:', error)
    } 
  }

  return { switchPlatform }
}