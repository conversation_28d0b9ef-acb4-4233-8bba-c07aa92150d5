import { QUERY_KEYS } from '@/constants/queryKeys'
import { Member, TeamAPI } from '@/libs/request/api/team'
import { useQuery } from '@tanstack/react-query'
import useInfiniteQuery, { PaginationQueryParams } from '../useInfiniteQuery'
import { usePagination } from '../usePagination'
import { PaginationParams } from '@app/shared/types/database.types'

export const useQueryCurrentTeam = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_CURRENT],
    queryFn: () => TeamAPI.current({}),
  })
}

export const useQueryTeamMemberList = (keyword?: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_MEMBER_LIST, keyword],
    queryFn: () => TeamAPI.members({ keyword }),
  })
}

export const useQueryTeamMemberByID = (memberId?: number | null) => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_MEMBER, memberId],
    queryFn: () => TeamAPI.member({ memberId: memberId! }),
    enabled: !!memberId,
  })
}

export const usePaginationTeamMemberList = (params: PaginationParams = {}) => {
  return usePagination<Member, PaginationQueryParams>({
    queryKey: [],
    queryFn: TeamAPI.membersPage,
    searchParams: params,
    initialPageSize: 20,
    enabled: true,
  })
}

export const useInfiniteQueryTeamMembers = (params: PaginationParams = {}) => {
  return useInfiniteQuery([QUERY_KEYS.TEAM_MEMBER_LIST], TeamAPI.membersPage, params, {
    pageSize: params.pageSize || 20,
  })
}

export const useQueryTeamRoles = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_ROLES],
    queryFn: () => TeamAPI.roles({}),
  })
}

export const useQueryTeamList = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_LIST],
    queryFn: () => TeamAPI.list({}),
  })
}
