import React, { useState } from 'react'
import { FontResource } from '@/types/resources'
import { useDebounceValue } from '@/hooks/useDebounceValue'
import { useInfiniteQueryFontUnified } from '@/hooks/queries/useQueryFont'
import { Input } from '@/components/ui/input'
import { throttle } from 'lodash'
import { SelectableInfinitePanel } from './selectable-infinite-panel'
import { cn } from '@/components/lib/utils'

const FontPreviewItem: React.FC<{ font: FontResource.Font; className?: string }> = ({
  font, className = ''
}) => {
  if (!font.cover?.url) return null

  return (
    <div
      className={cn(
        `flex items-center justify-center py-1.5 border border-gray-400
        rounded-md transition-colors`,
        className
      )}
    >
      {/* 字体预览图 */}
      <img
        src={font.cover.url}
        alt={`${font.title} 预览`}
        className="h-7 aspect-auto object-cover"
      />
    </div>
  )
}

export const TextFontFamilySelector: React.FC<{ onChange?: (val: FontResource.Font[]) => void }> = ({ onChange }) => {
  const [keywordInput, setKeywordInput] = useState('')

  const keyword = useDebounceValue(keywordInput, 500)

  const fontInfiniteQueryResult = useInfiniteQueryFontUnified({
    keyword,
    pageNo: 1,
    pageSize: 99,
    autoLoadAllPages: true
  })

  return (
    <div>
      <Input
        value={keywordInput}
        onChange={throttle(e => setKeywordInput(e.target.value), 500)}
        placeholder="搜索字体"
      />

      <SelectableInfinitePanel
        query={fontInfiniteQueryResult}
        itemName="字体"
        onChange={onChange}
        renderItem={item => (
          <FontPreviewItem
            font={item}
            className="border-none"
          />
        )}
      />
    </div>
  )
}
