import React from 'react'
import { useQueryCurrentTeam, useQueryTeamMemberByID } from '@/hooks/queries/useQueryTeam'
import { TeamAPI } from '@/libs/request/api/team'
import { Button, type ButtonProps } from '@/components/ui/button'
import { TeamManager, TokenManager } from '@/libs/storage'
import { toast } from 'react-toastify'
import { useDeleteModal } from '@/components/modal/delete'
import { useNavigate } from 'react-router'
import { useQueryClient } from '@tanstack/react-query'
import { useSelectMember } from '@/pages/Team/TeamSetting/own-select-member'
import { useDisbandTeam } from '@/pages/Team/TeamSetting/disband-modal'
import { ROLES, TEAM_ACTIONS } from '@/types/team'
import { useRenameItemModal } from '@/components/material/modal'

interface TeamActionOptions {
  kind: string
  name: string
  actionName: string
  danger?: boolean
  apiCall: () => Promise<any>
  successMessage?: string
  errorMessage: string
}

export default function TeamSettingPage() {
  const { data: team } = useQueryCurrentTeam()
  const { data: member } = useQueryTeamMemberByID(TokenManager.getUserId())
  const currentTeamId = TeamManager.current()!
  const queryClient = useQueryClient()
  const deleteModal = useDeleteModal()
  const openSelectMember = useSelectMember()
  const openDisbandModal = useDisbandTeam()
  const navigate = useNavigate()
  const isOwner = member?.roles?.some(role => role.code === ROLES.OWNER)
  const renameItem = useRenameItemModal<TEAM_ACTIONS>(async (_type, _id, _title, data) => {
    console.log('输入的团队名称', data)
    // TODO: 接口未提供
    // await TeamAPI.update({ teamId: currentTeamId, name: data.title })
    // toast('修改成功', { type: 'success' })
  })

  function handleTeamAction({
    kind,
    name,
    actionName,
    danger = false,
    apiCall,
    successMessage,
    errorMessage,
  }: TeamActionOptions) {
    return deleteModal({
      kind,
      name,
      danger,
      actionName,
      action: async () => {
        try {
          await apiCall()
          TeamManager.clear()
          await queryClient.resetQueries()
          navigate('/login/team')
          if (successMessage) toast.success(successMessage)
        } catch (error: any) {
          toast.error(error?.message || errorMessage)
        }
      },
    })
  }

  const allActions: {
    title: string
    value: TEAM_ACTIONS
    description: string
    descriptionClass: string
    buttonText: string
    buttonVariant: ButtonProps['variant']
    onClick: () => void
  }[] = [
    {
      title: `团队名称：${team?.name || ''}`,
      value: TEAM_ACTIONS.UPDATE_NAME,
      description: '',
      descriptionClass: 'text-gray-500',
      buttonText: '修改名称',
      buttonVariant: 'outline',
      onClick: () => {
        renameItem(TEAM_ACTIONS.UPDATE_NAME, currentTeamId, team?.name || '当前团队', {
          label: '',
          headerTitle: '团队名称',
        })
      },
    },
    {
      title: '修改团队头像',
      value: TEAM_ACTIONS.UPDATE_AVATAR,
      description: '上传新的团队头像',
      descriptionClass: 'text-gray-500',
      buttonText: '修改头像',
      buttonVariant: 'outline',
      onClick: () => {
        console.log('修改团队头像')
        // TODO：接口尚未提供
        // const input = document.createElement('input')
        // input.type = 'file'
        // input.accept = 'image/*'
        // input.onchange = async (e: any) => {
        //   const file = e.target.files[0]
        //   if (!file) return
        //   const formData = new FormData()
        //   formData.append('avatar', file)
        //   handleTeamAction({
        //     kind: '团队',
        //     name: team?.name || '当前团队',
        //     actionName: '修改头像',
        //     apiCall: () => TeamAPI.update({ teamId: currentTeamId, avatar: '' }),
        //     successMessage: '团队头像修改成功',
        //     errorMessage: '修改团队头像失败',
        //   })
        // }
        // input.click()
      },
    },
    {
      title: '移交团队',
      value: TEAM_ACTIONS.TRANSFER,
      description: '移交团队后您将成为管理员',
      descriptionClass: 'text-gray-500',
      buttonText: '移交团队',
      buttonVariant: 'outline',
      onClick: () => {
        openSelectMember({
          title: '移交团队',
          description: '每个团队只有一位所有者，移交后您将变成管理员哦',
          onConfirm: memberId => {
            if (!memberId) return toast.warning('请选择要转让的成员')
            console.log('选中的成员ID:', memberId)
            toast.success(`你选择了成员 ${memberId}`)
            // TODO: 接口还没有添加成员id的入参
            // TeamAPI.transfer({ teamId: currentTeamId, userId: memberId })
          },
        })
      },
    },
    {
      title: '退出团队',
      value: TEAM_ACTIONS.LEAVE,
      description: '退出团队后您将看不到该团队',
      descriptionClass: 'text-gray-500',
      buttonText: '退出团队',
      buttonVariant: 'outline',
      onClick: () => {
        if (!isOwner) {
          handleTeamAction({
            kind: '团队',
            name: team?.name || '当前团队',
            danger: true,
            actionName: '退出',
            apiCall: () => TeamAPI.leave({ teamId: currentTeamId }),
            errorMessage: '退出团队失败',
          })
        } else {
          openSelectMember({
            title: '退出团队',
            description: '退出团队后你将看不到该团队，请选择一位成员接收团队',
            onConfirm: memberId => {
              if (!memberId) return toast.warning('请选择要转让的成员')
              console.log('选中的成员ID:', memberId)
              toast.success(`你选择了成员 ${memberId}`)
              // TODO: 接口还没有添加成员id的入参
              // TeamAPI.leave({ teamId: currentTeamId, userId: memberId })
            },
          })
        }
      },
    },
    {
      title: '解散团队',
      value: TEAM_ACTIONS.DISSOLVE,
      description: '解散后，团队内所有内容都会彻底删除不可恢复，请谨慎操作',
      descriptionClass: 'text-red-500',
      buttonText: '解散团队',
      buttonVariant: 'destructive',
      onClick: () => {
        openDisbandModal({
          onConfirm: () => {
            handleTeamAction({
              kind: '团队',
              name: team?.name || '当前团队',
              danger: true,
              actionName: '解散',
              apiCall: () => TeamAPI.disband({ teamId: currentTeamId }),
              errorMessage: '解散团队失败',
            })
          },
        })
      },
    },
  ]

  const SettingActions = isOwner
    ? allActions
    : allActions.filter(action => action.value !== TEAM_ACTIONS.DISSOLVE && action.value !== TEAM_ACTIONS.TRANSFER)

  return (
    <div className="flex flex-col items-stretch h-full px-4 w-full">
      <div className="p-4">
        <div className="text-lg font-bold mb-5">团队设置</div>

        <div className="flex flex-col gap-3">
          {SettingActions.map((action, idx) => (
            <div key={idx} className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <div className="font-bold">{action.title}</div>
                <div className={`text-sm ${action.descriptionClass}`}>{action.description}</div>
              </div>
              <Button variant={action.buttonVariant} onClick={action.onClick}>
                {action.buttonText}
              </Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
