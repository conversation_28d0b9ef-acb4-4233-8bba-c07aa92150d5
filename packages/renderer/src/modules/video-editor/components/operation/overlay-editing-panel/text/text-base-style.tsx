import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { Toggle } from '@/components/ui/toggle'
import { AlignCenter, AlignLeft, AlignRight, Bold, Italic, Underline } from 'lucide-react'
import { FormSlider } from '@/modules/video-editor/components/common/form-components'

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { FontResource } from '@/types/resources'
import { FontPreviewItem } from './font-preview-item'
import { useInfiniteQueryFontUnified } from '@/hooks/queries/useQueryFont'
import InfiniteResourceList from '@/components/InfiniteResourceList'
import { SettingsTabs, TabItem } from '../../../shared/settings-tabs'
import { cacheManager } from '@/libs/cache/cache-manager'
import { ColorPicker } from '@/components/color-picker'
import { useTextSettingContext } from './context'
import { toast } from 'react-toastify'
import { Switch } from '@/components/ui/switch'

const FontSelector: React.FC = () => {
  const { textOverlay, requestUpdateText } = useTextSettingContext()

  const [isSelectOpen, setIsSelectOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('list')

  const selectedFontFamily = useMemo(() => textOverlay.styles.fontFamily, [textOverlay.styles.fontFamily])

  const currentFont = useMemo(() => cacheManager.font.getFontSync(textOverlay.src), [textOverlay])

  const fontInfiniteQueryResult = useInfiniteQueryFontUnified({
    pageNo: 1,
    pageSize: 20,
    selectedCategory: selectedCategory === 'list' ? undefined : selectedCategory
  })

  const handleFontChange = useCallback(async (newFont: FontResource.Font) => {
    if (!newFont) return

    const newFontSrc = newFont.content.url

    try {
      return requestUpdateText({
        src: newFontSrc,
        styles: { fontFamily: newFont.title },
      }, true)
    } catch (error: any) {
      toast(`无法切换到[${newFont.title}]字体: ${error.message}`)
    } finally {
      setIsSelectOpen(false)
    }
  }, [requestUpdateText])

  const renderFontItem = useCallback((font: FontResource.Font) => (
    <FontPreviewItem
      key={font.id}
      font={font}
      isSelected={font.title === selectedFontFamily}
      onFontChange={handleFontChange}
    />
  ), [selectedFontFamily, handleFontChange])

  const tabs: TabItem[] = useMemo(
    () => [
      {
        value: 'list',
        label: '字体列表',
        content: (
          <div className="h-[300px] overflow-y-auto">
            <div className="p-1 space-y-1">
              <InfiniteResourceList
                queryResult={fontInfiniteQueryResult}
                renderItem={renderFontItem}
                emptyText="暂无字体"
                loadingText="字体加载中..."
              />
            </div>
          </div>
        )
      },
      {
        value: 'collected',
        label: '收藏字体',
        content: (
          <div className="h-[300px] overflow-y-auto">
            <div className="p-1 space-y-1">
              <InfiniteResourceList
                queryResult={fontInfiniteQueryResult}
                renderItem={renderFontItem}
                emptyText="暂无收藏字体"
                loadingText="收藏字体加载中..."
              />
            </div>
          </div>
        )
      }
    ],
    [fontInfiniteQueryResult, renderFontItem]
  )

  return (
    <Popover open={isSelectOpen} onOpenChange={setIsSelectOpen}>
      <PopoverTrigger asChild>
        <div
          className="flex h-10 w-full rounded-md border border-input px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer"
        >
          <div className="flex items-center justify-between w-full">
            <span style={currentFont ? { fontFamily: textOverlay.styles.fontFamily } : {}}>
              {textOverlay.styles.fontFamily || '选择字体'}
            </span>
            <span className="text-xs text-muted-foreground">
              {currentFont ? '' : '加载中...'}
            </span>
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-2" align="start">
        <SettingsTabs
          tabs={tabs}
          defaultTab="list"
          onTabChange={value => setSelectedCategory(value)}
        />
      </PopoverContent>
    </Popover>
  )
}

const TypographySetting: React.FC = () => {
  const { textOverlay, requestUpdateText } = useTextSettingContext()

  return (
    <div className="overlay-setting-card">
      <h3 className="text-sm font-medium">排版</h3>

      <div className="flex flex-col gap-2">
        <label className="text-xs text-muted-foreground">字体</label>
        <FontSelector />
      </div>

      <div className="grid grid-cols-2">
        {/* 对齐 */}
        <div className="flex flex-col gap-2">
          <label className="text-xs text-muted-foreground">对齐</label>
          <ToggleGroup
            type="single"
            className="justify-start gap-1"
            value={textOverlay.styles.textAlign}
            onValueChange={value => {
              if (value) requestUpdateText({ styles: { textAlign: value as any } }, true)
            }}
          >
            <ToggleGroupItem
              value="left"
              aria-label="Align left"
              className="h-10 w-10"
            >
              <AlignLeft className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem
              value="center"
              aria-label="Align center"
              className="h-10 w-10"
            >
              <AlignCenter className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem
              value="right"
              aria-label="Align right"
              className="h-10 w-10"
            >
              <AlignRight className="h-4 w-4" />
            </ToggleGroupItem>
          </ToggleGroup>
        </div>

        {/* 文字样式 */}
        <div className="flex flex-col gap-2">
          <label className="text-xs text-muted-foreground">文字样式</label>
          <div className="flex gap-1">
            <Toggle
              pressed={textOverlay.styles.fontWeight === 'bold'}
              aria-label="Bold"
              className="h-10 w-10"
              onPressedChange={pressed => {
                return requestUpdateText({ styles: { fontWeight: pressed ? 'bold' : 'normal' } }, true)
              }}
            >
              <Bold className="h-4 w-4" />
            </Toggle>
            <Toggle
              pressed={textOverlay.styles.fontStyle === 'italic'}
              aria-label="Italic"
              className="h-10 w-10"
              onPressedChange={pressed => {
                return requestUpdateText({ styles: { fontStyle: pressed ? 'italic' : 'normal' } }, true)
              }}
            >
              <Italic className="h-4 w-4" />
            </Toggle>
            <Toggle
              pressed={textOverlay.styles.underlineEnabled}
              aria-label="Underline"
              className="h-10 w-10"
              onPressedChange={pressed => {
                return requestUpdateText({ styles: { underlineEnabled: pressed } }, true)
              }}
            >
              <Underline className="h-4 w-4" />
            </Toggle>
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-2">
        <label className="text-xs text-muted-foreground">字号 (px)</label>
        <FormSlider
          showInput
          value={textOverlay.styles.fontSize || 150}
          min={50}
          max={500}
          step={10}
          onChange={(fontSize, commit) => {
            return requestUpdateText({ styles: { fontSize } }, commit)
          }}
        />
      </div>

      {/* Letter Spacing */}
      <div className="flex flex-col gap-2">
        <label className="text-xs text-muted-foreground">字间距 (px)</label>
        <FormSlider
          value={textOverlay.styles.letterSpacing || 0}
          min={-5}
          max={100}
          step={1}
          showInput
          onChange={(letterSpacing, commit) => {
            return requestUpdateText({ styles: { letterSpacing } }, commit)
          }}
        />
      </div>

      {/* Line Height */}
      <div className="flex flex-col gap-2">
        <label className="text-xs text-muted-foreground">行间距 (倍数)</label>
        <FormSlider
          value={textOverlay.styles.lineSpacing || 0}
          min={0}
          max={2}
          step={0.05}
          showInput
          onChange={(lineSpacing, commit) => {
            return requestUpdateText({ styles: { lineSpacing } }, commit)
          }}
        />
      </div>
    </div>
  )
}

const StrokeSetting: React.FC = () => {
  const { textOverlay, requestUpdateText } = useTextSettingContext()

  return (
    <div className="overlay-setting-card">
      <h3 className="text-sm font-medium">描边</h3>

      {/* 描边开关 */}
      <div className="flex items-center justify-between">
        <label className="text-xs text-muted-foreground">启用描边</label>

        <Switch
          checked={textOverlay.styles.strokeEnabled || false}
          aria-label="Enable Stroke"
          onCheckedChange={strokeEnabled => {
            return requestUpdateText({ styles: { strokeEnabled } }, true)
          }}
        />
      </div>

      {textOverlay.styles.strokeEnabled && (
        <>
          {/* 描边粗细 */}
          <div className="flex flex-col gap-2">
            <label className="text-xs text-muted-foreground">描边粗细</label>

            <FormSlider
              value={textOverlay.styles.strokeWidth || 1}
              min={1}
              max={80}
              step={1}
              showInput={false}
              onChange={(value, commit) => {
                return requestUpdateText({ styles: { strokeWidth: value } }, commit)
              }}
            />
          </div>

          {/* 描边颜色 */}
          <div className="flex flex-col gap-2">
            <label className="text-xs text-muted-foreground">描边颜色</label>
            <ColorPicker
              color={textOverlay.styles.strokeColor || '#000000'}
              onChange={({ hex }) => requestUpdateText({ styles: { strokeColor: hex } })}
              onChangeComplete={({ hex }) => requestUpdateText({ styles: { strokeColor: hex } }, true)}
            />
          </div>
        </>
      )}
    </div>
  )
}

const ShadowSetting: React.FC = () => {
  const { textOverlay, requestUpdateText } = useTextSettingContext()

  return (
    <div className="overlay-setting-card">
      <h3 className="text-sm font-medium">阴影</h3>

      {/* 阴影开关 */}
      <div className="flex items-center justify-between">
        <label className="text-xs text-muted-foreground">启用阴影</label>

        <Switch
          checked={textOverlay.styles.shadowEnabled || false}
          onCheckedChange={pressed => requestUpdateText({ styles: { shadowEnabled: pressed, shadowOpacity: 0.5 } }, true)}
          aria-label="Enable Shadow"
        />
      </div>

      {textOverlay.styles.shadowEnabled && (
        <>
          {/* 阴影距离和角度 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col gap-2">
              <label className="text-xs text-muted-foreground">阴影距离</label>
              <FormSlider
                value={textOverlay.styles.shadowDistance ?? 5}
                onChange={(value, commit) => requestUpdateText({ styles: { shadowDistance: value } }, commit)}
                min={0}
                max={50}
                step={1}
                showInput={false}
              />
            </div>
            <div className="flex flex-col gap-2">
              <label className="text-xs text-muted-foreground">阴影角度</label>
              <FormSlider
                value={textOverlay.styles.shadowAngle ?? 45}
                onChange={(value, commit) => requestUpdateText({ styles: { shadowAngle: value } }, commit)}
                min={0}
                max={360}
                step={1}
                showInput={false}
              />
            </div>
          </div>

          {/* 阴影模糊度和透明度 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col gap-2">
              <label className="text-xs text-muted-foreground">模糊度</label>
              <FormSlider
                value={textOverlay.styles.shadowBlur ?? 1}
                onChange={(value, commit) => requestUpdateText({ styles: { shadowBlur: value } }, commit)}
                min={0}
                max={50}
                step={1}
                showInput={false}
              />
            </div>
            <div className="flex flex-col gap-2">
              <label className="text-xs text-muted-foreground">透明度</label>
              <FormSlider
                value={textOverlay.styles.shadowOpacity ?? 1}
                onChange={(value, commit) => requestUpdateText({ styles: { shadowOpacity: value } }, commit)}
                min={0}
                max={1}
                step={0.1}
                showInput={false}
              />
            </div>
          </div>

          {/* 阴影颜色 */}
          <div className="flex flex-col gap-2">
            <label className="text-xs text-muted-foreground">阴影颜色</label>
            <ColorPicker
              color={textOverlay.styles.shadowColor || '#000000'}
              onChange={({ hex }) => requestUpdateText({ styles: { shadowColor: hex } })}
              onChangeComplete={({ hex }) => requestUpdateText({ styles: { shadowColor: hex } }, true)}
            />
          </div>
        </>
      )}
    </div>
  )
}

const ColorSetting: React.FC = () => {
  const { textOverlay, requestUpdateText } = useTextSettingContext()

  return (
    <div className="overlay-setting-card">
      <h3 className="text-sm font-medium">颜色</h3>

      <div className="grid grid-cols-3 gap-4">
        <div className="flex flex-col gap-2">
          <label className="text-xs text-muted-foreground">
            文字颜色
          </label>
          <ColorPicker
            color={textOverlay.styles.color || '#000000'}
            onChange={({ hex }) => requestUpdateText({ styles: { color: hex } })}
            onChangeComplete={({ hex }) => requestUpdateText({ styles: { color: hex } }, true)}
          />
        </div>

        {/* 文字透明度 */}
        <div className="flex flex-col gap-2">
          <label className="text-xs text-muted-foreground">文字透明度</label>
          <FormSlider
            value={textOverlay.styles.textOpacity || 1}
            min={0}
            max={1}
            step={0.1}
            showInput={false}
            onChange={(value, commit) => requestUpdateText({ styles: { textOpacity: value } }, commit)}
          />
        </div>

        <div className="flex flex-col gap-2">
          <label className="text-xs text-muted-foreground">
            背景色
          </label>
          <ColorPicker
            color={textOverlay.styles.backgroundColor || '#000000'}
            onChange={({ hex }) => requestUpdateText({ styles: { backgroundColor: hex } })}
            onChangeComplete={({ hex }) => requestUpdateText({ styles: { backgroundColor: hex } }, true)}
          />
        </div>
      </div>
    </div>
  )
}

export const TextBaseStyle: React.FC = () => {
  const { textOverlay, requestUpdateText } = useTextSettingContext()

  const [input, setInput] = useState(textOverlay.content || '')

  useEffect(() => {
    setInput(textOverlay.content || '')
  }, [textOverlay.content])

  return (
    <div className="space-y-6">
      <div className="relative w-full overflow-hidden rounded-sm border border-border bg-container/40">
        <textarea
          value={input}
          onChange={e => {
            requestUpdateText({ content: e.target.value })
            setInput(e.target.value)
          }}
          onBlur={() => requestUpdateText({}, true)}
          placeholder="Enter your text here..."
          className="w-full min-h-[60px] bg-transparent p-2 text-foreground placeholder:text-muted-foreground outline-none focus:outline-none"
          spellCheck="false"
        />
      </div>

      <TypographySetting />
      <StrokeSetting />
      <ShadowSetting />
      <ColorSetting />
    </div>
  )
}
