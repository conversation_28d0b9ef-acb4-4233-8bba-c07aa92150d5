import { MixcutRulesFormCategories } from '@/modules/mixcut/context/useMixcutRulesForm'
import { MixcutRuleSwitch } from '@/modules/mixcut/components/mixcut-rule-switch'
import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut'
import { Label } from '@/components/ui/label'
import { cn } from '@/components/lib/utils'
import { NumberInput, RangeInput } from '@/components/ui/number-input'
import { LabeledCheckbox } from '@/components/ui/checkbox'
import React from 'react'

export const VideoTransform = () => {
  const CATEGORY = MixcutRulesFormCategories.videoTransform

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <MixcutRuleSwitch
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.video.rotation}
          label="视频旋转"
        >
          {({ getConfigValue, setConfigValue }) => (
            <>
              <div className="flex items-center gap-3">
                <Label className={cn('text-sm font-normal cursor-pointer')}>
                  最大旋转角度
                </Label>
                <NumberInput value={getConfigValue('range')} onChange={val => setConfigValue('range', val)} />
              </div>
            </>
          )}
        </MixcutRuleSwitch>

        <MixcutRuleSwitch
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.video.scale}
          label="视频缩放"
        >
          {({ getConfigValue, setConfigValue }) => (
            <>
              <div className="flex items-center gap-3">
                <Label className={cn('text-sm font-normal cursor-pointer')}>
                  放大倍数上限
                </Label>
                <NumberInput value={getConfigValue('range')} onChange={val => setConfigValue('range', val)} />
              </div>
            </>
          )}
        </MixcutRuleSwitch>

        <MixcutRuleSwitch
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.video.positionOffset}
          label="视频位置偏移"
        >
          {({ getConfigValue, setConfigValue }) => (
            <div className="flex items-center gap-3">
              <div className={cn('text-sm font-normal cursor-pointer')}>
                偏移上限(%)
              </div>
              <NumberInput value={getConfigValue('range')} onChange={val => setConfigValue('range', val)} />
            </div>
          )}
        </MixcutRuleSwitch>

        <MixcutRuleSwitch
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.video.trim}
          label="视频去片头/片尾"
        >
          {({ category, pipeline, getConfigValue, setConfigValue }) => (
            <div className="flex flex-col gap-3">
              <LabeledCheckbox
                id={`${category}-${pipeline}-allowTrimStart`}
                label="允许去片头"
                checked={getConfigValue('allowTrimStart')}
                onChange={checked => setConfigValue('allowTrimStart', checked)}
              />

              <RangeInput
                label="去片头/片尾帧数范围"
                minValue={getConfigValue('rangeMin')}
                maxValue={getConfigValue('rangeMax')}
                onMinChange={val => setConfigValue('rangeMin', val)}
                onMaxChange={val => setConfigValue('rangeMax', val)}
              />
            </div>
          )}
        </MixcutRuleSwitch>
      </div>
    </div>
  )
}
