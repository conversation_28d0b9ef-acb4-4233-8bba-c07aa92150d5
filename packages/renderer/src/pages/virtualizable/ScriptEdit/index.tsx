import React, { use<PERSON><PERSON>back, useEffect, useId, useMemo, useRef, useState } from 'react'
import {
  Al<PERSON><PERSON>riangle,
  ArrowUpFromDot,
  Bot,
  BrushCleaning,
  Check,
  ChevronRight,
  ChevronsUpDown,
  CircleX,
  Copy,
  Edit2,
  Edit3,
  History,
  Loader2,
  Plus,
  RefreshCcw,
  Save,
  Share2,
  ShieldAlert,
  ShieldCheck,
  Trash,
  Trash2,
  Upload,
  Video,
  X,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectValue } from '@/components/ui/select'
import { SelectTrigger } from '@radix-ui/react-select'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import Editor from 'react-simple-code-editor'
import './script.css'
import { cn } from '@/components/lib/utils'
import { uploadB<PERSON>erViaIPC } from '@/libs/request/upload'
import { ScriptSceneData, useQueryScript } from '@/hooks/queries/useQueryScript'
import { PageLoading } from '@/components/LoadingIndicator'
import { ResourceModule } from '@/libs/request/api/resource'
import { debounce, isEqual } from 'lodash'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore'
import { ComplianceAPI, RiskKeyword } from '@/libs/request/api/secure'
import { toast } from 'react-toastify'
import { useVirtualTab } from '@/contexts'
import { OSSModules } from '@app/shared/types/ipc/file-uploader'
import { Streamdown } from 'streamdown'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import getCaretCoordinates from 'textarea-caret'
import { shotTypes } from '@/types/project'
import { Separator } from '@/components/ui/separator'
import rehypeRaw from 'rehype-raw'
import { useScriptChat } from '@/hooks/useScriptChat'
import {
  useInfiniteQueryScriptChatSessionList,
  useQueryScriptChatCategoryList,
  useQueryScriptChatFormConfig,
  useQueryScriptChatModelList,
} from '@/hooks/queries/useQueryScriptChat'
import { ChatModel, ScriptChatAPI } from '@/libs/request/api/script-chat'
import { useModalUpdateScriptSession } from './modal'
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver'
import { Sheet, SheetClose, SheetContent, SheetFooter, SheetHeader, SheetTitle } from '@/components/ui/sheet'
import { DynamicComponent } from '@/libs/tools/dynamic-react'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import rehypeSanitize, { defaultSchema } from 'rehype-sanitize'
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable'
import { useModalConfirm } from '@/components/modal/confirm'

const schema = {
  ...defaultSchema,
  tagNames: [...(defaultSchema.tagNames || []), 'think'],
}

function useAutoSave(callback: () => void | Promise<void>, deps: unknown[] = []) {
  const [isDirty, setIsDirty] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const callbackRef = useRef(callback)

  useEffect(() => {
    callbackRef.current = callback
  }, [callback])

  const debounced = useMemo(
    () =>
      debounce(async () => {
        setIsSaving(true)
        try {
          await callbackRef.current()
          setLastSaved(new Date())
          setIsDirty(false)
        } catch (error) {
          console.error('Error saving:', error)
        } finally {
          setIsSaving(false)
        }
      }, 1000),
    [],
  )

  useEffect(() => {
    setIsDirty(true)
    debounced()
  }, [...deps, debounced])

  return { isDirty, isSaving, lastSaved, saveNow: debounced.flush }
}

const promptCodes = ['仿写', '扩写', '缩写'] as const

function PromptRow({
  promptCode,
  content,
  onClick,
}: {
  promptCode: (typeof promptCodes)[number]
  content: string
  onClick?: (value: string) => void
}) {
  const [pending, setPending] = useState(true)
  const [error, setError] = useState('')
  const [message, setMessage] = useState('')

  useEffect(() => {
    ;(async () => {
      try {
        await ScriptChatAPI.prompt({ promptCode, content }, { onMessage: v => setMessage(msg => msg + v.data.content) })
      } catch (e: any) {
        setError(e.message || '请求出错')
      }
      setPending(false)
    })()
  }, [promptCode, content])

  return (
    <div
      className={cn('flex items-center px-2 py-1 h-6', !pending && !error && 'cursor-pointer')}
      onMouseDown={e => e.preventDefault()}
      onClick={() => {
        if (!pending && !error) onClick?.(message)
      }}
    >
      {error ? (
        <span className="text-red-500">
          <AlertTriangle className="mr-2 size-4" />
        </span>
      ) : pending ? (
        <Loader2 className="mr-2 size-4 animate-spin text-muted-foreground" />
      ) : (
        <Check className="mr-2 size-4 text-green-700" />
      )}
      {error ? <span className="text-red-500">{error}</span> : <span>{message}</span>}
    </div>
  )
}

function ScriptEditor({ value, onChange }: { value: string; onChange: (value: string) => void }) {
  const id = useId()
  const [keywords, setKeywords] = useState<RiskKeyword[]>([])
  const { isDirty } = useAutoSave(async () => {
    const keywords = await ComplianceAPI.checkRiskKeywords({ text: value })
    setKeywords(keywords)
  }, [value])
  const [pos, setPos] = useState<[number, number, number] | null>(null)
  const [selection, setSelection] = useState({ start: 0, end: 0 })
  const [action, setAction] = useState<{ code: (typeof promptCodes)[number]; count: number } | null>(null)

  useEffect(() => {
    const textarea = document.getElementById(id) as HTMLTextAreaElement
    if (!textarea) return

    const handleSelect = () => {
      const { selectionStart, selectionEnd } = textarea
      if (selectionStart === selection.start && selectionEnd === selection.end) return
      const { left, top, height } = getCaretCoordinates(textarea, selectionStart)
      setSelection({ start: selectionStart, end: selectionEnd })
      setPos([left, top, height])
    }

    const handleSelectionChange = () => {
      if (document.activeElement !== textarea) return setPos(null)
      if (textarea.selectionStart === textarea.selectionEnd) setPos(null)
    }

    textarea.addEventListener('select', handleSelect)
    document.addEventListener('selectionchange', handleSelectionChange)
    return () => {
      textarea.removeEventListener('select', handleSelect)
      document.removeEventListener('selectionchange', handleSelectionChange)
    }
  }, [id, selection])

  useEffect(() => {
    setAction(null)
  }, [pos])

  return (
    <>
      <Editor
        textareaId={id}
        className="h-full w-full editor"
        value={value}
        onValueChange={onChange}
        padding={10}
        highlight={text => {
          const escapeHtml = (s: string) =>
            // eslint-disable-next-line @stylistic/quotes
            s.replace(/[&<>"']/g, c => ({ '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;' })[c]!)

          const riskWords = keywords.map(k => k.word).filter(Boolean)
          let regex: RegExp | null = null
          if (riskWords.length) {
            const escapedWords = riskWords.map(w => w.replace(/[.*+?^${}()|[\]\\]/g, r => `\\${r}`))
            try {
              regex = new RegExp(escapedWords.join('|'), 'g')
            } catch (e) {
              regex = null
            }
          }

          return text
            .split('\n')
            .map((rawLine, i) => {
              if (!rawLine) {
                return `<span class="editorLineNumber">${i + 1}.</span><span class="editorPlaceholder">请输入或添加台词</span>`
              }
              let line = escapeHtml(rawLine)
              if (regex) {
                line = line.replace(regex, m => `<span class='risk-keyword'>${m}</span>`)
              }
              return `<span class="editorLineNumber">${i + 1}.</span>${line}`
            })
            .join('\n')
        }}
      />

      {pos && (
        <div className="absolute flex flex-col gap-2 -translate-y-10" style={{ left: pos[0], top: pos[1] }}>
          <div className="bg-gradient-brand px-2 rounded-full flex items-center [&>button]:p-2 [&>button]:cursor-pointer self-start">
            <button onClick={() => setAction({ code: '仿写', count: 5 })}>仿写</button>
            <Separator orientation="vertical" className="h-4" />
            <button onClick={() => setAction({ code: '扩写', count: 5 })}>扩写</button>
            <Separator orientation="vertical" className="h-4" />
            <button onClick={() => setAction({ code: '缩写', count: 5 })}>缩写</button>
          </div>
          {action && (
            <div className="flex flex-col bg-gradient-brand min-w-80 rounded-lg divide-y">
              {Array.from({ length: action.count }, (_, i) => (
                <PromptRow
                  key={i}
                  promptCode={action.code}
                  content={value.slice(selection.start, selection.end)}
                  onClick={msg => {
                    const newValue = value.slice(0, selection.start) + msg + value.slice(selection.end)
                    onChange(newValue)
                    setPos(null)
                  }}
                />
              ))}
            </div>
          )}
        </div>
      )}

      {isDirty && value && (
        <div className="absolute left-2 bottom-2 bg-blue-50 dark:bg-blue-900 rounded p-1 pl-5 flex gap-1 items-center">
          <Loader2 className="absolute left-1 size-3.5 text-blue-400 dark:text-blue-200 animate-spin" />
          <span className="text-xs font-medium leading-none text-blue-600 dark:text-blue-400">风险词检测中...</span>
        </div>
      )}

      {!isDirty && value && !keywords.length && (
        <div className="absolute left-2 bottom-2 bg-green-50 dark:bg-green-900 rounded p-1 pl-5 flex gap-1 items-center">
          <ShieldCheck className="absolute left-[3px] size-4 fill-green-400 text-green-100 dark:fill-green-400 dark:text-green-800" />
          <span className="text-xs font-medium leading-none text-green-600 dark:text-green-500">未检测到风险词</span>
        </div>
      )}

      {!isDirty && value && !!keywords.length && (
        <div className="absolute left-2 bottom-2 bg-red-50 dark:bg-red-900 rounded p-1 pl-5 flex gap-1 items-center">
          <ShieldAlert className="absolute left-[3px] size-4 fill-red-400 text-red-100 dark:fill-red-300 dark:text-red-800" />
          <span className="text-xs font-medium leading-none text-red-600 dark:text-red-200">检测到 1 个风险词</span>
        </div>
      )}
    </>
  )
}

const SmallUploadArea = ({
  src,
  onUpdate,
  className,
}: {
  src?: string
  onUpdate?: (url?: string) => unknown
  className?: string
}) => {
  const id = useId()

  const handleChange = async (file: File) => {
    const buffer = await file.arrayBuffer()
    const uuid = crypto.randomUUID().replace(/-/g, '')
    const suffix = file.type.split('/').at(-1)
    const filename = suffix ? `${uuid}.${suffix}` : uuid
    const result = await uploadBufferViaIPC(buffer, filename, uuid, undefined, OSSModules.script)
    if (!result.success) toast.error('图片上传失败：' + result.error)
    onUpdate?.(result.url)
  }

  return (
    <div
      className={cn(
        'group border border-dashed rounded text-center',
        'transition-colors cursor-pointer size-24 flex flex-col justify-center items-center relative',
        !src && 'hover:border-primary text-muted-foreground',
        className,
      )}
    >
      {src && <img src={src} alt="参考画面" className="size-full object-cover rounded z-0" />}
      <label
        htmlFor={id}
        className={cn(
          'absolute inset-0 flex flex-col items-center justify-center hover:opacity-100 z-10 transition-all',
          src ? 'opacity-0 bg-background/60' : 'opacity-50',
        )}
      >
        <Plus className="w-4 h-4 mx-auto mb-1" />
        <div className="text-xs">上传参考画面</div>
        {src && (
          <Button
            size="icon"
            variant="ghost"
            className="size-6"
            onClick={e => {
              e.stopPropagation()
              e.preventDefault()
              onUpdate?.()
            }}
          >
            <Trash className="size-4" />
          </Button>
        )}
      </label>
      <input
        id={id}
        className="hidden"
        type="file"
        accept="image/png,image/jpeg"
        onChange={async e => {
          const [file] = e.target.files ?? []
          if (!file) return
          await toast.promise(handleChange(file), {
            pending: '图片上传中...',
            success: '上传成功',
            error: '上传失败',
          })
          e.target.value = '' // 清空文件输入
        }}
      />
    </div>
  )
}

const ThinkContext = React.createContext<boolean | null>(null)

const Think = (props: any) => {
  const [open, setOpen] = useState(true)
  const think = React.useContext(ThinkContext)

  useEffect(() => {
    if (!think) setOpen(false)
  }, [think])

  return (
    <div className="mb-2">
      <button
        className="text-sm text-muted-foreground hover:underline underline-offset-2"
        onClick={() => setOpen(v => !v)}
      >
        {think ? '深度思考中...' : '已深度思考'}
      </button>
      {open && (
        <p {...props} className="pl-2 border-l-2 border-muted-foreground/50 text-sm text-muted-foreground py-1" />
      )}
    </div>
  )
}

export default function ScriptEditPage() {
  const { pushNamedTab } = useVirtualTabsStore()

  const {
    params: { id: scriptId },
  } = useVirtualTab('Script')
  const queryClient = useQueryClient()
  const { data, refetch } = useQueryScript(scriptId!)

  const [scenes, setScenes] = useState<ScriptSceneData[] | undefined>(undefined)
  const [openSelects, setOpenSelects] = useState<Record<string, boolean>>({})
  const [isEditingTitle, setIsEditingTitle] = useState(false)
  const [projectTitle, setProjectTitle] = useState('分镜脚本')

  const { isDirty, isSaving, lastSaved, saveNow } = useAutoSave(async () => {
    if (isEqual(scenes, data?.content)) return

    await ResourceModule.script.update({
      id: Number(scriptId!),
      content: JSON.stringify(scenes),
      fullContent: '[]',
    })
    await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SCRIPT_DETAIL, scriptId] })
  }, [scenes])

  useEffect(() => {
    if (!data?.scenes || scenes) return
    setScenes(data.scenes)
  }, [data, scenes])

  const updateScene = <K extends keyof ScriptSceneData>(
    id: ScriptSceneData['id'],
    field: K,
    value: ScriptSceneData[K],
  ) => {
    if (!scenes) return
    setScenes(scenes.map(scene => (scene.id === id ? { ...scene, [field]: value } : scene)))
  }

  const newScene = (): ScriptSceneData => {
    return { id: crypto.randomUUID() }
  }

  const addNewScene = () => {
    if (!scenes) return
    setScenes([...scenes, newScene()])
  }

  const handleSelectOpenChange = (sceneId: string, open: boolean) => {
    setOpenSelects(prev => ({ ...prev, [sceneId]: open }))
  }

  const handleTitleEdit = () => {
    setProjectTitle(data!.title)
    setIsEditingTitle(true)
  }

  const handleTitleBlur = async () => {
    setIsEditingTitle(false)
    if (!projectTitle.trim()) return toast.info('脚本标题不能为空')
    if (projectTitle.trim() === data?.title) return
    await ResourceModule.script.rename({ id: Number(scriptId!), title: projectTitle })
    await refetch()
    toast.success('脚本标题已更新')
    await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SCRIPT_LIST] })
  }

  const modalUpdateSession = useModalUpdateScriptSession()
  const modalConfirm = useModalConfirm()
  const [tab, setTab] = useState<'chat' | 'history'>('chat')
  const { data: modelList } = useQueryScriptChatModelList()
  const {
    data: sessionData,
    refetch: refetchSessionData,
    fetchNextPage,
  } = useInfiniteQueryScriptChatSessionList(Number(scriptId!))
  const sessionList = useMemo(() => sessionData?.pages.flatMap(page => page.list) || [], [sessionData])
  const [model, setModel] = useState<ChatModel>()
  const [inputMessage, setInputMessage] = useState('')
  const [sessionId, setSessionId] = useState<number>()
  const session = useMemo(() => sessionList.find(s => s.id === sessionId), [sessionList, sessionId])
  const { messages, sendMessage, genScript, pending } = useScriptChat(sessionId)
  const sessionListScrollRef = useRef<HTMLDivElement>(null)
  const NextPageFetcher = useIntersectionObserver({
    onIntersect: () => fetchNextPage(),
    deps: [sessionId],
    root: sessionListScrollRef.current,
  })
  const [sheetOpen, setSheetOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<{ id: number; name: string }>()
  const [selectedSubCategory, setSelectedSubCategory] = useState<{ id: number; name: string }>()
  const { data: categoryList } = useQueryScriptChatCategoryList(10000000011)
  const { data: subCategoryList } = useQueryScriptChatCategoryList(selectedCategory?.id)
  const { data: formConfig } = useQueryScriptChatFormConfig(selectedSubCategory?.id)
  const formRef = useRef<{ disabled?: () => boolean; submit?: () => boolean }>(null)

  useEffect(() => {
    if (!subCategoryList?.length) return
    setSelectedSubCategory(subCategoryList.find(c => c.id === selectedSubCategory?.id) || subCategoryList[0])
  }, [subCategoryList, selectedSubCategory])

  useEffect(() => {
    if (model || !modelList?.length) return
    setModel(modelList[0])
  }, [model, modelList])

  const handleMessageSend = useCallback(
    async (content?: string) => {
      content ??= inputMessage.trim()
      if (!model || !content) return

      const id = await (sessionId ?? ScriptChatAPI.create({ scriptId: Number(scriptId!) }).then(res => res.sessionId))
      setSessionId(id)
      sendMessage({ content, modelId: model.id, sessionId: id })
      setInputMessage('')
      await refetchSessionData()
    },
    [inputMessage, model, sendMessage, scriptId, sessionId],
  )

  const handleScriptGenerate = useCallback(
    async (promptCode: string, params: Record<string, any>) => {
      const id = await (sessionId ?? ScriptChatAPI.create({ scriptId: Number(scriptId!) }).then(res => res.sessionId))
      setSessionId(id)

      await genScript({ sessionId: id, promptCode, params: JSON.stringify(params) })
    },
    [genScript, sessionId],
  )

  // const env = useMemo(() => ({ API: { upload: console.log } }), [])

  return (
    <div className="w-full h-full flex">
      <ResizablePanelGroup direction="horizontal" autoSaveId="script-edit">
        <ResizablePanel
          defaultSize={70}
          minSize={50}
          className="p-4 flex h-full flex-col gap-4 overflow-hidden bg-main before:z-1 **:z-10 "
        >
          {/* Header */}
          <div className="flex items-center h-10 border-gray-200 gap-3 text-nowrap">
            {isEditingTitle ? (
              <>
                <Input
                  value={projectTitle}
                  onChange={e => setProjectTitle(e.target.value)}
                  onBlur={handleTitleBlur}
                  autoFocus
                  className="text-xl font-semibold border-0 shadow-none p-0 h-auto bg-transparent focus-visible:ring-0 outline-none"
                  style={{ boxShadow: 'none', outline: 'none' }}
                />
                <Button variant="ghost" size="sm" onClick={handleTitleEdit} className="p-1 h-auto hover:bg-gray-100">
                  <Check className="w-4 h-4 text-gray-500" />
                </Button>
              </>
            ) : (
              <>
                <span className="text-xl font-semibold">{data?.title}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleTitleEdit}
                  className="p-1 h-auto hover:bg-muted-foreground/15"
                >
                  <Edit2 className="w-4 h-4 text-muted-foreground" />
                </Button>
              </>
            )}
            {scenes && (
              <div className="text-sm text-muted-foreground flex items-center gap-1">
                {isSaving ? (
                  <>
                    <span>保存中</span>
                    <Loader2 className="size-4 animate-spin" />
                  </>
                ) : isDirty ? (
                  <>
                    <span>已编辑</span>
                    <Button variant="ghost" size="icon" onClick={saveNow} className="size-6 h-auto hover:bg-gray-100">
                      <Save className="size-4" />
                    </Button>
                  </>
                ) : (
                  `已保存 ${new Date(lastSaved!).toLocaleTimeString('zh-CN')}`
                )}
              </div>
            )}
            <Button
              variant="secondary"
              size="sm"
              className={cn('flex items-center gap-2', isEditingTitle || 'ml-auto')}
            >
              <Share2 className="w-4 h-4" />
              分享脚本
            </Button>
            <Button
              size="sm"
              className="flex items-center gap-2"
              onClick={() => {
                if (data) {
                  pushNamedTab('Editor', {
                    id: scriptId,
                    projectId: data.projectId.toString(),
                  })
                }
              }}
            >
              <Video className="w-4 h-4" />
              前往混剪
            </Button>
          </div>

          <Card className="border border-border shadow-sm flex-1 min-h-0 *:h-full *:rounded-xl bg-transparent overflow-y-auto pr-2">
            <Table className="table-fixed border-b">
              <TableHeader className="sticky top-0 rounded-sm">
                <TableRow className="rounded-lg *:border-r *:border-dashed">
                  <TableHead className="w-32 font-medium border-dashed">分镜编号</TableHead>
                  <TableHead className="w-32 font-medium border-dashed">景别</TableHead>
                  <TableHead className="font-medium border-dashed">
                    <div className="flex items-center justify-between">
                      <span>话术</span>
                    </div>
                  </TableHead>
                  <TableHead className="w-60 font-medium">参考画面要求/备注</TableHead>
                </TableRow>
              </TableHeader>
              {scenes?.length ? (
                <TableBody className="overflow-auto">
                  {scenes.map((scene, index) => (
                    <TableRow key={scene.id} className="*:h-60 *:border-r *:border-dashed">
                      <TableCell className="group relative">
                        <div>
                          <div className="text-base mb-2">分镜 {index + 1}</div>
                          <Input
                            placeholder="点击输入分镜名称"
                            value={scene.title}
                            onChange={e => updateScene(scene.id, 'title', e.target.value)}
                            className="rounded-none border-0 shadow-none p-0 h-auto text-sm focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent focus:bg-transparent hover:bg-transparent outline-none focus:outline-none"
                            style={{ boxShadow: 'none', outline: 'none' }}
                          />
                          <div className="flex absolute left-2 bottom-2 gap-2">
                            <Button
                              variant="secondary"
                              size="icon"
                              className="size-5 rounded bg-muted-foreground/60 hover:bg-muted-foreground/80 hidden group-hover:flex"
                              onClick={() => {
                                return setScenes(scenes.flatMap(s => (s.id === scene.id ? [s, newScene()] : [s])))
                              }}
                            >
                              <Plus className="size-4 text-white" />
                            </Button>
                            <Button
                              variant="secondary"
                              size="icon"
                              className="size-5 rounded bg-muted-foreground/60 hover:bg-muted-foreground/80 hidden group-hover:flex"
                              onClick={() => setScenes(scenes.filter(s => s.id !== scene.id))}
                            >
                              <Trash className="size-3.5 text-white" />
                            </Button>
                          </div>
                        </div>
                      </TableCell>

                      <TableCell className="text-center">
                        <div className="group inline-flex items-center gap-1 relative">
                          <Select
                            value={scene.shotType ?? ''}
                            onValueChange={value => updateScene(scene.id, 'shotType', value)}
                            onOpenChange={open => handleSelectOpenChange(scene.id, open)}
                          >
                            <SelectTrigger
                              className="border-0 shadow-none focus:ring-0 focus:ring-offset-0 bg-transparent
                        focus:bg-transparent hover:bg-transparent data-[state=open]:bg-transparent
                        outline-none focus:outline-none p-0 h-auto inline-flex items-center justify-between px-1"
                              style={{ boxShadow: 'none', outline: 'none' }}
                              asChild
                            >
                              <Button variant="secondary">
                                <SelectValue placeholder={<span className="text-muted-foreground">选择场景</span>} />
                                {!scene.shotType && (
                                  <ChevronRight
                                    className={`w-4 h-4 text-gray-400 transition-transform duration-200 flex-shrink-0 ${
                                      openSelects[scene.id] ? 'rotate-90' : 'rotate-0'
                                    }`}
                                  />
                                )}
                              </Button>
                            </SelectTrigger>
                            <SelectContent>
                              {shotTypes.map(type => (
                                <SelectItem key={type.value} value={type.value}>
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {scene.shotType && (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="absolute right-0 translate-x-1/1 hidden group-hover:inline-flex size-5 rounded-sm"
                              onClick={() => updateScene(scene.id, 'shotType', '')}
                            >
                              <CircleX className="size-4 fill-primary/40 text-background" />
                            </Button>
                          )}
                        </div>
                      </TableCell>

                      <TableCell className="p-0 relative overflow-visible">
                        <ScriptEditor
                          value={scene.script ?? ''}
                          onChange={value => updateScene(scene.id, 'script', value)}
                        />
                      </TableCell>

                      <TableCell className="align-top">
                        <div className="flex flex-col h-full relative">
                          <Textarea
                            placeholder="请输入画面要求，如：从包装盒取出护肤品"
                            value={scene.notes ?? ''}
                            onChange={e => updateScene(scene.id, 'notes', e.target.value)}
                            className="h-60 break-all rounded-none resize-none border-0 shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 p-0 bg-transparent focus:bg-transparent hover:bg-transparent outline-none focus:outline-none"
                            style={{ boxShadow: 'none', outline: 'none' }}
                          />
                          <SmallUploadArea
                            src={scene.refImg}
                            onUpdate={url => updateScene(scene.id, 'refImg', url)}
                            className="absolute left-1 bottom-1"
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              ) : (
                <TableCaption>
                  {scenes ? (
                    <div className="flex flex-col items-center gap-2">
                      <div className="text-sm text-gray-500">暂无分镜</div>
                    </div>
                  ) : (
                    <PageLoading />
                  )}
                </TableCaption>
              )}
            </Table>
          </Card>

          <Button variant="secondary" size="lg" disabled={!scenes} onClick={addNewScene}>
            <Plus className="w-4 h-4 mr-2" />
            新增分镜
          </Button>
        </ResizablePanel>
        <ResizableHandle />
        <ResizablePanel minSize={15} className="z-10 flex bg-background/20 flex-none">
          <div className="flex-1 min-w-0 flex flex-col border-r border-border">
            <div className="flex p-3">
              <Avatar className=" aspect-square h-full w-auto">
                <AvatarImage src={model?.icon} />
                <AvatarFallback>
                  <Bot className="size-6 stroke-1" />
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col ml-2 gap-2">
                <div className="flex items-end *:leading-none gap-1">
                  <span className="font-medium">AI 助手</span>
                  <span className="ml-auto text-xs text-muted-foreground">累计已使用 0/0 点</span>
                </div>
                <div>
                  <Select
                    value={model?.id.toString()}
                    onValueChange={v => setModel(modelList?.find(m => m.id.toString() === v))}
                  >
                    <SelectTrigger asChild>
                      <Button variant="secondary" size="sm" className="w-40 flex items-center justify-between">
                        <SelectValue placeholder="选择对话模型" />
                        <ChevronsUpDown className="size-4" />
                      </Button>
                    </SelectTrigger>
                    <SelectContent className="w-40">
                      {modelList?.map(m => (
                        <SelectItem key={m.id} value={m.id.toString()}>
                          {m.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
            {tab === 'chat' && (
              <div className="flex-1 flex flex-col overflow-auto *:px-3">
                {session ? (
                  <div className="flex justify-between items-center">
                    <h4 title={session.title} className="font-bold text-lg truncate">
                      {session.title}
                    </h4>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={async () => {
                          const result = await modalUpdateSession(session)
                          if (!result) return
                          await ScriptChatAPI.update({ id: session.id, title: result.title })
                          await refetchSessionData()
                        }}
                      >
                        <Edit3 className="size-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={async () => {
                          await ScriptChatAPI.delete({ id: session.id })
                          await refetchSessionData()
                          setSessionId(undefined)
                        }}
                      >
                        <Trash2 className="size-4" />
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <h4 className="font-bold text-lg">你好，欢迎使用 AI 助手</h4>
                    <p className="text-muted-foreground text-sm">快速生成脚本台词，一键应用至脚本中</p>
                    <div className="grid gap-4 grid-cols-[repeat(auto-fill,minmax(8rem,1fr))] p-4">
                      {categoryList?.map(category => (
                        <Button
                          key={category.id}
                          variant="secondary"
                          className="h-24 bg-muted-foreground/20"
                          onClick={() => {
                            setSelectedCategory(category)
                            setSheetOpen(true)
                          }}
                        >
                          {category.name}
                        </Button>
                      ))}
                    </div>
                  </>
                )}
                <div className="flex-1 flex flex-col-reverse overflow-auto">
                  {messages.map((msg, index) => (
                    <div className="flex flex-col not-first:mt-2 first:mb-auto" key={index}>
                      <div
                        data-role={msg.role}
                        className="flex gap-1 items-center data-[role=user]:self-end data-[role=user]:flex-row-reverse"
                      >
                        <span className="text-xs text-muted-foreground">{msg.role === 'user' ? '我' : 'AI 助手'}</span>
                        <span className="text-muted-foreground select-none">·</span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(msg.createTime).toLocaleString()}
                        </span>
                        {msg.pending && msg.role === 'assistant' && (
                          <Loader2 className="size-2.5 text-muted-foreground animate-spin" />
                        )}
                      </div>
                      {msg.role === 'user' ? (
                        <div
                          className={cn(
                            'self-end bg-primary/10 text-primary px-3 py-1.5 rounded-lg',
                            msg.pending && index === messages.length - 1 && 'animate-pulse',
                          )}
                        >
                          {msg.content}
                        </div>
                      ) : (
                        <>
                          <div className="self-start w-full bg-muted-foreground/10 text-muted-foreground px-3 py-1.5 rounded-lg">
                            <ThinkContext.Provider value={!msg.content.includes('</think>')}>
                              <Streamdown
                                rehypePlugins={[rehypeRaw, [rehypeSanitize, schema]]}
                                className="[&_code:not(:has(*))]:bg-muted-foreground/20 [&_*:has(>pre)]:bg-white [&_*:has(>pre)]:text-xs [&_*:has(>pre)+*]:bg-background"
                                components={{
                                  // @ts-ignore
                                  think: Think,
                                }}
                              >
                                {msg.content
                                  .trim()
                                  .replace(/<think>\n*/, '<think>')
                                  .replaceAll('\n', '<br>')}
                              </Streamdown>
                            </ThinkContext.Provider>
                          </div>
                          {!msg.pending && (
                            <div className="mt-1 flex items-center gap-2">
                              {messages.find(m => m.id === msg.parentId)?.isForm ? (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={async () => {
                                    const result = await modalConfirm({
                                      title: '确认将该回答应用到脚本中吗？',
                                      description: '此操作将覆盖现有脚本内容，是否继续？',
                                    })
                                    if (!result) return
                                    const doc = new DOMParser().parseFromString(msg.content, 'text/html')
                                    const table = doc.getElementsByTagName('table')[0]
                                    if (!table) return toast.error('未找到表格内容，无法应用')
                                    const tbody = table.getElementsByTagName('tbody')[0]
                                    if (!tbody) return toast.error('未找到表格主体，无法应用')
                                    const rows = Array.from(tbody.getElementsByTagName('tr'))
                                    if (!rows.length) return toast.error('未找到表格行，无法应用')
                                    const data = rows
                                      .map(row => {
                                        const cells = Array.from(row.getElementsByTagName('td')).slice(1, 3)
                                        return cells.map(cell => cell.textContent?.trim() || '')
                                      })
                                      .filter(v => v.length === 2)
                                    if (!data.length) return toast.error('未找到有效表格数据，无法应用')
                                    setScenes(
                                      data.map(d => ({
                                        id: crypto.randomUUID(),
                                        shotType: shotTypes
                                          .map(v => ({ ...v, index: d[1].indexOf(v.label) }))
                                          .filter(v => v.index > -1)
                                          .toSorted((a, b) => a.index - b.index)
                                          .at(0)?.value,
                                        script: d[0],
                                        notes: d[1],
                                      })),
                                    )
                                  }}
                                >
                                  <Upload className="-rotate-90 size-4" />
                                </Button>
                              ) : (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => navigator.clipboard.writeText(msg.content)}
                                >
                                  <Copy className="size-4" />
                                </Button>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => {
                                  const parent = messages.find(m => m.id === msg.parentId)
                                  if (!parent) return
                                  // FIXME: 分页加载接口可能找不到父消息
                                  handleMessageSend(parent.content)
                                }}
                              >
                                <RefreshCcw className="size-4" />
                              </Button>
                              <span className="text-xs text-muted-foreground">Token {msg.totalTokens}</span>
                              <span className="text-xs text-muted-foreground">耗时 {(msg.runTime ?? 0) / 1000} 秒</span>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  ))}
                </div>
                <div className="flex gap-2 py-2">
                  {categoryList?.map(category => (
                    <Button
                      key={category.id}
                      variant="secondary"
                      onClick={() => {
                        setSelectedCategory(category)
                        setSheetOpen(true)
                      }}
                    >
                      {category.name}
                    </Button>
                  ))}
                </div>
                <div className="flex flex-col border-t h-40 relative">
                  <textarea
                    value={inputMessage}
                    onChange={e => setInputMessage(e.target.value.slice(0, 4096))}
                    onKeyDown={e => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault()
                        if (!inputMessage.trim()) return
                        handleMessageSend()
                      }
                    }}
                    className="h-full resize-none px-3 py-2 text-sm placeholder:text-muted-foreground focus-within:outline-none"
                    placeholder="发消息，按 Shift + Enter 换行"
                  />
                  <Button variant="ghost" size="icon" className="absolute left-3 bottom-2 bg-background">
                    <BrushCleaning className="size-4" />
                  </Button>
                  <div className="flex items-center gap-2 absolute right-3 bottom-2">
                    <span className="text-xs text-muted-foreground rounded px-1 py-0.5 bg-background">
                      {inputMessage.length}/4096
                    </span>
                    <Button
                      size="icon"
                      className="rounded-full not-disabled:bg-violet-500 hover:bg-violet-500/80 not-disabled:text-primary transition-none disabled:pointer-events-none"
                      disabled={!inputMessage.trim() || !model || pending}
                      onClick={() => handleMessageSend()}
                    >
                      <ArrowUpFromDot className="size-4" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
            {tab === 'history' &&
              (sessionList.length ? (
                <div className="flex flex-col px-3 py-2 overflow-auto" ref={sessionListScrollRef}>
                  {sessionList.map(session => (
                    <div
                      key={session.id}
                      className="flex items-center justify-between p-3 hover:bg-muted-foreground/10 rounded-lg cursor-pointer"
                      onClick={() => {
                        setSessionId(session.id)
                        setTab('chat')
                      }}
                    >
                      <div className="flex flex-col items-start">
                        <h5 className="font-medium">{session.title || '未命名对话'}</h5>
                        <p className="text-sm text-muted-foreground line-clamp-2">{session.content}</p>
                        <span className="text-xs text-muted-foreground mt-2">
                          {new Date(session.createTime).toLocaleString()}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={async e => {
                            e.stopPropagation()
                            const result = await modalUpdateSession(session)
                            if (!result) return
                            await ScriptChatAPI.update({ id: session.id, title: result.title })
                            await refetchSessionData()
                          }}
                        >
                          <Edit3 className="size-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={async e => {
                            e.stopPropagation()
                            await ScriptChatAPI.delete({ id: session.id })
                            await refetchSessionData()
                            if (sessionId === session.id) setSessionId(undefined)
                          }}
                        >
                          <Trash2 className="size-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                  <NextPageFetcher />
                </div>
              ) : (
                <div className="p-4 text-sm text-muted-foreground">暂无历史对话</div>
              ))}
          </div>
          <div className="flex flex-col *:text-xs *:size-20 *:rounded-none *:flex-col *:aria-selected:bg-muted-foreground/20">
            <Button
              variant="ghost"
              onClick={() => {
                setTab('chat')
                setSessionId(undefined)
              }}
            >
              <Plus className="size-8 stroke-1" />
              新建对话
            </Button>
            <Button variant="ghost" aria-selected={tab === 'chat'} onClick={() => setTab('chat')}>
              <Bot className="size-6 stroke-1" />
              AI 对话
            </Button>
            <Button variant="ghost" aria-selected={tab === 'history'} onClick={() => setTab('history')}>
              <History className="size-6 stroke-1" />
              历史对话
            </Button>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>

      <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
        <SheetContent className="w-120 flex flex-col">
          <SheetClose asChild>
            <Button variant="ghost" size="icon" className="absolute right-4 top-4 z-30">
              <X className="size-4" />
            </Button>
          </SheetClose>
          <SheetHeader>
            <SheetTitle>{selectedCategory?.name}</SheetTitle>
          </SheetHeader>
          <Tabs defaultValue="form" className="w-full" value={selectedSubCategory?.id.toString()}>
            <TabsList className="bg-muted/30">
              {subCategoryList?.map(category => (
                <TabsTrigger
                  key={category.id}
                  value={category.id.toString()}
                  onClick={() => setSelectedSubCategory(category)}
                  className="bg-transparent hover:bg-transparent text-primary hover:text-primary !aria-selected:text-secondary"
                >
                  {category.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
          <form
            className="flex flex-col flex-1"
            onSubmit={async e => {
              e.preventDefault()
              if (formRef.current?.submit && !formRef.current?.submit?.()) return
              const formData = new FormData(e.currentTarget)
              const data = Object.fromEntries(formData.entries())
              setSheetOpen(false)
              await handleScriptGenerate(formConfig!.promptCode, data)
            }}
          >
            <div className="flex-1">
              {formConfig?.formContent ? (
                <DynamicComponent code={formConfig.formContent} ref={formRef} />
              ) : (
                <div className="p-4 text-sm text-muted-foreground">暂无表单内容</div>
              )}
            </div>
            <SheetFooter>
              <Button size="lg" type="submit" className="flex-1">
                生成
              </Button>
            </SheetFooter>
          </form>
        </SheetContent>
      </Sheet>
    </div>
  )
}
