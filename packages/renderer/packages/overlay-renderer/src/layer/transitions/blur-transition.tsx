// 模糊转场组件
import React, { useMemo, useRef } from 'react'
import { ThreeCanvas, useVideoTexture } from '@remotion/three'
import { useRenderContext } from '../../render.context'
import { TransitionBaseProps } from '../../types'
import { Video } from 'remotion'

// 基础的 Three.js 视频平面组件
const VideoPlane: React.FC<{
  video: React.RefObject<HTMLVideoElement | null>
  opacity: number
  aspect: number
}> = ({ video, aspect, opacity }) => {
  const videoTexture = useVideoTexture(video)

  return (
    <mesh>
      <planeGeometry args={[1, 1 / aspect]} />
      {videoTexture && (
        <meshBasicMaterial
          map={videoTexture}
        />
      )}
    </mesh>
  )
}

export const BlurTransition: React.FC<TransitionBaseProps> = ({
  fromVideo,
  toVideo,
  progress
}) => {
  const { playerMetadata: { width, height } } = useRenderContext()

  // 计算透明度
  const { fromOpacity, toOpacity } = useMemo(() => {
    if (progress <= 0.5) {
      // 前半段：fromVideo 显示，toVideo 隐藏
      return {
        fromOpacity: 1,
        toOpacity: 0
      }
    } else {
      // 后半段：fromVideo 淡出，toVideo 淡入
      const halfProgress = (progress - 0.5) * 2
      return {
        fromOpacity: 1 - halfProgress,
        toOpacity: halfProgress
      }
    }
  }, [progress])

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width,
    height,
    overflow: 'hidden',
  }

  const layerStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  }

  const fromVideoRef = useRef<HTMLVideoElement | null>(null)
  const toVideoRef = useRef<HTMLVideoElement | null>(null)

  return (
    <div style={containerStyle} id={`blur-${fromVideo.id}-${toVideo.id}`}>
      {/* 源视频层 - 使用 Three.js 渲染 */}
      <div style={{ ...layerStyle, zIndex: 1 }}>
        {/*{fromOpacity > 0.01 ? (*/}
        <ThreeCanvas
          orthographic={false}
          width={width}
          height={height}
          camera={{ position: [0, 0, 1] }}
          style={{ width: '100%', height: '100%' }}
        >
          <VideoPlane video={fromVideoRef} aspect={fromVideo.originalMeta.width / fromVideo.originalMeta.height} opacity={fromOpacity} />
        </ThreeCanvas>
        {/*) : null}*/}
      </div>

      {/* 目标视频层 - 使用 Three.js 渲染 */}
      {/*<div style={{ ...layerStyle, zIndex: 2 }}>*/}
      {/*  {toOpacity > 0.01 ? (*/}
      {/*    <ThreeCanvas*/}
      {/*      width={width}*/}
      {/*      height={height}*/}
      {/*      camera={{ position: [0, 0, 1] }}*/}
      {/*      style={{ width: '100%', height: '100%' }}*/}
      {/*    >*/}
      {/*      /!*<ambientLight intensity={1} />*!/*/}
      {/*      <VideoPlane video={toVideo} opacity={toOpacity} />*/}
      {/*    </ThreeCanvas>*/}
      {/*  ) : null}*/}
      {/*</div>*/}

      <Video
        ref={fromVideoRef}
        src={fromVideo.localSrc || fromVideo.src}
        style={{ position: 'absolute', opacity: 0 }}
      />
    </div>
  )
}
