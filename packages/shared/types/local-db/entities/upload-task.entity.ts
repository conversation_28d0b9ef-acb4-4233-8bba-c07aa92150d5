import { BaseEntity, OwnershipFields } from '../base.js'

/**
 * 上传任务相关类型
 */
export namespace UploadTask {

  export const DEFAULT_MAX_SIZE = 1024 * 1024 * 1024    // 1GB

  /**
   * 上传任务状态枚举
   */
  export enum Status {
    /** 等待上传 */
    PENDING = 0,

    /** 上传中 */
    UPLOADING = 1,

    /** 已暂停 */
    PAUSED = 2,

    /** 上传完成 */
    COMPLETED = 3,

    /** 上传失败 */
    FAILED = 4,

    /** 已取消 */
    CANCELLED = 5
  }

  /**
   * 上传任务类型枚举
   */
  export enum Type {
    MEDIA = 1,
    VIDEO = 2,
    AUDIO = 3,
    IMAGE = 4,
    OTHER = 5,
  }

  /**
   * 上传任务来源场景枚举
   */
  export enum UploadSourceScenes {
    /**
     * 页面位置：视频编辑器 - 左侧资源区 - 我的贴纸
     */
    TEAM_SHARED_PASTER = 'team_shared_paster',

    /**
     * 页面位置：视频编辑器 - 左侧资源区 - 我的音乐
     */
    TEAM_SHARED_MUSIC = 'team_shared_music',

    /**
     * 页面位置：视频编辑器 - 左侧资源区 - 我的音效
     */
    TEAM_SHARED_VOICE = 'team_shared_voice',

    /**
     * 页面位置：
     *   1. 视频编辑器 - 左侧资源区 - 素材库
     *   2. 首页 - 项目管理 - 我的素材
     */
    MATERIAL_LIBRARY = 'material_library',

    /**
     * 页面位置：爆款解析 - 上传视频
     */
    HOT_ANALYZER = 'hot_analyzer',
  }

  /**
   * 上传任务基础接口
   */
  export interface IUploadTask extends BaseEntity, OwnershipFields {
    id: number
    team_id: number
    name: string
    local_path: string
    url: string
    hash: string
    size: number
    progress: number
    status: Status
    type: Type
    reason: string
    folder_id: string
    object_key: string
    object_id: string
    upload_module: string
    checkpoint_data: string
    batch_id?: number

    /**
     * 上传任务的来源场景
     */
    source_scene: UploadSourceScenes
  }

  /**
   * 创建上传任务参数
   */
  export interface CreateParams extends OwnershipFields {
    name: string
    local_path: string
    type?: Type
    folder_id?: string
    upload_module?: string
    size?: number
    hash?: string
    batch_id?: number
    source_scene: UploadSourceScenes
  }

  /**
   * 更新上传任务参数
   */
  export interface UpdateParams {
    name?: string
    url?: string
    hash?: string
    type?: Type
    progress?: number
    status?: Status
    reason?: string
    cover?: string
    object_key?: string
    object_id?: string
    resume_data?: string
    checkpoint_data?: string
    size?: number
    local_path?: string
    batch_id?: number
    library?: string
  }

  /**
   * 查询上传任务参数
   */
  export interface QueryParams extends Partial<OwnershipFields> {
    status?: Status | Status[]
    type?: Type | Type[]
    folder_id?: string
    keyword?: string
    start_date?: number
    end_date?: number
  }

  /**
   * 上传任务统计结果
   */
  export interface StatsResult {
    total_count: number
    pending_count: number
    uploading_count: number
    paused_count: number
    completed_count: number
    failed_count: number
    cancelled_count: number
    total_size: number
    uploaded_size: number
    type_distribution: Record<Type, number>
  }

  /**
   * 批量操作参数
   */
  export interface BatchParams {
    ids: number[]
    action: 'pause' | 'resume' | 'cancel' | 'retry' | 'delete'
  }

  /**
   * 上传进度事件数据
   */
  export interface ProgressEvent {
    task_id: number
    progress: number
    speed?: number
    estimated_time?: number
  }

  export interface CompleteEvent {
    batchId: number,
    isCompleted: boolean,
    tasks: UploadTask.IUploadTask[]
  }
  /**
   * 上传任务队列配置
   */
  export interface QueueConfig {
    max_concurrent_uploads: number
    retry_attempts: number
    retry_delay: number
    chunk_size: number
  }

  /**
   * 断点续传数据
   */
  export interface ResumeData {
    upload_id?: string
    part_number?: number
    etag?: string
    checkpoint?: any
  }
}
