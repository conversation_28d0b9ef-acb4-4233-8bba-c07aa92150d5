import React, { useCallback } from 'react'

import { TextOverlay } from '@app/shared/types/overlay'
import { ResourceCacheType } from '@app/shared/types/resource-cache.types'

import { useInfiniteQueryFontStyleList } from '@/hooks/queries/useQueryTextStyle'

import { StyledTextResource } from '@/types/resources'
import { cacheManager } from '@/libs/cache/cache-manager'

import InfiniteResourceList, { HeaderContent } from '@/components/InfiniteResourceList'
import { useFontManager } from '../../hooks/useFontManager'
import { useResourceLoadingStore } from '../../hooks/resource/useResourceLoadingStore'
import { buildTextOverlay } from '../../utils/text'
import { cn } from '@/components/lib/utils'
import { TextLayerRenderer } from '@clipnest/overlay-renderer'

interface FontStyleSelectorProps {
  className?: string
  baseOverlay?: Partial<TextOverlay>
  headerContent?: HeaderContent

  itemWrapper?: (children: React.ReactNode, data: StyledTextResource.StyledText) => React.ReactNode
}

type StyledTextItemProps = StyledTextResource.StyledText & {
  className?: string
}

const StyledTextItem: React.FC<StyledTextItemProps> = ({ className, ...data }) => {
  const { isFontStyleLoaded } = useFontManager()

  const { content } = data
  const fontPath = content.fontPath

  const isFontLoaded = isFontStyleLoaded(fontPath)

  const previewOverlay = buildTextOverlay(data, { isPreview: true })

  // 如果字体已加载，更新字体名称
  if (isFontLoaded && content.fontName) {
    previewOverlay.styles.fontFamily = `"${content.fontName}"`
  }

  return (
    <div
      key={data.id.toString()}
      className={cn(
        'select-none group relative overflow-hidden border bg-gray-200 dark:bg-background rounded border-white/10 transition-all aspect-square w-20',
        className
      )}
    >
      <div className="h-full w-full flex items-center justify-center rounded">
        <div
          className="text-base transform-gpu transition-transform group-hover:scale-102 dark:text-white text-gray-900/90 size-full flex justify-center items-center"
        >
          <TextLayerRenderer overlay={previewOverlay} />
        </div>
      </div>

      {/* Font Name Label */}
      {/*<div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-1 truncate">*/}
      {/*  {title}*/}
      {/*</div>*/}
    </div>
  )
}

const StyledTextSelectorRenderer: React.FC<FontStyleSelectorProps> = ({
  className = 'h-full', headerContent, itemWrapper
}) => {
  const { isResourceLoading } = useResourceLoadingStore()
  const { isFontStyleLoaded, loadFont } = useFontManager()

  const infiniteFontStyleQuery = useInfiniteQueryFontStyleList({
    pageNo: 1,
    pageSize: 50
  })

  const renderItem = useCallback((item: StyledTextResource.StyledText) => {
    const base = () => (
      <StyledTextItem{...item} />
    )

    if (itemWrapper) {
      return itemWrapper(base(), item)
    }

    return base()
  }, [])

  // 预加载当前页面的所有字体
  React.useEffect(() => {
    const preloadVisibleFonts = async () => {
      if (infiniteFontStyleQuery.data?.pages) {
        const allItems = infiniteFontStyleQuery.data.pages.flatMap(page => page.list || [])

        // 限制预加载数量，防止无限加载
        const maxPreloadCount = 10
        let processedCount = 0

        // 过滤出需要加载的字体
        const fontsToLoad = allItems.filter(item => {
          const { content } = item
          const fontPath = content.fontPath

          if (!content.fontName || !fontPath) {
            return false
          }

          // 检查字体是否已经在 DOM 中加载
          const isFontLoaded = isFontStyleLoaded(fontPath)
          if (isFontLoaded) {
            return false
          }

          // 检查字体是否正在加载中
          const isLoading = isResourceLoading(fontPath, ResourceCacheType.FONT)
          if (isLoading) {
            return false
          }

          // 检查字体是否已经下载到本地缓存
          const localPath = cacheManager.resource.getResourcePathSync(ResourceCacheType.FONT, fontPath)
          if (localPath) {
            // 如果已经下载但未加载到 DOM，只需要加载到 DOM
            console.debug('[字体预加载] 字体已下载，仅需加载到 DOM:', content.fontName)
            return true
          }

          // 字体未下载，需要下载并加载
          return true
        }).slice(0, maxPreloadCount) // 限制预加载数量

        console.debug(`[字体预加载] 需要处理 ${fontsToLoad.length} 个字体 (最大 ${maxPreloadCount} 个)`)

        // 逐个处理字体，避免并发下载
        for (const item of fontsToLoad) {
          if (processedCount >= maxPreloadCount) {
            // console.debug('[字体预加载] 已达到最大预加载数量，停止预加载')
            break
          }

          const { content } = item
          const fontPath = content.fontPath

          try {
            await loadFont(fontPath, content.fontName)
            console.debug('[字体预加载] 字体处理成功:', content.fontName)
            processedCount++
          } catch (error) {
            console.warn('[字体预加载] 字体处理失败:', content.fontName, error)
          }
        }
      }
    }

    void preloadVisibleFonts()
  }, [infiniteFontStyleQuery.data, isFontStyleLoaded, loadFont, isResourceLoading])

  return (
    <div className={className}>
      <InfiniteResourceList
        headerContent={headerContent}
        queryResult={infiniteFontStyleQuery}
        emptyText="没有找到花体字样式"
        loadingText="加载花体字样式中..."
        itemsContainerClassName="flex flex-wrap gap-3 p-2"
        renderItem={renderItem}
      />
    </div>
  )
}

export const StyledTextSelector = Object.assign(StyledTextSelectorRenderer, {
  ItemRenderer: StyledTextItem
})
