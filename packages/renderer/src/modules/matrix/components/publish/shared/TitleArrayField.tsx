import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { PlatformKey } from '@/libs/request/api/generic-matrix'
import { Label } from '@radix-ui/react-dropdown-menu'
import { Plus, Trash2 } from 'lucide-react'
import React from 'react'
import { FieldValues, Control, FieldArrayPath, FieldErrors, useFieldArray, Controller, Path } from 'react-hook-form'

interface TitleFieldConfig {
  showDescription?: boolean
  descriptionSupportsTag?: boolean
  placeholder?: string
  maxLength?: number,
}

const platformTitleConfig: Record<PlatformKey, TitleFieldConfig> = {
  [PlatformKey.dy]: {
    showDescription: false,
    placeholder: '标题内容在此输入，可使用 #话题 标签',
    maxLength: 200,
  },
  [PlatformKey.xhs]: {
    showDescription: true,
    descriptionSupportsTag: true,
    placeholder: '标题内容在此输入，可使用 #话题 标签',
    maxLength: 200,

  },
  [PlatformKey.ks]: {
    showDescription: true,
    descriptionSupportsTag: false,
    placeholder: '标题内容在此输入',
    maxLength: 200,

  },
}

interface TitleManageProps<T extends FieldValues> {
  control: Control<T>
  namePrefix: FieldArrayPath<T>
  errors?: FieldErrors
  platform: PlatformKey
}

export function TitleManage<T extends FieldValues>({
  control,
  namePrefix,
  errors,
  platform,
}: TitleManageProps<T>) {
  const { fields, append, remove } = useFieldArray<T>({
    control,
    name: namePrefix,
  })

  const config = platformTitleConfig[platform]
  const MAX_TITLES = 100
  const canAddMore = fields.length < MAX_TITLES

  const formatTags = (value: string) => {
    if (config.descriptionSupportsTag) {
      return value.replace(/(#[^\s#]+)(?=#)/g, '$1 ')
    }
    return value
  }

  return (
    <div className="space-y-2">
      <Label>标题</Label>
      <div className="flex gap-2 items-center">
        <Button
          type="button"
          variant="outline"
          onClick={() => append({ title: '', description: '' } as any)}
          disabled={!canAddMore}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" /> 添加标题
        </Button>
        <span className="text-sm text-muted-foreground">
          已选择 {fields.length}/{MAX_TITLES}
        </span>
      </div>
      <div className="text-muted text-sm">
        标题优先跟随视频本身携带的标题，若添加标题，则未添加标题的视频与添加的标题做随机组合。
      </div>

      <div className="space-y-6">
        {fields.map((field, index) => (
          <div key={field.id} className="flex items-center gap-2">
            <div className="flex-1 space-y-2">
              {/* 标题 */}
              <div className="text-xs mb-1">标题{ index + 1 }</div>
              <Controller
                name={`${namePrefix}.${index}.title` as Path<T>}
                control={control}
                render={({ field: { value, onChange } }) => (
                  <Textarea
                    showLength
                    maxLength={config.maxLength}
                    placeholder={config.placeholder?.replace(
                      '${index}',
                      (index + 1).toString()
                    )}
                    className={errors?.titles?.[index]?.value ? 'border-red-500' : ''}
                    value={value}
                    onChange={val => onChange(formatTags(val.target.value))}
                  />
                )}
              />
              {errors?.titles?.[index]?.value && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.titles[index]?.value?.message}
                </p>
              )}

              {/* 简介 */}
              {config.showDescription && (
                <div>
                  <div className="text-xs mb-1">简介{ index + 1 }</div>
                  <Controller
                    name={`${namePrefix}.${index}.desc` as Path<T>}
                    control={control}
                    render={({ field: { value, onChange } }) => (
                      <Textarea
                        placeholder="简介内容在此输入"
                        value={value}
                        onChange={val => onChange(formatTags(val.target.value))}
                        className="text-sm"
                      />
                    )}
                  />
                </div>
              )}
            </div>

            {/* 删除按钮 */}
            {fields.length > 1 && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => remove(index)}
                className="flex items-center gap-1 text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        ))}
      </div>

      {errors?.titles && (
        <p className="text-sm text-red-500">{(errors.titles as any).message}</p>
      )}
    </div>
  )
}
