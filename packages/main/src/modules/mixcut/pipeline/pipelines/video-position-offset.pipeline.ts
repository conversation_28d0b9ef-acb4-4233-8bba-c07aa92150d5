import { SimpleMixcutPipeline } from '../mixcut-pipeline.types.js'
import { VideoOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut.js'

/**
* 随机偏移视频的位置，并补偿大小以保证填满画面
*/
export class VideoPositionOffsetPipeline extends SimpleMixcutPipeline<typeof MIXCUT_PIPELINES.video.positionOffset, VideoOverlay> {

  filter(overlay: MixcutableOverlay): overlay is VideoOverlay & MixcutableOverlay {
    return SimpleMixcutPipeline.filters.videoOverlay(overlay)
  }

  async processSingleOverlay(overlay: VideoOverlay & MixcutableOverlay) {
    const { range = 0.25 } = this.context.params
    const offsetPercent = _.random(0, range)

    return {
      ...overlay,
      left: overlay.left - Math.round(overlay.width * offsetPercent),
      top: overlay.top - Math.round(overlay.height * offsetPercent),
      width: Math.round(overlay.width * (1 + offsetPercent)),
      height: Math.round(overlay.height * (1 + offsetPercent)),
    }
  }
}
