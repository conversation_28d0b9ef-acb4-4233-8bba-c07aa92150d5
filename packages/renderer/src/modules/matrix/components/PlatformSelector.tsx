import React from 'react'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { cn } from '@/components/lib/utils'
import { usePlatform, usePlatformSwitch } from '@/modules/matrix/context/context'
import { PlatformKey } from '@/libs/request/api/generic-matrix'
import { PLATFORM_CONFIG } from '../constants/shared'

interface PlatformSelectorProps {
  className?: string
  onPlatformChange?: (platform: PlatformKey) => Promise<void>
  disabled?: boolean
}

export const PlatformSelector: React.FC<PlatformSelectorProps> = ({
  className,
  onPlatformChange,
  disabled = false
}) => {
  const { currentPlatform } = usePlatform()
  const { switchPlatform } = usePlatformSwitch()
  const allPlatforms =  Object.values(PLATFORM_CONFIG)

  const handlePlatformChange = async (value: string) => {
    const platform = value as PlatformKey
    if (platform === currentPlatform) return

    await switchPlatform(platform, onPlatformChange)
  }

  return (
    <div className={cn('flex', className)}>

      <RadioGroup
        value={currentPlatform}
        onValueChange={handlePlatformChange}
        disabled={disabled }
        className="flex items-center gap-3"
      >
        {allPlatforms.map(platform => (
          <div key={platform.key}>
            <RadioGroupItem
              value={platform.key}
              id={platform.key}
              disabled={disabled }
              className="hidden"
            />
            <Label
              htmlFor={platform.key}
              className={
                cn(currentPlatform === platform.key ? ' border-gradient-brand-dark' : ' bg-neutral-700/50 hover:bg-neutral-700/70',
                  'flex items-center cursor-pointer space-x-2 h-10  rounded px-4 transition-all border-2'
                )
              }
            >
              <img src={platform.icon} alt={platform.name} className="w-6 h-6 bg-transparent object-contain" />
              <span className="font-bold ">{platform.name}</span>
            </Label>
          </div>
        ))}
      </RadioGroup>
      
    </div>
  )
}

export default PlatformSelector