import { Platform<PERSON>ey } from '@/libs/request/api/generic-matrix'
import React, { FC, useState } from 'react'
import { cn } from '../../../components/lib/utils'
import { platformIconMap } from '../constants/shared'

export interface PlatformSelectorItem {
  key: PlatformKey
  name: string
  desc: string
  icon?: any
}

interface PublishChannelSelectorProps {
  value?: {
    selected: PlatformKey[]
    active?: PlatformKey
  }
  onChange?: (value: {
    selected: PlatformKey[]
    active?: PlatformKey
  }) => void
}

const PLATFORMS: PlatformSelectorItem[] = [
  {
    key: PlatformKey.dy,
    name: '抖音',
    desc: '支持发布到抖音平台',
    icon: platformIconMap[PlatformKey.dy],
  },

  {
    key: PlatformKey.xhs,
    name: '小红书',
    desc: '支持发布到小红书',
    icon: platformIconMap[PlatformKey.xhs],
  },
  {
    key: PlatformKey.ks,
    name: '快手',
    desc: '支持发布到快手',
    icon: platformIconMap[PlatformKey.ks],
  }
]

export const PublishChannelSelector: FC<PublishChannelSelectorProps> = ({
  value,
  onChange,
}) => {
  const [internal, setInternal] = useState<{
    selected: PlatformKey[]
    active?: PlatformKey
  }>({
    selected: value?.selected ?? [],
    active: value?.active,
  })

  const state = value ?? internal

  const setState = (next: typeof internal) => {
    setInternal(next)
    onChange?.(next)
  }

  // 切换多选
  const toggle = (key: PlatformKey) => {
    let nextSelected: PlatformKey[]
    if (state.selected.includes(key)) {
      nextSelected = state.selected.filter(v => v !== key)
    } else {
      nextSelected = [...state.selected, key]
    }
    setState({ ...state, selected: nextSelected })
  }

  // 点击卡片（单选）
  const handlePlatformClick = (key: PlatformKey) => {
    setState({
      selected: state.selected.includes(key) ? state.selected : [...state.selected, key],
      active: key,
    })
  }

  return (
    <div className="flex gap-4">
      {PLATFORMS.map(p => {
        const isSelected = state.selected.includes(p.key)
        const isActive = state.active === p.key
        return (
          <div
            key={p.key}
            className={cn(
              'w-48 rounded-lg border  p-4 cursor-pointer transition-all bg-neutral-800/20 group',
              'flex flex-col gap-2',
              isSelected
                ? 'border-primary-accent/30 hover:border-primary-accent shadow-md  bg-primary-highlight/10'
                : 'border-neutral-500 hover:border-gray-400 ',
              isActive ? 'ring-1 ring-primary-accent' : ''
            )}
            onClick={() => handlePlatformClick(p.key)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <img src={p.icon} className="w-6 h-6 rounded bg-transparent object-cover" />
                <span className="font-bold text-lg">{p.name}</span>
              </div>
             
              {
                isSelected ? (
                  <div 
                    onClick={e => {
                      e.stopPropagation()
                      toggle(p.key)
                    }}
                    className="w-5 h-5  rounded-full bg-primary-highlight/60 flex items-center justify-center text-white text-xs"
                  >
                    ✓
                  </div>
                ) : (
                  <div className="group-hover:block hidden">
                    <div className="w-5 h-5 rounded-full border border-primary-highlight/20" />
                  </div>
                )
              }
            </div>
            <div className="text-sm text-gray-300">{p.desc}</div>
          </div>
        )
      })}
    </div>
  )
}
