import { ScriptChatAPI } from '@/libs/request/api/script-chat'
import { QUERY_KEYS } from '@/constants/queryKeys'
import useInfiniteQuery from '../useInfiniteQuery'
import { useQuery } from '@tanstack/react-query'

export const useInfiniteQueryScriptChatSessionList = (scriptId: number) => {
  return useInfiniteQuery(
    [QUERY_KEYS.SCRIPT_CHAT_SESSION_PAGE, scriptId],
    ScriptChatAPI.list,
    { scriptId },
    { enabled: !!scriptId },
  )
}

export const useInfiniteQueryScriptChatMessageList = (sessionId?: number) => {
  return useInfiniteQuery(
    [QUERY_KEYS.SCRIPT_CHAT_MESSAGE_PAGE],
    ScriptChatAPI.messages,
    { sessionId },
    { enabled: !!sessionId },
  )
}

export const useQueryScriptChatModelList = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.SCRIPT_CHAT_MODEL_LIST],
    queryFn: () => ScriptChatAPI.models({}),
  })
}

export const useQueryScriptChatCategoryList = (id?: number) => {
  return useQuery({
    queryKey: [QUERY_KEYS.SCRIPT_CHAT_CATEGORY_LIST, id],
    queryFn: () => ScriptChatAPI.categories({ id: id! }),
    enabled: !!id,
  })
}

export const useQueryScriptChatFormConfig = (id?: number) => {
  return useQuery({
    queryKey: [QUERY_KEYS.SCRIPT_CHAT_FORM_CONFIG, id],
    queryFn: () => ScriptChatAPI.formConfig({ id: id! }),
    enabled: !!id,
    staleTime: 0,
  })
}

export const useQueryScriptChatFormExample = (id: number) => {
  return useQuery({
    queryKey: [QUERY_KEYS.SCRIPT_CHAT_FORM_EXAMPLE, id],
    queryFn: () => ScriptChatAPI.formExample({ id }),
    enabled: !!id,
  })
}
