import React, { useEffect, useMemo, useState } from 'react'
import { useQueries } from '@tanstack/react-query'
import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useInfiniteQueryProjectList } from '@/hooks/queries/useQueryProject'
import { fetchMaterialDirectoryList } from '@/hooks/queries/useQueryMaterial'
import TreeList, { findNodeById, TreeNode } from '@/components/TreeList'
import { Plus } from 'lucide-react'
import { SearchInput } from '@/components/ui/search-input'
import { useItemActions } from '@/hooks/useItemActions'
import { ResourceSource, FolderActionKeys } from '@/types/resources'
import { QUERY_KEYS } from '@/constants/queryKeys'

interface MoveDialogProps {
  open: boolean
  moveId: string
  moveType: ResourceSource
  dirList?: TreeNode[]
  onOpenChange: (open: boolean) => void
  onConfirm: (selectedNode: any) => void
}

const MoveDialog: React.FC<MoveDialogProps> = ({
  open,
  moveId,
  dirList,
  moveType = ResourceSource.FOLDER,
  onOpenChange,
  onConfirm,
}) => {
  const [keyword, setKeyword] = useState('')
  const [selectedNode, setSelectedNode] = useState<any>(null)
  const { createItem, moveItem } = useItemActions()

  const { data: projectData } = useInfiniteQueryProjectList({})
  const projects = useMemo(() => projectData?.pages.flatMap(page => page.list) || [], [projectData])

  // 针对每个项目请求目录树
  const projectTrees = useQueries({
    queries: projects.map(project => ({
      queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST, project.id, keyword],
      queryFn: () => fetchMaterialDirectoryList({ projectId: Number(project.id), keyword }),
      enabled: !!projects.length,
    })),
  })

  /** 拼接为项目+目录的树结构 */
  const combinedTreeData = useMemo(() => {
    return projects.map((project, index) => ({
      id: `project-${project.id}`,
      label: project.projectName,
      type: 'disabled', // 用于区分项目节点
      children: projectTrees[index]?.data || [],
    }))
  }, [projects, projectTrees])
  // 定义本地资源类型集合
  const localTypes = useMemo(
    () => [
      ResourceSource.LOCAL_STICK,
      ResourceSource.LOCAL_STICK_FOLDER,
      ResourceSource.LOCAL_MUSIC,
      ResourceSource.LOCAL_MUSIC_FOLDER,
      ResourceSource.LOCAL_SOUND,
      ResourceSource.LOCAL_SOUND_FOLDER,
      ResourceSource.LOCAL_STICK_MULTI_SELECT,
      ResourceSource.LOCAL_MUSIC_MULTI_SELECT,
      ResourceSource.LOCAL_SOUND_MULTI_SELECT,
    ],
    []
  )
  const treeData = useMemo(() => {
    //如果是本地资源，直接使用传入的dirList
    return localTypes.includes(moveType) ? dirList || [] : combinedTreeData
  }, [moveType, dirList, combinedTreeData])

  const handleConfirm = async () => {
    if (!selectedNode || selectedNode.type === 'disabled') return

    try {
      if (
        moveType !== ResourceSource.MULTI_SELECT &&
        moveType !== ResourceSource.LOCAL_STICK_MULTI_SELECT &&
        moveType !== ResourceSource.LOCAL_MUSIC_MULTI_SELECT &&
        moveType !== ResourceSource.LOCAL_SOUND_MULTI_SELECT
      ) {
        await moveItem(moveType, moveId, selectedNode.id)
      }
      onConfirm?.(selectedNode)
      onOpenChange(false)
    } catch (error) {
      if (error.message === '存储目录已经存在') throw new Error('目标目录里存在同名目录')
      throw new Error(error.message)
    }
  }

  const actions = [
    {
      icon: <Plus className="w-4 h-4" />,
      label: '新建',
      value: FolderActionKeys.CREATE,
      onClick: (nodeId: string) => {
        const node = findNodeById(treeData, nodeId)
        const children = node?.children || []
        return createItem(
          moveType,
          nodeId,
          {
            label: '',
            headerTitle: '文件夹',
          },
          children,
        )
      },
    },
  ]

  useEffect(() => {
    if (!open) {
      setKeyword('')
    }
  }, [open])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>移动</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-auto py-4">
          <SearchInput
            placeholder="关键词搜索"
            value={keyword}
            onChange={e => setKeyword(e.target.value)}
            size="lg"
            containerClassName="mx-4"
          />
          <TreeList
            data={treeData}
            className="w-full flex-1 max-h-[500px] overflow-auto"
            selectStyle="bg-primary-highlight1 text-black"
            actions={localTypes.includes(moveType) ? [] : actions}
            showEllipsis={false}
            keyword={keyword}
            onSelect={node => {
              setSelectedNode(node)
            }}
          />
        </div>

        <DialogFooter>
          <button onClick={() => onOpenChange(false)} className="px-4 py-2 bg-primary/20 rounded-md">
            取消
          </button>
          <button onClick={handleConfirm} className="px-4 py-2 bg-primary-highlight1 text-white rounded-md">
            确定
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default MoveDialog
