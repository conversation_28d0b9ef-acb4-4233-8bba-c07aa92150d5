import { Overlay, TextOverlay } from '@app/shared/types/overlay.js'
import { MixcutPipelineSchemas } from '@app/shared/types/mixcut.js'
import _ from 'lodash'
import z from 'zod'

export namespace MixcutPipelineUtils {

  export function mergeStyles(
    styles: TextOverlay['styles'],
    config: z.infer<(typeof MixcutPipelineSchemas.textStyledTextSchema)>['options'][number]
  ) {
    return _.merge(
      {},
      styles,
      {
        color: config.textColor,
        backgroundColor: config?.backgroundColor,

        strokeEnabled: Boolean(config?.borderWidth),
        strokeWidth: config?.borderWidth,
        strokeColor: config?.borderColor,

        shadowEnabled: Boolean(config?.shadowDistance),
        shadowDistance: config?.shadowDistance,
        shadowAngle: config?.shadowAngle,
        shadowBlur: config?.shadowBlur,
        shadowOpacity: config?.shadowColorAlpha,
        shadowColor: config?.shadowColor,
      }
    )
  }

  export function getOverlaysBoundary(
    overlays: Overlay[],
    playerWidth: number,
    playerHeight: number
  ) {
    const boundaryLeft = 0
    const boundaryTop = 0
    const boundaryRight = playerWidth
    const boundaryBottom = playerHeight

    const maxBoundaryOfOverlays = overlays.reduce((max, overlay) => {
      const right = overlay.left + overlay.width
      const bottom = overlay.top + overlay.height
      return {
        left: Math.min(max.left, overlay.left),
        top: Math.min(max.top, overlay.top),
        right: Math.max(max.right, right),
        bottom: Math.max(max.bottom, bottom)
      }
    }, {
      left: Infinity,
      top: Infinity,
      right: -Infinity,
      bottom: -Infinity
    })

    return {
      minX: boundaryLeft - maxBoundaryOfOverlays.left,
      minY: boundaryTop - maxBoundaryOfOverlays.top,
      maxX: boundaryRight - maxBoundaryOfOverlays.right,
      maxY: boundaryBottom - maxBoundaryOfOverlays.bottom
    }
  }
}
