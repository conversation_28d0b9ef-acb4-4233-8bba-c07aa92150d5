import { RenderTaskHook } from './useRenderTasks'
import { useUploadTasks } from './useUploadTasks'
import React from 'react'

export type TaskCenterValues = {
  uploading: ReturnType<typeof useUploadTasks>
  rendering: RenderTaskHook
}

export const TaskCenterContext = React.createContext<TaskCenterValues>({} as any)

export const useTaskCenter = () => {
  const ctx = React.useContext(TaskCenterContext)
  if (!ctx) {
    throw new Error('useTaskCenter must be used within a TaskCenterProvider')
  }
  return ctx
}
