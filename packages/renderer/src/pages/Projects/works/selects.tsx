/* eslint-disable @stylistic/indent */
import React, { useId, useMemo, useState } from 'react'
import { useQuerySystemDict } from '@/hooks/queries/useQuerySystemDict'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/ui/select'
import { useSearchParams } from 'react-router'
import { Button } from '@/components/ui/button'
import { XCircle } from 'lucide-react'
import { cn } from '@/components/lib/utils'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { CaretSortIcon } from '@radix-ui/react-icons'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'

function DictSelect({ label, value }: { label: string; value: string }) {
  const [searchParams, setSearchParams] = useSearchParams()
  const { data } = useQuerySystemDict(value)
  const tag = useMemo(() => value.split('_').at(-1) ?? label, [label])
  const selected = searchParams.get(tag) ?? ''

  function setSelected(value: string) {
    setSearchParams(prev => {
      prev.set(tag, value)
      return prev
    })
  }

  return (
    <div className="relative">
      <Select value={selected} onValueChange={setSelected} disabled={!data}>
        <SelectTrigger>
          <SelectValue placeholder={<span className="text-muted-foreground">{label}</span>} />
        </SelectTrigger>
        <SelectContent>
          {data?.map(item => (
            <SelectItem key={item.value} value={item.value}>
              {item.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <Button
        variant="ghost"
        size="icon"
        className={cn(
          'absolute top-1/2 -translate-y-1/2 right-[9px] z-10 size-6 hidden',
          selected && 'flex',
        )}
        onClick={e => {
          e.preventDefault()
          e.stopPropagation()
          setSearchParams(prev => {
            prev.delete(tag)
            return prev
          })
        }}
      >
        <XCircle className="size-4.5 fill-gray-300 text-background" />
      </Button>
    </div>
  )
}

function DistSelect() {
  const id = useId()
  const [open, setOpen] = useState(false)
  const { data } = useQuerySystemDict('project_work_select_distStatus')
  const [searchParams, setSearchParams] = useSearchParams()
  const dists = searchParams.get('dist')?.split(',') ?? []

  function setSelected(value: string) {
    setSearchParams(prev => {
      let newDists: string[] = []
      if (!dists.includes(value)) newDists = [...dists, value]
      else newDists = dists.filter(v => v !== value)
      if (!newDists.length) prev.delete('dist')
      else prev.set('dist', newDists.join(','))
      return prev
    })
  }

  const items: { label: string; value: string }[] = [
    { label: '未发布', value: '0' },
    { label: '已占用', value: '1' },
    { label: '已发布', value: '2' },
  ]

  function getName(dist: string) {
    const [type, status] = dist.split('-')
    const typeName = data?.find(v => v.value === type)?.label ?? ''
    const statusName = items.find(v => v.value === status)?.label ?? ''
    return `${typeName}-${statusName}`
  }

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            id="dates"
            className="w-full justify-between font-normal"
            onClick={e => e.preventDefault()}
            onMouseDown={() => setOpen(v => !v)}
          >
            {dists.length ? (
              <span className="flex-1 min-w-0 overflow-hidden flex justify-start">
                {dists.map(getName).join(' | ')}
              </span>
            ) : (
              <span className="text-muted-foreground">发布状态</span>
            )}
            <CaretSortIcon className="h-4 w-4 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="overflow-hidden p-2 flex flex-col gap-3" align="start">
          {data?.map(item => (
            <div key={item.value} className="flex flex-col gap-1">
              <p className="text-sm">{item.label}</p>
              <div className="flex justify-between">
                {items.map(({ label, value }) => (
                  <div className="flex items-center gap-2" key={value}>
                    <Checkbox
                      key={value}
                      checked={dists.includes(`${item.value}-${value}`)}
                      onCheckedChange={() => setSelected(`${item.value}-${value}`)}
                      id={`${id}${item.value}-${value}`}
                    />
                    <Label htmlFor={`${id}${item.value}-${value}`}>{label}</Label>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </PopoverContent>
      </Popover>
      <Button
        variant="ghost"
        size="icon"
        className={cn(
          'absolute top-1/2 -translate-y-1/2 right-[9px] z-10 size-6 hidden',
          dists.length && 'flex',
        )}
        onClick={e => {
          e.preventDefault()
          e.stopPropagation()
          setSearchParams(prev => {
            prev.delete('dist')
            return prev
          })
        }}
      >
        <XCircle className="size-4.5 fill-gray-300 text-background" />
      </Button>
    </div>
  )
}

export function Selects({ data }: { data?: { label: string; value: string }[] }) {
  return (
    <div className="w-full grid grid-cols-[repeat(auto-fit,minmax(0,1fr))] gap-2">
      {!data
        ? Array(6)
            .fill(0)
            .map((_, index) => (
              <div key={index} className="h-8 bg-muted rounded-md animate-pulse" />
            ))
        : data.map(v => <DictSelect key={v.label} {...v} />)}
      <DistSelect />
    </div>
  )
}
