import { useForm<PERSON>ontex<PERSON>, Controller } from 'react-hook-form'
import React from 'react'
import { MountSettingsSection } from '@/modules/matrix/components/MountSettingsSections'
import { DyCartInfoSection } from '@/modules/matrix/components/publish/dy/DyCartInfoSection'
import { DyLocationInfoSection } from '@/modules/matrix/components/publish/dy/DyLocationInfoSelector'
import { ScheduledPublishSection } from '@/modules/matrix/components/publish/shared/ScheduledPublishSection'
import { RadioGroupField } from '@/modules/matrix/components/publish/shared/RadioGroupField'
import { LoopPublishSection } from '@/modules/matrix/components/publish/shared/LoopPublishSection'
import { PublishData } from '@/modules/matrix/schemas/publish.schema'
import {  PublishTimeType, VideoMountMode } from '@/modules/matrix/enums/shared'
import { AccountProduct } from '@/modules/matrix/schemas/shared.schema'
import { modeOptions, settingOptions, timeTypeOptions } from '@/modules/matrix/constants/shared'
import { AccountSelector } from '@/modules/matrix/components/publish/shared/AccountSelector'
import { PlatformKey } from '@/libs/request/api/generic-matrix'
import { LoopTimeTypeTooltip } from '@/modules/matrix/components/publish/shared/LoopTimeTypeTooltip'
import { TitleManage } from '@/modules/matrix/components/publish/shared/TitleArrayField'

export default function DyForm() {
  const { control, formState: { errors }, watch, setValue } = useFormContext<PublishData>()

  const accountProducts =  watch('platforms').dy?.accountProducts || []

  const mountType = watch('platforms').dy?.mountType
  const timeType = watch('platforms').dy?.timeType

  return (
    <div className="space-y-6">

      {/* 动态标题 */}
      <TitleManage
        platform={PlatformKey.dy}
        control={control}
        namePrefix="platforms.dy.titles"
        errors={errors}
      />

      {/* 账号 */}
      <Controller
        control={control}
        name="platforms.dy.accountList"
        render={({ field: { value, onChange } }) => (
          <AccountSelector
            platform={PlatformKey.dy}
            value={value || []}
            onChange={val => {
              const updatedAccountProducts: AccountProduct[]  = val.map(v => {
                const existing = accountProducts.find(ap => ap.accountInfo.id === v.id)
                return existing || {
                  accountInfo: v,
                  products: [{ title: '', url: '' }] 
                }
              })

              setValue<any>('platforms.dy.accountProducts', updatedAccountProducts)

              onChange(val)
            }}
            errors={errors.platforms?.dy?.accountList?.message }
          />
        )}
      />
              
      {/* 挂载模式设置 */}
      <Controller
        control={control}
        name="platforms.dy.mountType"
        render={({ field: { value, onChange } }) => (
          <MountSettingsSection value={value} onChange={onChange} errors={errors.platforms?.dy?.mountType?.message} />
        )}
      />

      {/* 购物车信息 - 当挂载模式为购物车时显示 */}
      {mountType === VideoMountMode.CART && (
        <DyCartInfoSection />
      )}

      {/* 位置信息 - 当挂载模式为位置时显示 */}
      {mountType === VideoMountMode.LOCATION && (
        <DyLocationInfoSection />
      )}

      {/* 发布模式 */}
      <Controller
        control={control}
        name="platforms.dy.publishMode"
        render={({ field: { value, onChange } }) => (
          <RadioGroupField label="发布模式" options={modeOptions} value={value} onChange={val => onChange(Number(val))} error={errors.platforms?.dy?.publishMode?.message}  />
        )}
      />

      {/* 发布设置 */}
      <Controller
        control={control}
        name="platforms.dy.setting"
        render={({ field: { value, onChange } }) => (
          <RadioGroupField label="发布设置"
            options={settingOptions}
            value={value}
            onChange={val => onChange(Number(val))}
            error={errors.platforms?.dy?.publishMode?.message}
          />
        )} 
      />

      {/* 发布时间类型 */}
      <Controller
        control={control}
        name="platforms.dy.timeType"
        render={({ field: { value, onChange } }) => (
          <RadioGroupField
            label="发布时间类型"
            options={timeTypeOptions}
            value={value}
            onChange={val => onChange(Number(val))}
            error={errors.platforms?.dy?.timeType?.message}
            tailContent={val => val === PublishTimeType.LOOP && <LoopTimeTypeTooltip />}
          />
        )}
      />

      {/* 定时发布配置 - 当发布时间类型为定时时显示 */}
      {timeType === PublishTimeType.SCHEDULED && (
        <ScheduledPublishSection
          control={control}
          namePrefix="platforms.dy.timeSetting"
        />
      )}
      
      {/* 循环定时发布配置 - 当发布时间类型为循环时显示 */}
      {timeType === PublishTimeType.LOOP && (
        <LoopPublishSection
          control={control}
          namePrefix="platforms.dy.timeSetting"
        />
      )}
    </div>
  )
}
