import React, { useRef } from 'react'
import { Users, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { SelectAccountDialog, SelectAccountDialogRef } from '@/modules/matrix/components/publish/shared/SelectAccountDialog'
import { AccountForm } from '@/modules/matrix/schemas/shared.schema'
import { cn } from '@/components/lib/utils'
import { PlatformKey } from '@/libs/request/api/generic-matrix'

interface AccountSelectorProps {
  value: AccountForm[]
  onChange: (accounts: AccountForm[]) => void
  platform: PlatformKey
  errors?: string
}

export const AccountSelector: React.FC<AccountSelectorProps> = ({
  value,
  onChange,
  platform,
  errors
}) => {
  const selectAccountDialogRef = useRef<SelectAccountDialogRef>(null)

  // 移除单个账号
  const handleRemoveAccount = (accountId: number) => {
    const updatedAccounts = value.filter(account => account.id !== accountId)
    onChange(updatedAccounts)
  }

  return (
    <div className="space-y-4">
      <div className="space-y-3">
        <Label className="flex items-center gap-2">
          <Users className="w-4 h-4" />
          选择账号 <span className="text-red-500">*</span>
          <div className="text-xs text-neutral-400">选择的发布账号数不能大于视频数</div>

        </Label>
        <div className={cn('border py-3 px-4 rounded flex flex-col gap-2', errors ? 'border-red-500' : 'border-border')}>
          <div className="flex items-center gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => selectAccountDialogRef.current?.open({
                selectedAccountIds: value.map(account => account.id)
              })}
              className="flex items-center gap-2"
            >
              <Users className="w-4 h-4" />
              选择账号
              {value.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {value.length}
                </Badge>
              )}
            </Button>
            {value.length > 0 && (
              <span className="text-sm text-muted-foreground">
                发布账号个数： {value.length} 个
              </span>
            )}
          </div>
          {value.length > 0 && (
            <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
              {value.map(account => (
                <Badge
                  key={account.id}
                  variant="outline"
                  className="flex items-center gap-1 px-3 py-2"
                >
                  {account.nickname || '-'}
                  <X
                    className="w-3 h-3 cursor-pointer hover:text-red-500"
                    onClick={() => handleRemoveAccount(account.id)}
                  />
                </Badge>
              ))}
            </div>
          )}
        </div>
        {errors && <p className="text-sm text-red-500">{errors}</p>}
      </div>

      {/* 账号选择对话框 */}
      <SelectAccountDialog
        ref={selectAccountDialogRef}
        onConfirm={onChange}
        platform={platform}
      />
    </div>
  )
}
