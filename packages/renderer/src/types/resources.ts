import { PaginationParams } from '@app/shared/infra/request'

enum CoverType {
  PHOTO = 'photo',
}

interface BaseResource {
  id: number
  authorName: string
  title: string
  cover: Cover | null
  contentType: string
  createTime: number
  tags: string[]
  top: number
  featured: number
  auditState: number
  subCategoryId?: number
  version: string
}

interface Cover {
  type: CoverType
  url: string
}

export type CommonCategory = {
  id: string
  name: string
  num?: number
  sort?: number
}

export interface BaseResourceQueryParams extends PaginationParams {
  categoryIds?: string[]
  keyword?: string

  autoLoadAllPages?: boolean
}

export interface InteractInfo {
  collected: boolean
  collectedCount: any
  refCount: any
}

//上传本地资源：我的贴纸/我的音效/我的音乐
export interface UploadLocal {
  folderUuid: string
  title: string
  fileMd5: string
  contentType: string
  objectId: string
}

export enum ResourceSource {
  MEDIA = 'media',
  FOLDER = 'folder',
  MULTI_SELECT = 'multi_select',

  LOCAL_STICK = 'local_stick',
  LOCAL_STICK_FOLDER = 'local_stick_folder',
  LOCAL_STICK_MULTI_SELECT = 'local_stick_multi_select',

  LOCAL_MUSIC = 'local_music',
  LOCAL_MUSIC_FOLDER = 'local_music_folder',
  LOCAL_MUSIC_MULTI_SELECT = 'local_music_multi_select',

  LOCAL_SOUND = 'local_sound',
  LOCAL_SOUND_FOLDER = 'local_sound_folder',
  LOCAL_SOUND_MULTI_SELECT = 'local_sound_multi_select',

  DIR_RECYCLE = 'dir_recycle',
  MEDIA_RECYCLE = 'media_recycle',
}

export const RESOURCE_ACTIONS = {
  CREATE: 'create',
  RENAME: 'rename',
  DELETE: 'delete',
  MOVE: 'move',
  RECYCLE: 'recycle',
  BACK: 'back',
} as const

export type ResourceAction = (typeof RESOURCE_ACTIONS)[keyof typeof RESOURCE_ACTIONS]

export type MultiAction =
  | typeof RESOURCE_ACTIONS.MOVE
  | typeof RESOURCE_ACTIONS.RECYCLE
  | typeof RESOURCE_ACTIONS.DELETE
  | typeof RESOURCE_ACTIONS.BACK

export const FolderActionKeys = {
  ...RESOURCE_ACTIONS,
  DETAILS: 'details',
  COMPLETELY_DELETE: 'completely_delete',
}

export const actionLabels = {
  [FolderActionKeys.RENAME]: '重命名',
  [FolderActionKeys.MOVE]: '移动到',
  [FolderActionKeys.DELETE]: '删除',
  [FolderActionKeys.CREATE]: '新建文件夹',
  [FolderActionKeys.DETAILS]: '详细信息',
  [FolderActionKeys.BACK]: '放回原处',
  [FolderActionKeys.COMPLETELY_DELETE]: '彻底删除',
  [FolderActionKeys.RECYCLE]: '放入回收站',
}

// 素材的操作列表类型（回收站）
export type RecycleModeParams = {
  isLocal: boolean
  type: ResourceSource
  isRecycle: true
}

// 素材的操作列表类型（普通模式）
export type NormalModeParams = {
  isLocal: boolean
  type: ResourceSource
  setMoveType: (type: ResourceSource) => void
  setMoveId: (id: string) => void
  setMoveDialogOpen: (open: boolean) => void
}

//#region ~ 团队共享的资源类型，包括贴纸、音乐、音效

/**
 * 团队共享资源的元数据
 */
export type TeamSharedResourceMeta = {
  fileId: string
  folderUuid: string
}

export namespace PasterResource {
  export interface Paster extends BaseResource {
    content: PasterContent
    interactInfo: InteractInfo
  }

  export interface PasterContent {
    fileUrl: string // 完整资源URL，用于编辑器中实际使用
    thumbUrl: string // 动态缩略图URL，用于悬停预览
    width: number
    height: number
  }

  // 三层加载状态枚举
  export enum LoadingLayer {
    COVER = 'cover', // 第一层：cover.url 静态图片
    THUMB = 'thumb', // 第二层：thumbUrl 动态缩略图
    FILE = 'file', // 第三层：fileUrl 完整资源
  }

  // 贴纸加载状态接口
  export interface StickerLoadingState {
    coverId: string | number // 贴纸ID
    coverLoaded: boolean // cover.url 是否已加载
    thumbLoaded: boolean // thumbUrl 是否已加载
    thumbLoading: boolean // thumbUrl 是否正在加载
    fileLoaded: boolean // fileUrl 是否已缓存
    fileLoading: boolean // fileUrl 是否正在下载
    currentLayer: LoadingLayer // 当前显示的层级
  }

  // 创建本地资源
  export interface SharedPaster extends Paster, TeamSharedResourceMeta {
    /**
     * TODO: 能否移动到 `TeamSharedResourceMeta` 中？
     */
    fileName: string
  }
}

export namespace SoundResource {
  export interface Sound extends BaseResource {
    content: SoundContent
    interactInfo: InteractInfo
  }

  export interface SoundContent {
    itemUrl: string
    durationMsec: number
  }

  export interface SharedSound extends Sound, TeamSharedResourceMeta {
  }
}

export type TeamSharedResources = PasterResource.SharedPaster | SoundResource.SharedSound
//#endregion

export namespace FontResource {
  export interface BubbleLetters extends BaseResource {
    content: BubbleContent
    interactInfo: InteractInfo
  }

  export interface BubbleContent {
    name: string
    coverUrl: string
    itemUrl?: string
    zipUrl?: string
    zipV3Url?: string
    content?: string
  }

  export interface Font extends BaseResource {
    content: FontContent
    interactInfo: InteractInfo
  }

  export interface FontContent {
    name: string
    url: string
    textColor: string
    borderColor: any
    borderWidth: number
  }
}

export namespace StyledTextResource {
  export interface StyledText extends BaseResource {
    content: StyledTextContext
    interactInfo: InteractInfo
  }

  export interface StyledTextContext {
    fontName: string
    fontPath: string
    textColor: string
    borderColor: string
    borderWidth: number
    bold: boolean
    flag: number
    italic: boolean
    itemUrl: string
    alignment: number
    cateLevel: number
    fontSize: number
    presetId: number
    underline: boolean
    textAlpha: number
    lineSpacing: number
    shadowBlur: number
    textureUrl: string
    jyEffectId: string
    shadowAngle: number
    shadowColor: string
    letterSpacing: number
    jyResourceId: string
    shadowDistance: number
    underlineWidth: number
    backgroundAlpha: number
    backgroundColor: string
    underlineOffset: number
    flowerZipV3Url: string
    shadowColorAlpha: number
  }
}

export namespace MaterialResource {
  //素材文件夹
  export interface MaterialDirectoryParams {
    projectId: number | undefined
    keyword?: string
  }

  export interface Directory {
    folderId: string
    folderName: string
    parentId: string | undefined
    icons: string | null
    folderCount: number
    videoCount: number
    audioCount: number
    imageCount: number
    fileSize: number | null
    createdAt: number | undefined
    updatedAt: number | null
    // 本地
    resourcesCount: number
    // 回收站
    recycleExpiredAt?: number
  }

  // 素材媒体资源文件
  export enum MediaType {
    FOLDER = 0,
    VIDEO = 1,
    AUDIO = 2,
    IMAGE = 3,
  }

  export enum SortField {
    UPLOAD_TIME = 'uploadTime',
    FILE_SIZE = 'fileSize',
    DURATION = 'duration',
    FILE_NAME = 'fileName',
    QUOTE_COUNT = 'quoteCount',
  }

  export const SortFieldLabelMap: Record<SortField, string> = {
    [SortField.UPLOAD_TIME]: '上传时间',
    [SortField.FILE_SIZE]: '文件大小',
    [SortField.DURATION]: '媒体时长',
    [SortField.FILE_NAME]: '名称',
    [SortField.QUOTE_COUNT]: '引用次数'
  }

  export enum SortOrder {
    ASC = 'asc',
    DESC = 'desc',
  }

  export interface SortDirectionOption {
    label: string
    order: SortOrder
  }

  export interface SortOption {
    label: string
    field: SortField
    directions: SortDirectionOption[]
  }

  export enum MediaStyle {
    HORIZONTAL = 'horizontal',
    VERTICAL = 'vertical',
  }

  export interface MaterialMediaParams extends PaginationParams {
    projectId: number
    folderUuid: string
    sortField: SortField // 排序字段
    sortOrder: SortOrder // 排序方向
    createAtRange?: string // 创建时间范围
    durationRange?: string // 时长范围
    useCountRange?: number | undefined // 合成次数
    quoteCountRange?: number | undefined // 引用次数
    keyword?: string | undefined
    resType?: MediaType
  }

  export interface Media {
    codecs?: string
    cover?: string
    coverFrame?: string
    createTime?: number
    updatedTime?: number
    dar?: null
    duration?: number
    fileId: string
    fileName: string
    fileSize?: number
    folderUuid: string
    hdrSetting?: number
    height?: number
    lnp?: number
    lnPath?: null
    projectId?: number
    quoteCount?: number
    reason?: null
    resType?: MediaType
    rotate?: number
    sceneTags?: null
    sceneType?: number
    segment?: null
    status?: number
    tags?: null
    taskNo?: null
    trackFrame?: null
    url: string
    useCount?: number
    width?: number
    // 文件夹
    childrenFolder?: number
    mediaNum?: number
    // 回收站
    recycleExpiredAt?: number
  }
}

export namespace TimbreResource {
  export interface Timbre extends BaseResource {
    content: TimbreContent
    interactInfo: InteractInfo
  }

  export interface TimbreContent {
    /**
     * 试听音频地址
     */
    url: string
    voiceId: number
  }
}
