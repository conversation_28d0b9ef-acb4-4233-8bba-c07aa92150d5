import React from 'react'
import { VideoOverlay } from '@app/shared/types/overlay'
import { MediaFilterPresetSelector } from '@/modules/video-editor/components/common/media-filter-preset-selector'
import { useOverlayEditing } from '@/modules/video-editor/contexts'
import { FormSlider } from '../../../common/form-components'

export const VideoStylePanel = () => {
  const { localOverlay: videoOverlay, updateEditingOverlay } = useOverlayEditing<VideoOverlay>()

  return (
    <div className="space-y-6">
      {/* Appearance Settings */}
      <div className="space-y-4 rounded-md bg-background  p-4 border border-border ">
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          样式
        </h3>

        {/* <div className="space-y-2">
          <Label >
            适配
          </Label>

          <Select
            value={videoOverlay?.styles?.objectFit ?? 'cover'}
            onValueChange={e => updateEditingOverlay({ styles: { objectFit: e as any } }, true)}
          >
            <SelectTrigger className="w-64">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="free">自由缩放</SelectItem>
              <SelectItem value="cover">裁剪填充（保持比例）</SelectItem>
              <SelectItem value="contain">完整显示（保持比例）</SelectItem>
              <SelectItem value="fill">拉伸填充（不保持比例）</SelectItem>
            </SelectContent>
          </Select>
        </div> */}

        {/* Filter Preset Selector */}
        <MediaFilterPresetSelector />

        {/* Border Radius */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-xs text-gray-600 dark:text-gray-400">
              圆角
            </label>
            <span className="text-xs text-gray-600 dark:text-gray-400 min-w-[40px] text-right">
              {videoOverlay?.styles?.borderRadius ?? '0px'}
            </span>
          </div>

          <FormSlider
            value={parseInt(videoOverlay?.styles?.borderRadius ?? '0')}
            onChange={(val, commit) => updateEditingOverlay({ styles: { borderRadius: `${val}px` } }, commit)}
            min={0}
            max={100}
            step={1}
            showInput={false}
          />

        </div>

      </div>
    </div>
  )
}
