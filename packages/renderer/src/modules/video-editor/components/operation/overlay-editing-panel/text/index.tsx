import React, { useCallback, useState } from 'react'
import { SettingsTabs, TabItem } from '../../../shared/settings-tabs'
import { TextAnimation } from './text-animation'
import { TextBaseStyle } from './text-base-style'
import { StyledText } from './styled-text'
import { useEditorContext, useOverlayEditing } from '@/modules/video-editor/contexts'
import { OverlayType, TextOverlay } from '@app/shared/types/overlay'
import { TextSettingContext } from './context'
import { cacheManager } from '@/libs/cache/cache-manager'

import { calculateTextRenderInfo, TextPropOverrides } from '@clipnest/overlay-renderer'
import opentype from 'opentype.js'
import { TrackType } from '@/modules/video-editor/types'
import _ from 'lodash'
import { DeepPartial } from '@/types/utils'
import { findTrackByOverlay } from '@/modules/video-editor/utils/overlay-helper'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'

const TABS: TabItem[] = [
  {
    value: 'basic',
    label: '基础',
    content: <TextBaseStyle />
  },
  {
    value: 'styled',
    label: '花体字',
    content: <StyledText />
  },
  // {
  //   value: 'bubble',
  //   label: '气泡字',
  //   content: <BubbleTextPanel />
  // },
  {
    value: 'animation',
    label: '动画',
    content: <TextAnimation />
  },
]

function convertUpdateToOverride(update: DeepPartial<TextOverlay>): TextPropOverrides | null {
  const override: TextPropOverrides = {
    content: update.content,
    width: update.width,
    fontSize: update.styles?.fontSize,
    letterSpacing: update.styles?.letterSpacing,
    lineSpacing: update.styles?.lineSpacing,
  }

  if (Object.values(override).every(o => o === undefined)) return null

  return Object.fromEntries(
    Object
      .entries(override)
      .filter(([, value]) => value !== undefined)
  )
}

function buildUpdateForSingleTextOverlay(
  overlay: TextOverlay,
  font: opentype.Font,
  update: DeepPartial<TextOverlay>,
) {
  const override = convertUpdateToOverride(update)

  if (!override) return update

  const renderInfo = calculateTextRenderInfo(font, overlay, override)
  const { minHeight, minWidth = 0 } = renderInfo
  const {
    content = overlay.content,
    fontSize = overlay.styles.fontSize,
    lineSpacing = overlay.styles?.lineSpacing,
    letterSpacing = overlay.styles?.letterSpacing,
  } = override

  const src = update.src || overlay.src

  return {
    ...update,
    content,
    src,
    width: Math.max(overlay.width, minWidth),
    height: Math.max(minHeight, overlay.height),
    styles: {
      ...overlay.styles,
      ...update.styles,
      fontSize,
      lineSpacing,
      letterSpacing
    }
  }
}

export const TextEditing: React.FC = () => {
  const { localOverlay, updateEditingOverlay } = useOverlayEditing<TextOverlay>()
  const { tracks, updateTracks } = useEditorContext()

  const [syncChanges, setSyncChanges] = useState(false)

  const requestUpdateText = useCallback(async (
    update: DeepPartial<TextOverlay>,
    commit = false,
  ) => {
    if (!localOverlay) return

    const font = await cacheManager.font.cacheFont(update.src || localOverlay.src)
    if (!font) return

    update.localSrc = cacheManager.font.getFontLocalUrlSync(update.src || localOverlay.src) || undefined

    if (!syncChanges || !commit) {
      return updateEditingOverlay(buildUpdateForSingleTextOverlay(localOverlay, font, update), commit)
    }

    return updateTracks(prevTracks => {
      return prevTracks.map((track, _index) => {
        if (track.type !== TrackType.NARRATION) return track

        return {
          ...track,
          overlays: track.overlays.map(overlay => {
            if (overlay.type !== OverlayType.TEXT) return overlay
            return _.merge({}, overlay, buildUpdateForSingleTextOverlay(overlay, font, update))
          })
        }
      })
    })
  }, [localOverlay, syncChanges, updateEditingOverlay])

  return (
    <TextSettingContext
      value={{
        textOverlay: localOverlay,
        requestUpdateText
      }}
    >
      {findTrackByOverlay(tracks, localOverlay.id)?.type === TrackType.NARRATION && (
        <div className="flex items-center gap-2 mb-3">
          <Checkbox
            id="sync-change"
            checked={syncChanges}
            onCheckedChange={v => setSyncChanges(!!v)}
          />
          <Label htmlFor="sync-change" className="text-xs">
            将修改应用到口播轨道的所有字幕
          </Label>
        </div>
      )}

      <div className="space-y-3">
        <SettingsTabs tabs={TABS} defaultTab="basic" />
      </div>
    </TextSettingContext>
  )
}
