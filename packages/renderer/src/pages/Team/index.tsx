import React, { useMemo, useState } from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import {
  useQueryCurrentTeam,
  useQueryTeamMemberByID,
  useQueryTeamMemberList,
  useQueryTeamRoles,
} from '@/hooks/queries/useQueryTeam'
import { TeamAPI, TeamPermission } from '@/libs/request/api/team'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { TokenManager } from '@/libs/storage'
import { toast } from 'react-toastify'
import { CheckboxHeader, CheckboxItem, CheckboxProvider, useCheckboxContext } from '@/components/checkbox'
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useModalMemberPermission, MemberPermissionMode } from '@/pages/Team/modal-member-permission'
import { useCreateItemModal } from '@/components/material/modal'
import { useTeam } from '@/hooks/useTeam'
import { ROLES, TEAM_ACTIONS } from '@/types/team'
import { WithConfirm } from '@/components/WithConfirm'
import { TipModal } from '@/components/tipDialog'
import { PenLine } from 'lucide-react'
import PermissionDialog from '@/pages/Team/PermissionDialog'
import { SearchInput } from '@/components/ui/search-input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

export default function TeamPageWrapper() {
  return (
    <CheckboxProvider>
      <TeamPage />
    </CheckboxProvider>
  )
}
const inputs = [
  { placeholder: '账号昵称', field: 'nickname' },
  { placeholder: '手机号', field: 'mobile' },
  { placeholder: '备注', field: 'remark' },
]
function TeamPage() {
  const { data: team } = useQueryCurrentTeam()
  const { data: members } = useQueryTeamMemberList('')
  const { data: member } = useQueryTeamMemberByID(TokenManager.getUserId())
  const { data: roles, refetch } = useQueryTeamRoles()
  const openMemberModal = useModalMemberPermission()
  const { selected, all } = useCheckboxContext()
  const [newTeamId, setNewTeamId] = useState<number | null>(null)
  const [showJoinSuccessModal, setShowJoinSuccessModal] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const { joinTeam, switchTeam } = useTeam()
  const joinTeamModal = useCreateItemModal<TEAM_ACTIONS>(async (_type, data) => {
    const teamId = await joinTeam(data.title, false)
    if (!teamId) return
    setNewTeamId(teamId)
    setShowJoinSuccessModal(true)
  })

  const [filters, setFilters] = useState({
    nickname: '',
    mobile: '',
    remark: '',
    role: '',
  })

  const isOwner = member?.roles?.some(role => role.code === ROLES.OWNER)
  const isViewer = member?.roles?.some(role => role.code === ROLES.VIEWER)
  const editable = useMemo(() => {
    if (!member) return false
    return member.roles?.some(role => {
      return ['owner', 'admin'].includes(role.code)
    })
  }, [member?.roles, roles])

  function SwitchItem({
    checked,
    field,
    memberId,
  }: {
    checked: boolean
    field: keyof TeamPermission
    memberId: number
  }) {
    return (
      <TableCell>
        <Switch
          disabled={!editable}
          checked={checked}
          onCheckedChange={async (checked: boolean) => {
            await TeamAPI.setMemberPermission({
              memberId,
              type: field,
              status: !checked,
            })
            refetch()
          }}
        />
      </TableCell>
    )
  }

  function RemarkCell({ member, onUpdateRemark }) {
    const [editing, setEditing] = useState(false)
    const [value, setValue] = useState(member.remark || '')

    const save = async () => {
      setEditing(false)
      if (value !== member.remark) {
        await onUpdateRemark(value)
      }
    }

    return (
      <TableCell>
        {editing ? (
          <input
            autoFocus
            className="border rounded px-2 py-1 w-full"
            value={value}
            onChange={e => setValue(e.target.value)}
            onBlur={save}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                e.preventDefault()
                save()
              }
            }}
          />
        ) : (
          <div className="flex items-center gap-2">
            <span>{member.remark}</span>
            <PenLine
              className="w-4 h-4 cursor-pointer"
              onClick={e => {
                e.stopPropagation()
                setEditing(true)
              }}
            />
          </div>
        )}
      </TableCell>
    )
  }

  async function deleteMember(memberId?: number | number[]) {
    if (!team || !memberId) return

    try {
      // TODO: 删除成员接口未提供
      const userIds = Array.isArray(memberId) ? memberId : [memberId]
      await TeamAPI.removeMember({ teamId: team.id, userId: userIds })
      toast.success('成员已删除')
      refetch() // 重新拉取成员列表
    } catch (error) {
      toast.error('删除失败，请稍后重试')
    }
  }

  return (
    <div className="flex flex-col items-stretch h-full px-4">
      <div className="py-4 w-full flex items-center justify-between">
        <div className="flex flex-col gap-2">
          {team ? <h4 className="text-lg font-bold">{team.name}</h4> : <Skeleton className="h-8 w-48" />}
          <div>
            <div className="flex gap-1">
              {inputs.map(({ placeholder, field }) => (
                <SearchInput
                  key={field}
                  placeholder={placeholder}
                  value={filters[field] ?? ''}
                  onChange={e =>
                    setFilters(prev => ({
                      ...prev,
                      [field]: e.target.value,
                    }))}
                />
              ))}
              <Select
                value={filters.role}
                onValueChange={value => setFilters(prev => ({ ...prev, role: value }))}
              >
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="选择角色" />
                </SelectTrigger>
                <SelectContent>
                  {roles?.map(role => (
                    <SelectItem key={role.id} value={role.code}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <div>
          <div className="flex justify-end gap-4 mb-3">
            <Button
              className="bg-gradient-brand hover:opacity-90 mr-[2px]"
              onClick={() =>
                joinTeamModal(TEAM_ACTIONS.JOIN, {
                  label: '通过输入对方团队邀请码加入该团队，可咨询团队管理员获取',
                  headerTitle: '加入新团队',
                  placeholder: '请输入团队邀请码',
                })}
            >
              加入团队
            </Button>

            {!isViewer && (
              <Button className="bg-gradient-brand hover:opacity-90 mr-[1px]" onClick={() => openMemberModal(MemberPermissionMode.INVITE)}>
                邀请成员
              </Button>
            )}
          </div>
          {isOwner && (
            <div className="flex items-center gap-4">
              {members && (
                <div className="text-sm text-muted-foreground/70">
                  已选择：{Object.keys(selected).length}/{Object.keys(all).length}
                </div>
              )}

              <Button variant="outline" onClick={() => setDialogOpen(true)}>
                权限设置
              </Button>

              <WithConfirm
                asChild={true}
                title="删除成员"
                description="删除成员后，该成员创建的内容仍将保留"
                confirmVariant="destructive"
                onConfirm={() => deleteMember(Object.keys(selected).map(Number))}
              >
                <Button
                  variant="outline"
                  onClick={e => {
                    if (Object.keys(selected).length === 0) {
                      e.preventDefault()
                      toast.error('请先选择一个成员')
                    }
                  }}
                >
                  删除成员
                </Button>
              </WithConfirm>
            </div>
          )}
        </div>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>
              <CheckboxHeader />
            </TableHead>
            <TableHead>姓名</TableHead>
            <TableHead>手机号</TableHead>
            <TableHead>备注</TableHead>
            <TableHead>角色</TableHead>
            <TableHead>混剪权限</TableHead>
            <TableHead>发布权限</TableHead>
            <TableHead>抖音评论权限</TableHead>
            <TableHead>TikTok 评论权限</TableHead>
            <TableHead>私信管理权限</TableHead>
            <TableHead>操作</TableHead>
          </TableRow>
        </TableHeader>
        {members ? (
          <TableBody>
            {members.map(member => (
              <TableRow key={member.id}>
                <TableCell>{member.id !== TokenManager.getUserId() && <CheckboxItem value={member.id} />}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">{member.nickname}</span>
                    {member.id === TokenManager.getUserId() && (
                      <Badge className="ml-2" variant="secondary">
                        我
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>{member.mobile}</TableCell>
                <RemarkCell
                  member={member}
                  onUpdateRemark={async (newRemark: string) => {
                    try {
                      console.log(newRemark)
                      if (!editable) return toast.error('没有权限')
                      // TODO: 修改备注接口未提供
                      // await TeamAPI.updateMemberRemark({
                      //   memberId: member.id,
                      //   remark: newRemark,
                      // })
                      toast.success('备注已更新')
                      refetch()
                    } catch (err) {
                      toast.error('更新备注失败，请稍后重试')
                    }
                  }}
                />
                <TableCell>
                  {member.roles?.map(role => (
                    <Badge key={role.id} className="mr-1" variant="outline">
                      {role.name}
                    </Badge>
                  ))}
                </TableCell>
                <SwitchItem checked={member.isEditVideo} field="isEditVideo" memberId={member.id} />
                <SwitchItem checked={member.isPublishVideo} field="isPublishVideo" memberId={member.id} />
                <SwitchItem checked={member.isComment} field="isComment" memberId={member.id} />
                <SwitchItem checked={member.isPrivateMsg} field="isPrivateMsg" memberId={member.id} />
                <SwitchItem checked={member.isTiktokComment} field="isTiktokComment" memberId={member.id} />
                <TableCell>
                  {editable && (
                    <>
                      <Button variant="link" onClick={() => openMemberModal(MemberPermissionMode.EDIT, member)}>
                        编辑
                      </Button>
                      <WithConfirm
                        asChild={true}
                        title="删除成员"
                        description="删除成员后，该成员创建的内容仍将保留"
                        confirmVariant="destructive"
                        onConfirm={() => deleteMember(member.id)}
                      >
                        <Button variant="link">删除</Button>
                      </WithConfirm>
                      <Button variant="link" onClick={() => setDialogOpen(true)}>
                        权限设置
                      </Button>
                    </>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <TableCaption>
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-blue-500" />
          </TableCaption>
        )}
      </Table>
      {/* 加入团队成功弹窗 */}
      {showJoinSuccessModal && newTeamId && (
        <TipModal
          open={showJoinSuccessModal}
          title="加入成功"
          description="欢迎加入，开启您的创作之旅吧"
          confirmText="现在切换"
          onConfirm={() => {
            switchTeam(newTeamId)
            setShowJoinSuccessModal(false)
          }}
          onCancel={() => setShowJoinSuccessModal(false)}
        />
      )}

      {/* 权限弹窗 */}
      <PermissionDialog open={dialogOpen} onOpenChange={setDialogOpen} />
    </div>
  )
}
