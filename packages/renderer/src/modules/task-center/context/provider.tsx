import React from 'react'
import { TaskCenterContext } from './context'
import { useUploadTasks } from './useUploadTasks'
import { useRenderTasks } from './useRenderTasks'

export const UploadTasksProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const uploading = useUploadTasks()
  const rendering = useRenderTasks()

  return (
    <TaskCenterContext.Provider
      value={{
        uploading,
        rendering
      }}
    >
      {children}
    </TaskCenterContext.Provider>
  )
}
