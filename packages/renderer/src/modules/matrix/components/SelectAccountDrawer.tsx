import React, { useState, useMemo, forwardRef, useImperativeHandle } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer'
import { DataTable } from '@/components/ui/data-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { SearchIcon, X } from 'lucide-react'
import { usePagination } from '@/hooks/usePagination'
import { useDebounceValue } from '@/hooks/useDebounceValue'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { GenericMatrixModule } from '@/libs/request/api/generic-matrix'
import { Account, AccountSearchParams } from '@/modules/matrix/types/shared'
import { usePlatform } from '../context/context'
import { accountAuthStatusMap } from '../constants/shared'

// 组件 ref 接口
export interface SelectAccountDrawerRef {
  open: (options?: {
    selectedAccountIds?: number[]
    title?: string
    description?: string
    maxSelection?: number
    groupId?: number
  }) => void
  close: () => void
}

interface SelectAccountDrawerProps {
  onConfirm: (selectedAccounts: Account[]) => void
}

export const SelectAccountDrawer = forwardRef<SelectAccountDrawerRef, SelectAccountDrawerProps>(
  ({ onConfirm }, ref) => {
    const [open, setOpen] = useState(false)
    const [searchTerm, setSearchTerm] = useState('')
    const [provinceFilter, setProvinceFilter] = useState<string>('')
    const [internalSelectedIds, setInternalSelectedIds] = useState<number[]>([])
    const [title, setTitle] = useState('添加账号')
    const [description, setDescription] = useState('请选择要添加到分组的账号')
    const [maxSelection, setMaxSelection] = useState<number | undefined>(undefined)
    const [_groupId, setGroupId] = useState<number | undefined>(undefined)
    const { currentPlatform } = usePlatform()

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      open: (options = {}) => {
        setInternalSelectedIds(options.selectedAccountIds || [])
        setTitle(options.title || '添加账号')
        setDescription(options.description || '请选择要添加到分组的账号')
        setMaxSelection(options.maxSelection)
        setGroupId(options.groupId)
        setOpen(true)
      },
      close: () => {
        setOpen(false)
        // 重置状态
        setSearchTerm('')
        setProvinceFilter('')
        setInternalSelectedIds([])
      }
    }), [])

    // 搜索防抖
    const debouncedSearchTerm = useDebounceValue(searchTerm, 500)

    const searchParams = useMemo(() => ({
      accessTokenStatus: null,
    }), [provinceFilter])

    const {
      data: accounts,
      pagination,
      isLoading,
      isError,
      setPagination,
    } = usePagination<Account, AccountSearchParams>({
      queryKey: [QUERY_KEYS.AUTH_ACCOUNT_LIST, currentPlatform],
      queryFn: data => GenericMatrixModule.endpoints.accountList(currentPlatform, data),
      searchParams,
      initialPageSize: 20,
      enabled: open,
    })

    const filteredAccounts = useMemo(() => {
      if (!debouncedSearchTerm) return accounts
      return accounts.filter(account =>
        account.nickname?.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
      account.orgNickname?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
      )
    }, [accounts, debouncedSearchTerm])

    // 处理选择
    const handleSelectAccount = (accountId: number, selected: boolean) => {
      setInternalSelectedIds(prev => {
        if (selected) {
          if (maxSelection && prev.length >= maxSelection) {
            return prev
          }
          return [...prev, accountId]
        } else {
          return prev.filter(id => id !== accountId)
        }
      })
    }

    // 处理全选
    const handleSelectAll = (selected: boolean) => {
      if (selected) {
        const newIds = filteredAccounts
          .slice(0, maxSelection ? maxSelection - internalSelectedIds.length : undefined)
          .map(account => account.id!)
          .filter(id => !internalSelectedIds.includes(id))
        setInternalSelectedIds(prev => [...prev, ...newIds])
      } else {
        const currentPageIds = filteredAccounts.map(account => account.id!)
        setInternalSelectedIds(prev => prev.filter(id => !currentPageIds.includes(id)))
      }
    }

    // 确认选择
    const handleConfirm = () => {
      const selectedAccounts = accounts.filter(account =>
        internalSelectedIds.includes(account.id!)
      )
      onConfirm(selectedAccounts)
      setOpen(false)
    }

    // 重置选择
    const handleReset = () => {
      setInternalSelectedIds([])
      setSearchTerm('')
      setProvinceFilter('')
    }

    // 表格列定义
    const columns: ColumnDef<Account>[] = [
      {
        id: 'select',
        header: () => {
          const currentPageIds = filteredAccounts.map(account => account.id!)
          const selectedInCurrentPage = currentPageIds.filter(id => internalSelectedIds.includes(id))
          const isAllSelected = currentPageIds.length > 0 && selectedInCurrentPage.length === currentPageIds.length
          const isIndeterminate = selectedInCurrentPage.length > 0 && selectedInCurrentPage.length < currentPageIds.length

          return (
            <input
              type="checkbox"
              checked={isAllSelected}
              ref={el => {
                if (el) el.indeterminate = isIndeterminate
              }}
              onChange={e => handleSelectAll(e.target.checked)}
              className="rounded border-gray-300"
            />
          )
        },
        cell: ({ row }) => {
          const account = row.original
          const isSelected = internalSelectedIds.includes(account.id!)
          const isDisabled = !isSelected && maxSelection && internalSelectedIds.length >= maxSelection

          return (
            <input
              type="checkbox"
              checked={isSelected}
              disabled={!!isDisabled}
              onChange={e => handleSelectAccount(account.id!, e.target.checked)}
              className="rounded border-gray-300 disabled:opacity-50"
            />
          )
        },
        size: 50,
      },
      {
        id: 'avatar',
        header: '头像',
        cell: ({ row }) => {
          const account = row.original
          return (
            <div className="flex items-center space-x-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={account.avatar} />
                <AvatarFallback>
                  {account.nickname?.charAt(0).toUpperCase() || account.orgNickname?.charAt(0).toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
            </div>
          )
        },
        size: 80,
      },

      {
        accessorKey: 'nickname',
        header: '昵称',
        cell: ({ row }) => {
          const account = row.original
          return (
            <span className="font-medium">
              {account.nickname || account.orgNickname || '未知账户'}
            </span>
          )
        },
      },
      {
        accessorKey: 'fanNum',
        header: '粉丝数',
        cell: ({ row }) => {
          const fanNum = row.original.fanNum
          return fanNum ? fanNum : '-'
        },
        size: 100,
      },
      {
        accessorKey: 'likeNum',
        header: '点赞数',
        cell: ({ row }) => {
          const likeNum = row.original.likeNum
          return likeNum ? likeNum : '-'
        },
        size: 100,
      },
      {
        accessorKey: 'publishNum',
        header: '分享数',
        cell: ({ row }) => {
          const publishNum = row.original.publishNum
          return publishNum ? publishNum : '-'
        },
        size: 100,
      },
      {
        accessorKey: 'accessTokenStatus',
        header: '授权状态',
        cell: ({ row }) => {
          const status = row.original.accessTokenStatus || 0
          const statusInfo = accountAuthStatusMap[status] || { label: '未知', variant: 'secondary' as const }
          return (
            <Badge variant={statusInfo.variant}>
              {statusInfo.label}
            </Badge>
          )
        },
        size: 100,
      },
    ]

    return (
      <Drawer
        direction="right"
        open={open}
        onOpenChange={setOpen}
      >
        <DrawerContent className="max-h-screen data-[vaul-drawer-direction=right]:w-[600px] ">
          <DrawerHeader className="border-b">
            <div className="flex items-center justify-between">
              <div>
                <DrawerTitle>{title}</DrawerTitle>
                <DrawerDescription>{description}</DrawerDescription>
              </div>
              <DrawerClose asChild>
                <Button variant="ghost" size="icon">
                  <X className="h-4 w-4" />
                </Button>
              </DrawerClose>
            </div>

            {/* 搜索和筛选 */}
            <div className="flex items-center space-x-4 pt-4">
              <div className="relative flex-1">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="请输入昵称或账号ID"
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>
              <Select value={provinceFilter} onValueChange={setProvinceFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="全部地区" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="authed">已授权</SelectItem>
                  <SelectItem value="expired">已过期</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 选择状态 */}
            <div className="flex items-center justify-between pt-2 text-sm text-muted-foreground">
              <span>
                已选择 {internalSelectedIds.length} 个账号
                {maxSelection && ` / ${maxSelection}`}
              </span>
              <Button variant="ghost" size="sm" onClick={handleReset}>
                重置
              </Button>
            </div>
          </DrawerHeader>

          {/* 表格内容 */}
          <div className="flex-1 p-4 overflow-hidden">
            <DataTable
              columns={columns}
              data={filteredAccounts}
              pagination={pagination}
              onPaginationChange={setPagination}
              loading={isLoading}
              emptyMessage={isError ? '加载失败，请重试' : '暂无账户数据'}
            />
          </div>

          <DrawerFooter className="border-t">
            <div className="flex items-center justify-end space-x-2">
              <Button variant="outline" onClick={() => setOpen(false)}>
                取消
              </Button>
              <Button
                onClick={handleConfirm}
                disabled={internalSelectedIds.length === 0}
              >
                确定 ({internalSelectedIds.length})
              </Button>
            </div>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    )
  }
)
