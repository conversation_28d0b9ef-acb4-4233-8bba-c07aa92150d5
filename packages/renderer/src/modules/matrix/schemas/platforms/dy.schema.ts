import z from 'zod'
import { accountFormSchema, commonAccountProductSchema, commonPoiSchema, commonPublishPlanSchema, publishModeSchema, publishSettingSchema, publishTimeTypeSchema, timeSettingSchema, videoMountModeSchema } from '../shared.schema'
import { buildDetailDO } from '../../utils/shared'
import { timeSettingMap } from '../../constants/shared'
import { PlatformKey } from '@/libs/request/api/generic-matrix'

export const dyFormSchema = z.object({
  publishMode: publishModeSchema,
  mountType: videoMountModeSchema,
  setting: publishSettingSchema,
  timeType: publishTimeTypeSchema,
  poi: commonPoiSchema.optional(),
  timeSetting: timeSettingSchema.optional(),
  
  titles: z.array(z.object({  title: z.string().min(1, '抖音标题不能为空').max(200, '标题不能超过200个字符') })).optional(),
  accountList: z.array(accountFormSchema).min(1, '至少选择一个发布账号'),
  accountProducts: z.array(commonAccountProductSchema).optional(),
})

export const dyMergedFormSchema = dyFormSchema.merge(commonPublishPlanSchema)

export const dySubmitSchema = dyMergedFormSchema.transform(form => ({
  ...form,
  timeSetting: timeSettingMap[form.timeType]?.(form.timeSetting) ?? {},
  accountIds: form.accountList.map(a => a.id),
  accountProducts: form.accountProducts?.map(ap => ({
    accountId: ap.accountInfo.id,
    products: ap.products
  })),
  titles: form.titles?.map(title => title.title),
  totalAccount: form.accountList.length,
  totalVideo: form.videoList.length,
  detailDOS: buildDetailDO(PlatformKey.dy, form)
}))

export type DyMergedForm = z.infer<typeof dyMergedFormSchema>
export type DySubmitForm = z.infer<typeof dySubmitSchema>

