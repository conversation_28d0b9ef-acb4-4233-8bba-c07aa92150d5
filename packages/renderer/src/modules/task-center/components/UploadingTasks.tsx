import { Progress } from '@/components/ui/progress'
import { useTaskCenter } from '@/modules/task-center/context/context'
import { UploadTask } from '@app/shared/types/local-db/entities/upload-task.entity'
import { UploadCloudIcon } from 'lucide-react'
import React, { FC, useMemo } from 'react'
import { cn } from '@/components/lib/utils'

type UploadingTasksProps = {
  scene: UploadTask.UploadSourceScenes
  fullSize?: boolean
}

export const UploadingTasks: FC<UploadingTasksProps> = ({ scene, fullSize = false }) => {
  const { uploading: { tasks } } = useTaskCenter()

  const uploadingTasks = useMemo(() => {
    return tasks
      .filter(task => task.source_scene === scene)
      .filter(t => t.status === UploadTask.Status.PENDING || t.status === UploadTask.Status.UPLOADING)
  }, [tasks, scene])

  if (uploadingTasks.length === 0) return null

  return (
    <div className="flex gap-4">
      {uploadingTasks?.map(task => (
        <div
          className={cn(
            'border border-input rounded bg-neutral-900/50 flex justify-center items-center flex-col gap-5 px-6 py-3',
            fullSize ? 'h-full w-full' : 'h-50 w-50',
          )}
        >
          <UploadCloudIcon />
          <Progress value={Math.round(task.progress) * 100} />
        </div>
      ))}
    </div>
  )
}
