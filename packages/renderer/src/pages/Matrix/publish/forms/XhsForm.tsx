import { useForm<PERSON>ontext, Controller } from 'react-hook-form'
import React from 'react'
import { AccountSelector } from '@/modules/matrix/components/publish/shared/AccountSelector'
import { PublishData } from '@/modules/matrix/schemas/publish.schema'
import { AccountProduct } from '@/modules/matrix/schemas/shared.schema'
import { PlatformKey } from '@/libs/request/api/generic-matrix'
import { RadioGroupField } from '@/modules/matrix/components/publish/shared/RadioGroupField'
import { settingOptions, timeTypeOptions } from '@/modules/matrix/constants/shared'
import { LoopTimeTypeTooltip } from '@/modules/matrix/components/publish/shared/LoopTimeTypeTooltip'
import { PublishTimeType } from '@/modules/matrix/enums/shared'
import { LoopPublishSection } from '@/modules/matrix/components/publish/shared/LoopPublishSection'
import { ScheduledPublishSection } from '@/modules/matrix/components/publish/shared/ScheduledPublishSection'
import { commercialTypeOptions } from '@/modules/matrix/constants/xhs'
import { CommercialType } from '@/modules/matrix/enums/xhs.enums'
import { NumberInput } from '@/components/ui/number-input'
import { TitleManage } from '@/modules/matrix/components/publish/shared/TitleArrayField'

export default function XhsForm() {
  const { control, formState: { errors }, watch, setValue } = useFormContext<PublishData>()
  const timeType = watch('platforms').xhs?.timeType

  const accountProducts =  watch('platforms').xhs?.accountProducts || []

  const renderCommercialNumberInput = () => {
    return (
      <Controller 
        control={control}
        name="platforms.xhs.maxProduct"
        render={({ field: { value, onChange } }) => (
          <div className="relative ml-3 flex items-center justify-between border h-full">
            <NumberInput 
              className="w-12 py-0 px-2 appearance-none border-none rounded "
              value={value}
              onChange={onChange}
              showControls={false}
              min={1}
              max={18}
            />
            <div className="px-2 h-full flex items-center bg-neutral-700">项</div>
          </div>
        )}
      />
    )
  }

  return (
    <div className="space-y-6">

      <TitleManage
        platform={PlatformKey.xhs}
        control={control}
        namePrefix="platforms.xhs.titles"
        errors={errors}
      />

      {/* 账号 */}
      <Controller
        control={control}
        name="platforms.xhs.accountList"
        render={({ field: { value, onChange } }) => (
          <AccountSelector
            platform={PlatformKey.xhs}          
            value={value || []}
            onChange={val => {
              const updatedAccountProducts: AccountProduct[]  = val.map(v => {
                const existing = accountProducts.find(ap => ap.accountInfo.id === v.id)
                return existing || {
                  accountInfo: v,
                  products: [] 
                }
              })

              setValue<any>('platforms.xhs.accountProducts', updatedAccountProducts)

              onChange(val)
            }}
            errors={errors.platforms?.xhs?.accountList?.message }
          />
        )}
      />

      {/* 发布时间类型 */}
      <Controller
        control={control}
        name="platforms.xhs.commercialType"
        render={({ field: { value, onChange } }) => (
          <RadioGroupField
            label="商业推广"
            options={commercialTypeOptions}
            value={value}
            onChange={val => onChange(Number(val))}
            tailContent={val => val === CommercialType.ONE_RANDOM_VIDEO_TO_MULTIPLE_NO_REPEAT_PRODUCT && renderCommercialNumberInput()}
          />
        )}
      />

      {/* 发布设置 */}
      <Controller
        control={control}
        name="platforms.xhs.setting"
        render={({ field: { value, onChange } }) => (
          <RadioGroupField label="发布设置"
            options={settingOptions}
            value={value}
            onChange={val => onChange(Number(val))}
            error={errors.platforms?.dy?.publishMode?.message}
          />
        )} 
      />

      {/* 发布时间类型 */}
      <Controller
        control={control}
        name="platforms.xhs.timeType"
        render={({ field: { value, onChange } }) => (
          <RadioGroupField
            label="发布时间类型"
            options={timeTypeOptions}
            value={value}
            onChange={val => onChange(Number(val))}
            error={errors.platforms?.dy?.timeType?.message}
            tailContent={val => val === PublishTimeType.LOOP && <LoopTimeTypeTooltip />}
          />
        )}
      />

      {/* 定时发布配置 - 当发布时间类型为定时时显示 */}
      {timeType === PublishTimeType.SCHEDULED && (
        <ScheduledPublishSection
          control={control}
          namePrefix="platforms.xhs.timeSetting"
        />
      )}
      {/* 循环定时发布配置 - 当发布时间类型为循环时显示 */}
      {timeType === PublishTimeType.LOOP && (
        <LoopPublishSection
          control={control}
          namePrefix="platforms.xhs.timeSetting"
        />
      )}
    </div>
  )
}
