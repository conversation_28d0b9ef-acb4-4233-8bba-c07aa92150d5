import React, { useMemo } from 'react'
import { useCurrentFrame } from 'remotion'
import { TransitionOverlay, TransitionType, VideoOverlay } from '@app/shared/types/overlay'
import { useRenderContext } from '../../render.context'
import { DissolveTransition } from '../transitions/dissolve-transition'

interface TransitionLayerContentProps {
  overlay: TransitionOverlay
}

export const TransitionLayerContent: React.FC<TransitionLayerContentProps> = ({ overlay }) => {
  const { overlays } = useRenderContext()
  const currentFrame = useCurrentFrame()

  // 获取转场的源视频和目标视频
  const fromVideo = useMemo(
    () => {
      return overlays.find(o => o.id === overlay.fromVideoId && o.type === 'video') as VideoOverlay
    },
    [overlays, overlay.fromVideoId]
  )

  const toVideo = useMemo(
    () => {
      return overlays.find(o => o.id === overlay.toVideoId && o.type === 'video') as VideoOverlay
    },
    [overlays, overlay.toVideoId]
  )

  // 计算转场进度 (0-1)
  const progress = useMemo(() => {
    const { durationInFrames: transitionDuration } = overlay

    if (currentFrame < 0) return 0
    if (currentFrame >= transitionDuration) return 1

    const rawProgress = currentFrame / transitionDuration

    // 应用缓动函数
    switch (overlay.config?.easing) {
      case 'ease-in':
        return rawProgress * rawProgress
      case 'ease-out':
        return 1 - Math.pow(1 - rawProgress, 2)
      case 'ease-in-out':
        return rawProgress < 0.5
          ? 2 * rawProgress * rawProgress
          : 1 - Math.pow(-2 * rawProgress + 2, 2) / 2
      default:
        return rawProgress
    }
  }, [currentFrame, overlay])

  // 如果找不到源视频或目标视频，不渲染
  if (!fromVideo || !toVideo) {
    return null
  }

  // 根据转场类型渲染不同效果
  switch (overlay.config.transitionType) {
    case TransitionType.DISSOLVE:
      return (
        <DissolveTransition
          fromVideo={fromVideo}
          toVideo={toVideo}
          progress={progress}
        />
      )

    default:
      return null
  }
}

