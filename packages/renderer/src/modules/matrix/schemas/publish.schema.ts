import z from 'zod'
import {  CommonPublishPlan, commonPublishPlanSchema, platformEnum } from './shared.schema'
import { dyFormSchema, dySubmitSchema } from './platforms/dy.schema'
import { PlatformKey } from '@/libs/request/api/generic-matrix'
import { platformValidationRules } from '../validation-rules'
import { xhsFormSchema, xhsSubmitSchema } from './platforms/xhs.schema'
import { ksFormSchema, ksSubmitSchema } from './platforms/ks.schema'

export const platformsSchema = z.object({
  [PlatformKey.dy]: dyFormSchema.optional(),
  [PlatformKey.xhs]: xhsFormSchema.optional(),
  [PlatformKey.ks]: ksFormSchema.optional(),
})

export const platformSubmitSchemas = z.object({
  [PlatformKey.dy]: dySubmitSchema.optional(),
  [PlatformKey.xhs]: xhsSubmitSchema.optional(),
  [PlatformKey.ks]: ksSubmitSchema.optional(),
})

const channelsSchema = z.object({
  selected: z.array(platformEnum).min(1, '至少选择一个平台'),
  active: platformEnum
})

/** 
 * 对应类型使用`PublishData`
 * */
export const publishSchema = z.object({
  channels: channelsSchema,
  common: commonPublishPlanSchema,
  //Zod 在解析时会递归校验整个 platformsSchema 对象, 表单校验只能延迟到 superRefine 再调用对应的子 schema
  platforms: z.record(z.string(), z.unknown()).default({}),
  previewSelectedIds: z.array(z.string()).optional()
})
  // .transform(data => ({
  //   ...data,
  //   platforms: Object.fromEntries(
  //     Object.entries(data.platforms || {}).filter(([k]) =>
  //       data.channels.selected.includes(k as PlatformKey)
  //     )
  //   ),
  // }))
  .superRefine((data, ctx) => {
    data.channels.selected.forEach(platform => {
      const schema = platformsSchema.shape[platform]
      const value = data.platforms?.[platform]

      if (!value) {
        ctx.addIssue({
          code: 'custom',
          path: ['platforms', platform],
          message: `${platform} 表单未填写`,
        })
        return
      }

      const parsed = schema.safeParse(value)
      if (!parsed.success) {
        parsed.error.issues.forEach(issue => {
          ctx.addIssue({
            ...issue,
            path: ['platforms', platform, ...issue.path],
          })
        })
        return
      }

      const rules = platformValidationRules[platform] || []
      rules.forEach(rule => rule({ data: { ...data, platforms: { ...data.platforms, [platform]: parsed.data } }, ctx }))
    })
  })

export type PublishData = {
  channels: {
    selected: PlatformKey[]
    active: PlatformKey
  }
  common: z.infer<typeof commonPublishPlanSchema>
  platforms: PlatformSchema,
  previewSelectedIds?: string[]

}

export type PlatformSchema = z.infer<typeof platformsSchema>

export type PlatformsSubmitMap =  z.infer<typeof platformSubmitSchemas>
export type PlatformPublishForm<T extends PlatformKey> = PlatformsSubmitMap[T]

export type PublishPayload = CommonPublishPlan & Partial<PlatformsSubmitMap>
