import React, { FC, useCallback, useMemo, useRef } from 'react'
import { CaptionOverlay, Overlay, OverlayType, SoundOverlay, VideoOverlay } from '@app/shared/types/overlay'
import { VideoOverlayKeyframe } from './video-overlay-keyframe'
import { TimelineItemHandle } from './timeline-item-handle'
import { TimelineItemContextMenu } from './timeline-item-context-menu'
import { StyleOnlyTimelineItem } from './style-only-timeline-item'
import TimelineCaptionBlocks from './timeline-caption-blocks'
import clsx from 'clsx'
import { useEditorContext, useTimeline, useTimelineDnd } from '@/modules/video-editor/contexts'
import { findOverlayStoryboard, getOverlayTimeRange } from '@/modules/video-editor/utils/overlay-helper'
import { PIXELS_PER_FRAME, TimelineZIndices } from '@/modules/video-editor/constants'
import { Volume2, VolumeOff } from 'lucide-react'
import { clamp } from 'lodash'
import { TimelineItemHandleDndWrapper } from './timeline-item-handle-dnd-wrapper'
import { useTimelineTrackContext } from '../timeline-track/timeline-track-context'
import { EditorDraggableTypes, useTypedDraggable } from '../../editor-dnd-wrapper'
import { getStoryboards } from '@/modules/video-editor/utils/track-helper'

export interface TimelineItemProps {
  item: Overlay
}

const useAdjustedStyles = (overlay: Overlay) => {
  const { zoomScale } = useTimeline()
  const { currentTrack } = useTimelineTrackContext()
  const { previewOverlaysAdjust, draggingOverlay  } = useTimelineDnd()
  const { tracks, durationInFrames } = useEditorContext()

  const isCurrentOverlayDragging = useMemo(
    () => draggingOverlay?.id === overlay.id,
    [draggingOverlay, overlay]
  )

  const left = useMemo(() => {
    if (overlay.type === OverlayType.TRANSITION) {
      const prevStoryboard = getStoryboards([currentTrack])[overlay.prevStoryboardIndex]
      const [, prevEnd] = getOverlayTimeRange(prevStoryboard)
      return (prevEnd - overlay.durationInFrames / 2) * zoomScale * PIXELS_PER_FRAME
    }

    const adjust = isCurrentOverlayDragging
      ? 0
      : previewOverlaysAdjust.get(overlay.id)?.fromFrameShift ?? 0

    return (overlay.from + adjust) * zoomScale * PIXELS_PER_FRAME
  }, [overlay, durationInFrames, isCurrentOverlayDragging, zoomScale, previewOverlaysAdjust])

  const width = useMemo(() => {
    const adjustOfCurrent = previewOverlaysAdjust.get(overlay.id)?.durationShift ?? 0
    const adjustedDuration = overlay.durationInFrames + adjustOfCurrent

    const storyboard = findOverlayStoryboard(tracks, overlay)
    // 分镜本身不需要限制长度
    if (!storyboard || overlay.type === OverlayType.STORYBOARD || currentTrack.isGlobalTrack) {
      return (overlay.durationInFrames + adjustOfCurrent) * zoomScale * PIXELS_PER_FRAME
    }

    const adjustOfStoryboard = previewOverlaysAdjust.get(storyboard.id)?.durationShift ?? 0
    const storyboardEndFrame = storyboard.from + storyboard.durationInFrames + adjustOfStoryboard

    const limitedDuration = clamp(adjustedDuration, 0, storyboardEndFrame - overlay.from)
    return limitedDuration * zoomScale * PIXELS_PER_FRAME
  }, [currentTrack, overlay, tracks, zoomScale, durationInFrames, previewOverlaysAdjust])

  return {
    left,
    width,
    positionAdjusted: (previewOverlaysAdjust.get(overlay.id)?.fromFrameShift ?? 0) !== 0
  }
}

const VolumeStatus: React.FC<{ item: VideoOverlay | SoundOverlay, setCurrentOverlayVolume: (vol: number) => void }> = ({ item, setCurrentOverlayVolume }) => {
  if (item.type !== OverlayType.VIDEO && item.type !== OverlayType.SOUND) return null

  return (
    <div className="absolute inset-0 group-hover:opacity-100 opacity-0 transition-all duration-150">
      <div className="flex h-full justify-end items-end pr-4 pb-0.5">
        <div
          className="bg-gray-500/80 size-4 flex justify-center items-center"
          style={{ zIndex: TimelineZIndices.volumeIcon }}
          onClick={e => {
            e.stopPropagation()
            setCurrentOverlayVolume(item.styles.volume ? 0 : 1)
          }}
        >

          {
            typeof item.styles.volume === 'number' && item.styles.volume > 0
              ? <Volume2 className="size-3 text-white" />
              : <VolumeOff className="size-3 text-white" />
          }
        </div>
      </div>
    </div>
  )
}

const TimelineItemContent: FC<Overlay> = item => {
  const {
    durationInFrames,
    videoPlayer: { currentFrame },
    updateOverlay
  } = useEditorContext()

  // const waveformData = useWaveformProcessor(
  //   item.type === OverlayType.SOUND ? item.src : undefined,
  //   item.type === OverlayType.SOUND ? item.startFromSound : undefined,
  //   item.durationInFrames,
  // )

  return (
    <>
      {item.type === OverlayType.CAPTION && (
        <div className="relative h-full">
          <TimelineCaptionBlocks
            captions={(item as CaptionOverlay).captions}
            durationInFrames={item.durationInFrames}
            currentFrame={currentFrame ?? 0}
            startFrame={item.from}
            totalDuration={durationInFrames}
          />
        </div>
      )}

      {/* {item.type === OverlayType.SOUND && waveformData && (
        <div className="absolute inset-0">
          <WaveformVisualizer
            waveformData={waveformData}
            totalDuration={durationInFrames}
            durationInFrames={item.durationInFrames}
          />
        </div>
      )} */}

      {item.type === OverlayType.VIDEO && (
        <VideoOverlayKeyframe overlay={item} />
      )}

      {(item.type === OverlayType.SOUND || item.type === OverlayType.VIDEO) && (
        <VolumeStatus
          item={item}
          setCurrentOverlayVolume={vol => updateOverlay(item.id, { styles: { volume: vol } })}
        />
      )}
    </>
  )
}

export const TimelineItem: React.FC<TimelineItemProps> = ({ item }) => {
  const { setNodeRef, listeners, attributes } = useTypedDraggable(
    EditorDraggableTypes.TimelineItem,
    item.id,
    {
      overlay: item,
    },
    {
      disabled: item.type === OverlayType.TRANSITION
    }
  )

  const { isDragging, draggingOverlay } = useTimelineDnd()
  const { toggleOverlaySelection, getOverlayActivationState } = useTimeline()

  const {
    tracks,
    selectedOverlay,
    setSelectedOverlay,
    videoPlayer: { seekTo }
  } = useEditorContext()

  const adjustedStyles = useAdjustedStyles(item)

  const itemRef = useRef<HTMLDivElement>(null)

  const isSelected = useMemo(
    () => selectedOverlay?.id === item.id,
    [selectedOverlay]
  )

  const isCurrentOverlayDragging = useMemo(
    () => draggingOverlay?.id === item.id,
    [draggingOverlay, item]
  )

  const selectCurrentOverlay = useCallback(() => {
    const target = item
    setSelectedOverlay(item)
    seekTo(target.from)
    toggleOverlaySelection(target)
  }, [setSelectedOverlay, toggleOverlaySelection])

  const handleSelect = (e: React.MouseEvent) => {
    e.stopPropagation()
    selectCurrentOverlay()
  }

  const containerClassName = 'absolute inset-y-px select-none pointer-events-auto overflow-hidden'

  const opacity = useMemo<number>(() => {
    if (
      isCurrentOverlayDragging
      || (draggingOverlay?.type === OverlayType.STORYBOARD && item.type === OverlayType.TRANSITION)
    ) {
      return 0
    }

    return adjustedStyles.positionAdjusted ? 0.5 : 1
  }, [tracks, isCurrentOverlayDragging, item, isDragging, adjustedStyles])

  return (
    <TimelineItemContextMenu overlay={item}>
      <div
        ref={ref => {
          itemRef.current = ref
          setNodeRef(ref)
        }}
        className={clsx(
          containerClassName,
          !getOverlayActivationState(item) && 'grayscale',
          isSelected
            ? 'border-2 border-black dark:border-white rounded-md'
            : 'border-0',
        )}
        style={{
          left: adjustedStyles.left,
          width: adjustedStyles.width,
          zIndex: (
            isDragging
              ? TimelineZIndices.draggingItem
              : isSelected
                ? TimelineZIndices.activeItem
                : TimelineZIndices.normalItem
          ) + (
            item.type === OverlayType.TRANSITION ? TimelineZIndices.transitionItemDelta : 0
          ),
          transition: 'opacity 0.2s, left 0.15s ease-out',
          opacity,
        }}
        {...listeners}
        {...attributes}
        onClick={e => {
          e.stopPropagation() // 防止事件冒泡到时间轴点击处理器
          handleSelect(e)
        }}
      >
        <StyleOnlyTimelineItem
          item={item}
          width={adjustedStyles.width}
          className="StyleOnlyTimelineItem w-full h-full relative"
        >
          <TimelineItemHandleDndWrapper>
            <TimelineItemContent {...item} />

            {/* TODO: 为 TransitionOverlay 也实现 Resize Handle */}
            {item.type !== OverlayType.TRANSITION && (
              <TimelineItemHandle
                disabled={isDragging && !isCurrentOverlayDragging }
                position="right"
                isSelected={isSelected}
                overlay={item}
              />
            )}
          </TimelineItemHandleDndWrapper>
        </StyleOnlyTimelineItem>
      </div>
    </TimelineItemContextMenu>
  )
}

