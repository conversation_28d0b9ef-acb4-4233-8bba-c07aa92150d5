// 模糊转场组件
import React, { useEffect, useMemo, useRef } from 'react'
import { ThreeCanvas, useVideoTexture } from '@remotion/three'
import { useRenderContext } from '../../render.context'
import { TransitionBaseProps } from '../../types'
import { Easing, interpolate, useVideoConfig, Video } from 'remotion'
import * as THREE from 'three'
import { Vector2 } from 'three'

const VERT = /* glsl */ `
  varying vec2 vUv;
  void main() {
    vUv = uv;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`

const FRAG = /* glsl */ `
  precision highp float;
  varying vec2 vUv;
  uniform sampler2D u_tex;
  uniform vec2 u_res;      // 纹理分辨率（像素）
  uniform float u_radius;  // 模糊半径（像素）

  // 经典 9-tap 高斯权重（来源于常用的快速高斯模糊写法）
  vec4 blur9(sampler2D img, vec2 uv, vec2 res, float radius) {
    vec2 off1 = vec2(1.3846153846) * radius / res;
    vec2 off2 = vec2(3.2307692308) * radius / res;

    // 横向
    vec4 h = texture2D(img, uv) * 0.2270270270;
    h += texture2D(img, uv + vec2(off1.x, 0.0)) * 0.3162162162;
    h += texture2D(img, uv - vec2(off1.x, 0.0)) * 0.3162162162;
    h += texture2D(img, uv + vec2(off2.x, 0.0)) * 0.0702702703;
    h += texture2D(img, uv - vec2(off2.x, 0.0)) * 0.0702702703;

    // 纵向
    vec4 v = texture2D(img, uv) * 0.2270270270;
    v += texture2D(img, uv + vec2(0.0, off1.y)) * 0.3162162162;
    v += texture2D(img, uv - vec2(0.0, off1.y)) * 0.3162162162;
    v += texture2D(img, uv + vec2(0.0, off2.y)) * 0.0702702703;
    v += texture2D(img, uv - vec2(0.0, off2.y)) * 0.0702702703;

    return 0.5 * (h + v); // 合并横+纵
  }

  void main() {
    gl_FragColor = blur9(u_tex, vUv, u_res, u_radius);
  }
`

// 基础的 Three.js 视频平面组件
const VideoPlane: React.FC<{
  video: React.RefObject<HTMLVideoElement | null>
  opacity: number
  aspect: number,
  blurRadius: number
}> = ({ video, aspect, opacity, blurRadius }) => {
  const texture = useVideoTexture(video)
  const { width, height } = useVideoConfig()

  const shaderMaterialRef = useRef<THREE.ShaderMaterial>(null)

  useMemo(() => {
    if (!texture) return
    texture.wrapS = texture.wrapT = THREE.ClampToEdgeWrapping
    texture.minFilter = THREE.LinearFilter
    texture.magFilter = THREE.LinearFilter
    texture.generateMipmaps = false
    texture.needsUpdate = true
  }, [texture])

  const uniforms = useMemo(
    () => ({
      u_tex: { value: texture },
      u_res: { value: new Vector2(width, height) },
      u_radius: { value: blurRadius },
    }),
    [texture, width, height, blurRadius]
  )

  useEffect(() => {
    if (shaderMaterialRef.current) {
      shaderMaterialRef.current.uniforms.u_radius.value = blurRadius
    }
  }, [blurRadius])

  return (
    <mesh>
      <planeGeometry args={[1, 1 / aspect]} />
      {texture && (
        <shaderMaterial
          ref={shaderMaterialRef}
          vertexShader={VERT}
          fragmentShader={FRAG}
          uniforms={uniforms}
          transparent={false}
          depthTest={false}
          depthWrite={false}
          toneMapped={false}
        />
        // <meshBasicMaterial
        //   map={videoTexture}
        // />
      )}
    </mesh>
  )
}

export const BlurTransition: React.FC<TransitionBaseProps> = ({
  fromVideo,
  toVideo,
  progress
}) => {
  const { playerMetadata: { width, height } } = useRenderContext()
  const maxBlur = 20

  // 计算透明度
  const { fromOpacity, fromBlur } = useMemo(() => ({
    fromOpacity: 1,
    fromBlur: interpolate(
      progress,
      [0, 0.5, 1],
      [0, maxBlur, 0],
      { easing: Easing.inOut(Easing.ease), extrapolateRight: 'clamp' }
    ),
    toOpacity: 0
  }), [progress])

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width,
    height,
    overflow: 'hidden',
  }

  const layerStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  }

  const fromVideoRef = useRef<HTMLVideoElement | null>(null)
  const toVideoRef = useRef<HTMLVideoElement | null>(null)

  return (
    <div style={containerStyle} id={`blur-${fromVideo.id}-${toVideo.id}`}>
      {/* 源视频层 - 使用 Three.js 渲染 */}
      <div style={{ ...layerStyle, zIndex: 1 }}>
        {/*{fromOpacity > 0.01 ? (*/}
        <ThreeCanvas
          orthographic={false}
          width={width}
          height={height}
          camera={{ position: [0, 0, 1] }}
          style={{ width: '100%', height: '100%' }}
        >
          <VideoPlane
            video={fromVideoRef}
            aspect={fromVideo.originalMeta.width / fromVideo.originalMeta.height}
            opacity={fromOpacity}
            blurRadius={fromBlur}
          />
        </ThreeCanvas>
        {/*) : null}*/}
      </div>

      {/* 目标视频层 - 使用 Three.js 渲染 */}
      {/*<div style={{ ...layerStyle, zIndex: 2 }}>*/}
      {/*  {toOpacity > 0.01 ? (*/}
      {/*    <ThreeCanvas*/}
      {/*      width={width}*/}
      {/*      height={height}*/}
      {/*      camera={{ position: [0, 0, 1] }}*/}
      {/*      style={{ width: '100%', height: '100%' }}*/}
      {/*    >*/}
      {/*      /!*<ambientLight intensity={1} />*!/*/}
      {/*      <VideoPlane video={toVideo} opacity={toOpacity} />*/}
      {/*    </ThreeCanvas>*/}
      {/*  ) : null}*/}
      {/*</div>*/}

      <Video
        ref={fromVideoRef}
        src={fromVideo.localSrc || fromVideo.src}
        style={{ position: 'absolute', opacity: 0 }}
      />
    </div>
  )
}
