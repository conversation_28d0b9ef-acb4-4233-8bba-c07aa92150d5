import { AccountTokenStatus, PublishStatus, PublishMode, PublishSetting, PublishTimeType } from '../enums/shared'
import DouyinIcon from '@/assets/platforms/douyin.svg'
import XiaohongshuIcon from '@/assets/platforms/xiaohongshu.svg'
import KuaishouIcon from '@/assets/platforms/kuaishou.svg'

import { PlatformKey } from '@/libs/request/api/generic-matrix'
import { TimeSetting } from '../schemas/shared.schema'

export const modeOptions = [
  { value: PublishMode.CHECK_IN, label: '打卡' },
  { value: PublishMode.GOODS, label: '带货' }
]

export const settingOptions = [
  { value: PublishSetting.ONE_ACCOUNT_ONE_VIDEO, label: '一个账号一个视频' },
  { value: PublishSetting.ONE_ACCOUNT_MULTIPLE_VIDEOS, label: '一个账号多个视频' }
]

export const timeTypeOptions = [
  { value: PublishTimeType.IMMEDIATE, label: '立即发布' },
  { value: PublishTimeType.SCHEDULED, label: '定时发布' },
  { value: PublishTimeType.LOOP, label: '循环定时发布' }
]

export const platformIconMap = {
  [PlatformKey.dy]: DouyinIcon,
  [PlatformKey.xhs]: XiaohongshuIcon,
  [PlatformKey.ks]: KuaishouIcon
}

export const timeSettingMap = {
  [PublishTimeType.LOOP]: (ts?: TimeSetting) => ({
    loopDays: ts?.loopDays,
    loopTime: `${ts?.loopTime}:00`,
    loopPeriod: ts?.loopPeriod,
    numEachDay: ts?.numEachDay,
  }),
  [PublishTimeType.SCHEDULED]: (ts?: TimeSetting) => ({
    publishTime: ts?.publishTime,
    period: ts?.period,
    periodType: ts?.periodType,
  }),
}

export const PLATFORM_CONFIG = {
  [PlatformKey.dy]: {
    key: PlatformKey.dy,
    name: '抖音',
    icon: platformIconMap[PlatformKey.dy],
  },
  [PlatformKey.xhs]: {
    key: PlatformKey.xhs,
    name: '小红书',
    icon: platformIconMap[PlatformKey.xhs],
  },
  [PlatformKey.ks]: {
    key: PlatformKey.ks,
    name: '快手',
    icon: platformIconMap[PlatformKey.ks],
  }
}

// 授权状态映射
export const accountAuthStatusMap = {
  [AccountTokenStatus.AUTHORIZED]: { label: '已授权', variant: 'default' as const, key: AccountTokenStatus.AUTHORIZED },
  [AccountTokenStatus.EXPIRED]: { label: '已过期', variant: 'destructive' as const, key: AccountTokenStatus.EXPIRED },
  [AccountTokenStatus.ALL]: { label: '全部', variant: 'default' as const, key: AccountTokenStatus.ALL },
}

export const publishStatusMap = {
  [PublishStatus.ALL]: { label: '所有', variant: 'secondary' as const },
  [PublishStatus.AWAITING]: { label: '待发布', variant: 'secondary' as const },
  [PublishStatus.PUBLISHING]: { label: '发布中', variant: 'default' as const },
  [PublishStatus.FAILED]: { label: '失败', variant: 'destructive' as const },
  [PublishStatus.SUCCESS]: { label: '成功', variant: 'success' as const },
  [PublishStatus.PART_SUCCESS]: { label: '部分成功', variant: 'warning' as const },
}