import React from 'react'
import { useParams } from 'react-router'
import { useQueryDirRecycleList, useQueryMediaRecycleList } from '@/hooks/queries/useQueryMaterial'
import { MaterialResource, MultiAction, ResourceSource } from '@/types/resources'
import { useProjectsContext } from '../../context'
import MediaItem from '@/components/material/MediaItem'
import { useSelection } from '@/hooks/useSelection'
import BatchActionButtons from '@/components/material/BatchActionButtons'
import { useItemActions } from '@/hooks/useItemActions'
import { useMediaActions } from '@/hooks/useMediaActions'
import { useFolderActions } from '@/hooks/useFolderActions'

const MaterialRecycle: React.FC = () => {
  const params = useParams()
  const { keyword } = useProjectsContext()
  const { data: dirData, isLoading: isDirLoading } = useQueryDirRecycleList(
    {
      projectId: Number(params.projectId),
      keyword,
    },
    { enabled: !!params.projectId },
  )
  const { data: mediaData, isLoading: isMediaLoading } = useQueryMediaRecycleList({
    projectId: Number(params.projectId),
    keyword,
  })

  const { MediaAndFoldersMulitAction } = useItemActions()

  const folderAsMediaItems: MaterialResource.Media[] = dirData?.map(folder => ({
    fileId: folder.folderId,
    fileName: folder.folderName,
    folderUuid: folder.parentId!,
    childrenFolder: folder.folderCount,
    mediaNum: folder.imageCount + folder.videoCount + folder.audioCount,
    resType: MaterialResource.MediaType.FOLDER,
    createTime: folder.createdAt,
    recycleExpiredAt: folder.recycleExpiredAt ?? 0,
    url: '',
  }))

  const selection = useSelection({
    mediaList: mediaData,
    folderAsMediaItems: folderAsMediaItems,
    getMediaId: media => media.fileId,
    getFolderId: folder => folder.fileId,
  })

  const handleBatchDelete = async (mulitType: MultiAction) => {
    await MediaAndFoldersMulitAction(mulitType, selection.selectedMediaItems, selection.selectedFolderItems, () => {
      selection.setSelectedMediaItems(new Set())
      selection.setSelectedFolderItems(new Set())
    })
  }

  const folderActions = useFolderActions({ isLocal: false, type: ResourceSource.FOLDER, isRecycle: true })

  const mediaActions = useMediaActions({ isLocal: false, type: ResourceSource.MEDIA, isRecycle: true })

  return (
    <div className="p-4 pb-10 flex flex-col flex-1 h-full w-full overflow-auto">
      <div className="flex-1 h-full overflow-y-auto">
        <div className="flex items-center justify-between text-sm text-gray-600 pl-4">
          <div>文件在回收站保留15天，之后将被自动清除</div>
          <div className="flex justify-end items-center space-x-4">
            {selection.selectedCount !== 0 && (
              <BatchActionButtons
                variant="default"
                highlightClass="bg-primary-highlight1 hover:bg-blue-400 text-white"
                deleteName={selection.selectedText}
                isRecycle={true}
                onDelete={handleBatchDelete}
              />
            )}
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={selection.allSelected}
                onChange={selection.toggleSelectAll}
                className="accent-primary-highlight1 mr-1"
              />
              全选
            </label>
            <span> | </span>
            <span>
              已选 {selection.selectedCount}/{selection.materialCount}
            </span>
          </div>
        </div>

        {isDirLoading && isMediaLoading ? (
          <div className="flex items-center justify-center h-full">加载中...</div>
        ) : (
          <div className="flex flex-wrap gap-4 p-4">
            {folderAsMediaItems.map(folder => (
              <MediaItem
                key={folder.fileId}
                isTrash={true}
                orientation={MaterialResource.MediaStyle.HORIZONTAL}
                media={folder}
                isSelected={selection.selectedFolderItems.has(folder.fileId)}
                isFolder={true}
                actions={folderActions}
                onToggleSelect={fileId => selection.toggleSelect(fileId, true)}
              />
            ))}

            {mediaData?.pages.map(page =>
              page.list.map(media => (
                <MediaItem
                  key={media.fileId}
                  isTrash={true}
                  orientation={MaterialResource.MediaStyle.HORIZONTAL}
                  media={media}
                  isSelected={selection.selectedMediaItems.has(media.fileId)}
                  isFolder={false}
                  actions={mediaActions}
                  onToggleSelect={fileId => selection.toggleSelect(fileId, false)}
                />
              )),
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default MaterialRecycle
