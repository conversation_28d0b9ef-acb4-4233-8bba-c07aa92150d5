import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>box<PERSON><PERSON>, CheckboxProvider, useCheckboxContext } from '@/components/checkbox'
import { ModalContent } from '@/components/modal'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { useQueryProjectList } from '@/hooks/queries/useQueryProject'
import { useQueryTeamRoles } from '@/hooks/queries/useQueryTeam'
import { usePending } from '@/hooks/usePending'
import { TeamAPI } from '@/libs/request/api/team'
import { useModal } from '@/libs/tools/modal'
import { Loader2, Search, User2Icon } from 'lucide-react'
import React from 'react'
import { toast } from 'react-toastify'

export enum MemberPermissionMode {
  INVITE = 'invite',
  EDIT = 'edit',
}

type MemberPermissionModalProps = {
  mode?: MemberPermissionMode
  userInfo?: any
}

function MemberPermissionModal({ mode = MemberPermissionMode.INVITE, userInfo }: MemberPermissionModalProps) {
  const { data } = useQueryTeamRoles()
  const roles = React.useMemo(() => data?.filter(role => role.code !== 'owner') || [], [data])
  const [selectedRole, setSelectedRole] = React.useState<string>('')
  const { selected, all, setSelected } = useCheckboxContext<number>()
  const { data: projectList } = useQueryProjectList()
  const [keyword, setKeyword] = React.useState('')

  return (
    <ModalContent
      title={mode === MemberPermissionMode.INVITE ? '邀请成员' : '编辑成员'}
      description={mode === MemberPermissionMode.INVITE ? '请选择新成员的角色与项目权限' : '修改成员的角色与项目权限'}
      buttons={() => {
        const { pending, withPending } = usePending()

        return (
          <Button
            className="w-full"
            disabled={!selectedRole || pending}
            onClick={withPending(async () => {
              const roleId = roles.find(role => role.code === selectedRole)?.id
              if (!roleId) return

              if (mode === MemberPermissionMode.INVITE) {
                // 邀请逻辑
                const code = await TeamAPI.invite({
                  projectIds: Object.values(selected),
                  roleIds: [roleId],
                })
                navigator.clipboard.writeText(code)
                toast.success('邀请链接已复制到剪贴板')
              } else {
                // TODO: 修改成员角色和项目权限接口未提供
                // 编辑逻辑
                // await TeamAPI.updateMember({
                //   projectIds: Object.values(selected),
                //   roleId,
                //   userId: userInfo.id,
                // })
                toast.success('成员信息已更新')
              }
            })}
          >
            {pending && mode === MemberPermissionMode.INVITE ? (
              <Loader2 className="animate-spin size-4" />
            ) : mode === MemberPermissionMode.INVITE ? (
              '复制邀请码'
            ) : (
              '保存修改'
            )}
          </Button>
        )
      }}
    >
      {mode === MemberPermissionMode.EDIT && (
        <div className="flex items-center text-sm mb-4">
          {/* 头像 */}
          {userInfo.avatar ? (
            <img src={userInfo.avatar} alt={userInfo.nickname} className="w-10 h-10 rounded-full" />
          ) : (
            <div className="w-10 h-10 rounded-full border bg-blue flex items-center justify-center">
              <User2Icon className="w-7 h-7 text-muted-foreground" />
            </div>
          )}
          <div>
            <div>
              {userInfo.remark && <span className="ml-4 font-bold">{userInfo.remark}</span>}
              <span className="ml-4">{userInfo.nickname ?? '未命名昵称'}</span>
            </div>
            <span className="ml-4">{userInfo.mobile}</span>
          </div>
        </div>
      )}
      <div className="flex items-center justify-between">
        <span>权限角色</span>
        <Select
          value={selectedRole}
          onValueChange={code => {
            setSelectedRole(code)
            if (code === 'admin') Object.keys(all).forEach(id => setSelected(id, true))
          }}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="选择角色" />
          </SelectTrigger>
          <SelectContent>
            {roles.map(role => (
              <SelectItem key={role.id} value={role.code}>
                {role.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <Separator />
      <div className="flex flex-col gap-2">
        <div className="flex items-center">
          <div className="relative text-sm">
            <Input
              placeholder="搜索项目"
              value={keyword}
              onChange={e => setKeyword(e.target.value)}
              className="pr-10 h-8"
            />
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
          </div>
          <span className="ml-auto text-xs text-muted-foreground">
            已选择：{Object.values(selected).length}/{Object.values(all).length}
          </span>
          <CheckboxHeader className="ml-2" disabled={selectedRole === 'admin'} />
        </div>
        <div className="max-h-80 overflow-y-scroll -mr-4.5 pr-3">
          {projectList?.map(project => (
            <div
              className="flex items-center aria-hidden:hidden"
              aria-hidden={!project.projectName.includes(keyword)}
              key={project.id}
            >
              <Badge variant="default" className="px-1 rounded leading-none">
                项目
              </Badge>
              <span className="ml-2">{project.projectName}</span>
              <CheckboxItem className="ml-auto" value={project.id} disabled={selectedRole === 'admin'} />
            </div>
          ))}
        </div>
      </div>
    </ModalContent>
  )
}

export function useModalMemberPermission() {
  const modal = useModal()

  return (mode: MemberPermissionMode, userInfo?: any) =>
    modal({
      content: (
        <CheckboxProvider>
          <MemberPermissionModal mode={mode} userInfo={userInfo} />
        </CheckboxProvider>
      ),
    })
}
