import { find, flatMap, sample, shuffle, zip } from 'lodash'
import { SelectedVideoData } from '@/modules/matrix/components/publish/shared/VideoSelectionSection'
import {  PublishSetting } from '@/modules/matrix/enums/shared'
import {  DyMergedForm } from '@/modules/matrix/schemas/platforms/dy.schema'
import { AccountForm, DetailPlanDO } from '@/modules/matrix/schemas/shared.schema'
import { PlatformsSubmitMap, platformSubmitSchemas, PublishData, PublishPayload } from '../schemas/publish.schema'
import { GenericMatrixModule, PlatformKey } from '@/libs/request/api/generic-matrix'
import { XhsMergedForm } from '../schemas/platforms/xhs.schema'
import { KsMergedForm } from '../schemas/platforms/ks.schema'

type MergedFormMap = {
  [PlatformKey.dy]: DyMergedForm
  [PlatformKey.xhs]: XhsMergedForm
  [PlatformKey.ks]: KsMergedForm
}

// // 平台差异化处理函数
// const platformVideoTitleMap: {
//   [K in PlatformKey]?: (payload: MergedFormMap[K]) => string[]
// } = {
//   [PlatformKey.dy]: (payload: DyMergedForm) => payload.videoList.map(v => sample(payload.titles)?.title || v.name?.trim()),
//   [PlatformKey.xhs]: (payload: XhsMergedForm) => payload.videoList.map(v => sample(payload.titles)?.title || v.name?.trim()),
//   [PlatformKey.ks]: (payload: KsMergedForm) => payload.videoList.map(v => sample(payload.titles)?.title || v.name?.trim()),
// }

/**
 * 构建 detailDOS 数据 
 */
export const buildDetailDO =  <K extends PlatformKey>(_platformKey: K, payload: MergedFormMap[K]): DetailPlanDO[] => {
  const { accountList, videoList, accountProducts, setting, poi } = payload

  const videoTitles = payload.titles?.map(v => sample(payload.titles)?.title || v.name?.trim()) as string[]

  // 公共构建方法
  const buildDetail = (account: AccountForm, video: SelectedVideoData) => {
    const products =
      find(accountProducts, ap => ap.accountInfo.id === account.id)?.products?.map(p => ({
        title: p.title,
        url: p.url,
      })) || []

    return {
      accountId: account.id,
      cover: video.cover || '',
      description: '',
      poiId: poi?.poiId,
      poiName: poi?.poiName,
      products,
      title: sample(videoTitles) ?? '',
      url: video.url!,
    }
  }

  if (setting === PublishSetting.ONE_ACCOUNT_ONE_VIDEO) {
    return zip(shuffle(accountList), shuffle(videoList))
      .filter(([a, v]) => a && v)
      .map(([a, v]) => buildDetail(a!, v!))
  }
  
  if (setting === PublishSetting.ONE_ACCOUNT_MULTIPLE_VIDEOS) {
    return flatMap(accountList, acc =>
      videoList.map(v => buildDetail(acc, v))
    )
  }

  return []
}

/**
 * 通用 transform 函数
 */
export function transformPublishDataToSubmitMap<T extends PublishData>(
  data: T,
  options?: { draftSuffix?: boolean }

): Partial<PlatformsSubmitMap> {
  const { common, platforms, channels } = data

  return channels.selected.reduce((acc, platformKey) => {
    const schema = platformSubmitSchemas.shape[platformKey]
    
    if (!schema) return acc

    const key = options?.draftSuffix ? `${platformKey}Draft` : platformKey

    // @ts-ignore
    acc[key] = schema.parse({
      ...common,
      ...platforms[platformKey],
    })

    return acc
  }, {} as Partial<PlatformsSubmitMap>)
}

export async function buildPublishRequest(data: PublishPayload) {
  const requests = (Object.values(PlatformKey) as PlatformKey[])
    .filter(p => data[p])
    .map(p =>
      GenericMatrixModule.endpoints.publish(p, data[p]!).then(res => ({
        platform: p,
        result: res,
      }))
    )
  
  const results = await Promise.all(requests)
  
  return results.reduce<Record<PlatformKey, any>>((acc, { platform, result }) => {
    acc[platform] = result
    return acc
  }, {} as Record<PlatformKey, any>)
}