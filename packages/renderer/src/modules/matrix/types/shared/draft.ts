import { <PERSON>Key } from '@/libs/request/api/generic-matrix'
import { DySubmitForm } from '@/modules/matrix/schemas/platforms/dy.schema'
import { XhsSubmitForm } from '@/modules/matrix/schemas/platforms/xhs.schema'
import { CommonVideoData } from '@/modules/matrix/schemas/shared.schema'
import { KsSubmitForm } from '../../schemas/platforms/ks.schema'

export type PlatformDraftMap = {
  [K in PlatformKey as `${K}Draft`]?: 
  K extends PlatformKey.dy ? DySubmitForm :
    K extends  PlatformKey.xhs ? XhsSubmitForm :
      K extends  PlatformKey.ks ? KsSubmitForm :
        never; 
}

export interface DraftInfo extends PlatformDraftMap {
  id: number;
  name?: string;
  videoList?: CommonVideoData[];
  deliverType?: number;
  createTime?: number;
  updateTime?: number;
}

