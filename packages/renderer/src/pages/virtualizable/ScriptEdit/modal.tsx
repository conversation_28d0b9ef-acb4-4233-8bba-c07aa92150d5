import React, { useEffect } from 'react'
import { z } from 'zod'
import { Input } from '@/components/ui/input'
import { genForm } from '@/libs/tools/form'
import { useFormContext } from 'react-hook-form'
import { useModal, useModalContext } from '@/libs/tools/modal'
import { <PERSON><PERSON><PERSON>oot<PERSON>, ModalHeader } from '@/components/modal'
import { ScriptSession } from '@/libs/request/api/script-chat'
import { usePending } from '@/hooks/usePending'

const UpdateForm = genForm(
  z.object({
    title: z.string().min(1, '请输入会话名称'),
  }),
  {
    fields: {
      title: {
        label: '会话名称',
        render: ({ field }) => {
          const { setValue } = useFormContext()

          return (
            <div className="relative">
              <Input
                {...field}
                className="pr-16"
                onChange={e => setValue('title', e.currentTarget.value.slice(0, 20))}
              />
              <span className="absolute top-1/2 -translate-y-1/2 right-2 text-muted-foreground text-sm">
                {field.value.length}/20
              </span>
            </div>
          )
        },
      },
    },
  },
)

function UpdateModal({
  session,
  onConfirm,
}: {
  session: ScriptSession
  onConfirm?: (session?: ScriptSession) => void
}) {
  const { close, closed } = useModalContext()
  const { pending, withPending } = usePending()

  useEffect(() => {
    if (closed) onConfirm?.()
  }, [closed])

  return (
    <>
      <ModalHeader title="更新会话" />
      <UpdateForm
        defaultValues={session}
        onSubmit={withPending(async values => {
          onConfirm?.({ ...session, ...values })
          close()
        })}
      >
        <ModalFooter pending={pending} />
      </UpdateForm>
    </>
  )
}

export const useModalUpdateScriptSession = () => {
  const modal = useModal()

  return (session: ScriptSession) =>
    new Promise<ScriptSession | undefined>(resolve => {
      modal({ content: <UpdateModal session={session} onConfirm={resolve} /> })
    })
}
