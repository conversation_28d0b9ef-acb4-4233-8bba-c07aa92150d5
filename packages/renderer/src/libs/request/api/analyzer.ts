import { fetchPaginationGet, requestCurrying } from '../request'
import { HotAnalyzer, HotAnalyzerInfo } from '@/types/analyzer'

export const HotAnalyzerModule = {
  page: fetchPaginationGet<HotAnalyzer>('/app-api/script/hot_copy/page'),
  create: requestCurrying.post<{ url: string, islocal: boolean }>('/app-api/script/hot_copy/create'),
  get: requestCurrying.get<{ id: string }, HotAnalyzerInfo>('/app-api/script/hot_copy/info'),
  delete: requestCurrying.delete<{ id: string }>('/app-api/script/hot_copy/delete'),
  tags: requestCurrying.post<{ content: string, promptCode: string }>('/app-api/script/hot_copy/prompt'),
  getForm: requestCurrying.get<{ id: number }>('/app-api/script/hot_copy/form_config'),
  createForm: requestCurrying.post<{ promptCode: string, params: string }>('/app-api/script/hot_copy/form', { timeout: 40000 }),
  formCategories: requestCurrying.get<{ id: number }>('/app-api/script/hot_copy/category'),
}
