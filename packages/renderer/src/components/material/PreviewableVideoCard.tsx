import React, { useCallback, useRef, useState } from 'react'
import { MaterialResource } from '@/types/resources'
import { cn } from '@/components/lib/utils'
import { clamp } from 'lodash'
import { AuthedImg } from '@/components/authed-img'
import { useLoadTileImageInfo } from '@/hooks/material/useLoadTileImageInfo'
import { TileRenderer } from '@/components/material/TileRenderer'

interface MediaPreviewProps {
  media: MaterialResource.Media
}

export const PreviewableVideoCard: React.FC<MediaPreviewProps> = ({ media }) => {
  const originalRatio = media.width && media.height ? (media.width / media.height) : 1

  const [currentFrame, setCurrentFrame] = useState(0)
  const [hovered, setHovered] = useState(false)

  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const containerRef = useRef<HTMLDivElement | null>(null)
  const loadedImageRef = useRef<HTMLImageElement | null>(null)

  // 鼠标移动时计算当前帧
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!containerRef.current || !hovered || !tileInfo || tileInfo.totalFrames === 0) return

    // 获取容器的位置信息
    const { left, width } = containerRef.current.getBoundingClientRect()
    const relativeX = e.clientX - left

    // 计算相对位置的比例
    const percent = relativeX / width
    const frameIndex = clamp(Math.round(percent * tileInfo.totalFrames), 0, tileInfo.totalFrames - 1)

    if (frameIndex !== currentFrame) {
      setCurrentFrame(frameIndex)
    }
  }

  // 鼠标进入容器时加载瓦片图
  const handleMouseEnter = useCallback(() => {
    // 如果已经有定时器在运行，先清除掉
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      setHovered(true)
    }, loadedImageRef.current ? 0 : 300)
  }, [hovered])

  const tileInfo = media.coverFrame
    ? useLoadTileImageInfo(media.coverFrame)
    : null

  return (
    <div
      ref={containerRef}
      className={cn('flex relative group h-full w-full items-center justify-center')}
      onMouseEnter={handleMouseEnter}
      onMouseMove={handleMouseMove}
    >
      {
        tileInfo ? (
          <TileRenderer
            tileInfo={tileInfo}
            aspectRatio={originalRatio}
            currentFrame={currentFrame}
          />
        ) : (
          <AuthedImg src={media.cover} alt={media.fileName} className="h-full" />
        )
      }

      {hovered && tileInfo && (
        <div
          className="absolute border-gray-500 border-r border-dashed"
          style={{
            left: `${currentFrame / tileInfo?.totalFrames * 100}%`,
            height: '100%',
            width: 1,
          }}
        />
      )}
    </div>
  )
}

