import { SimpleMixcutPipeline } from '../mixcut-pipeline.types.js'
import { VideoOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut.js'

/**
 * 随机放大视频，同时补偿位移和宽高，以保证视频中心点不变的同时填满画面大小
 */
export class VideoScalePipeline extends SimpleMixcutPipeline<typeof MIXCUT_PIPELINES.video.scale, VideoOverlay> {

  filter(overlay: MixcutableOverlay): overlay is VideoOverlay & MixcutableOverlay {
    return SimpleMixcutPipeline.filters.videoOverlay(overlay)
  }

  async processSingleOverlay(overlay: VideoOverlay & MixcutableOverlay) {
    const { range = 0.2 } = this.context.params

    const scaleDelta = _.random(0, range)

    return {
      ...overlay,
      width: Math.round(overlay.width * (1 + scaleDelta)),
      height: Math.round(overlay.height * (1 + scaleDelta)),
      left: overlay.left - overlay.width * scaleDelta / 2,
      top: overlay.top - overlay.height * scaleDelta / 2,
    }
  }
}
