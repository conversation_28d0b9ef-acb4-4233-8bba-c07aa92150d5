import { VideoOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { SimpleMixcutPipeline } from '../mixcut-pipeline.types.js'
import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut.js'

/**
 * 将视频随机旋转一定角度，并补偿大小和位移以保证填满画面
 */
export class VideoRotationPipeline extends SimpleMixcutPipeline<typeof MIXCUT_PIPELINES.video.rotation, VideoOverlay> {

  filter(overlay: MixcutableOverlay): overlay is VideoOverlay & MixcutableOverlay {
    return SimpleMixcutPipeline.filters.videoOverlay(overlay)
  }

  async processSingleOverlay(overlay: VideoOverlay & MixcutableOverlay) {
    if (overlay.rotation !== 0) return overlay

    const { range = 7.5 } = this.context.params

    // 生成随机旋转角度
    const randomRotation = _.random(-range, range)
    const newRotation = overlay.rotation + randomRotation

    const { offsetX, offsetY, deltaW, deltaH } = this.calculateOffset(overlay, newRotation)

    return {
      ...overlay,
      rotation: newRotation,
      left: overlay.left + offsetX,
      top: overlay.top + offsetY,
      width: overlay.width + deltaW,
      height: overlay.height + deltaH,
    }
  }

  /**
   * Overlay 经过旋转后，会在四周产生空白区域。
   *
   * 该方法计算合适的位置偏移和大小偏移以填补上述的空白
   */
  private calculateOffset(overlay: VideoOverlay, newRotationDegree: number) {
    const a = overlay.width
    const b = overlay.height
    const alpha = Math.abs(newRotationDegree * Math.PI / 180)
    const sinAlpha = Math.sin(alpha)
    const cosAlpha = Math.cos(alpha)

    const offsetX = (sinAlpha * (b * (1 + cosAlpha) - a * sinAlpha))
      / (2 * cosAlpha * (1 + cosAlpha))

    const offsetY = (sinAlpha * (a * (1 + cosAlpha) - b * sinAlpha))
      / (2 * cosAlpha * (1 + cosAlpha))

    const deltaW = 2 * cosAlpha * offsetX
    const deltaH = 2 * cosAlpha * offsetY

    return {
      offsetX: -offsetX,
      offsetY: -offsetY,
      deltaW,
      deltaH,
    }
  }
}
