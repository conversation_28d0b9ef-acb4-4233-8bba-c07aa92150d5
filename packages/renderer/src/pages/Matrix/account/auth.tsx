import React, { useMemo, useState, useRef } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { DataTable } from '@/components/ui/data-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { SearchIcon, PlusIcon, DownloadIcon } from 'lucide-react'
import { usePagination } from '@/hooks/usePagination'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { DateRangePicker } from '@/components/date-range-picker'
import { type DateRange } from 'react-day-picker'
import { formatTimestamp } from '@/components/lib/utils'
import { AccountDetailDrawer, AccountDetailDrawerRef } from '@/modules/matrix/components/AccountDetailDrawer'
import { AuthorizationDialog, AuthorizationDialogRef } from '@/modules/matrix/components/AuthorizationDialog'
import { AssignAccountGroupDialog, AssignAccountGroupDialogRef } from '../../../modules/matrix/components/AssignAccountGroupDialog'
import useRequest from '@/hooks/useRequest'
import { WithConfirm } from '@/components/WithConfirm'
import { GenericMatrixModule } from '@/libs/request/api/generic-matrix'
import { Account, AccountSearchParams } from '@/modules/matrix/types/shared'
import { usePlatform } from '@/modules/matrix/context/context'
import { accountAuthStatusMap } from '@/modules/matrix/constants/shared'
import { AccountTokenStatus } from '@/modules/matrix/enums/shared'

// 获取授权状态信息
const getAuthStatusInfo = (account: Account) => {
  const status = account.accessTokenStatus || 4
  return accountAuthStatusMap[status as keyof typeof accountAuthStatusMap] || accountAuthStatusMap[0]
}

// 平台类型映射
const platformMap: Record<number, string> = {
  1: '抖音',
  2: '巨量引擎',
  3: '微信视频号',
  4: '小红书',
  5: '快手',
}

export default function MatrixAccountAuthPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>(String(AccountTokenStatus.ALL))
  const [dateRange, setDateRange] = useState<DateRange | undefined>()
  const { currentPlatform } = usePlatform()

  // 分组对话框ref
  const assignGroupDialogRef = useRef<AssignAccountGroupDialogRef>(null)
  // 详情抽屉ref
  const accountDetailDrawerRef = useRef<AccountDetailDrawerRef>(null)
  // 授权对话框ref
  const authorizationDialogRef = useRef<AuthorizationDialogRef>(null)

  // 处理分组按钮点击
  const handleAssignGroup = (account: Account) => {
    assignGroupDialogRef.current?.open(account)
  }

  // 处理查看按钮点击
  const handleViewDetail = (account: Account) => {
    accountDetailDrawerRef.current?.open(account)
  }

  // 处理新增授权按钮点击
  const handleNewAuthorization = () => {
    authorizationDialogRef.current?.open()
  }

  const searchParams = useMemo(() => {
    const createTime = dateRange?.from && dateRange?.to
      ? [dateRange.from.getTime(), dateRange.to.getTime()] as [number, number]
      : undefined

    return {
      search: searchTerm,
      createTime,
      accessTokenStatus: statusFilter === String(AccountTokenStatus.ALL) ? null : Number(statusFilter)
    }
  }, [ dateRange, searchTerm, statusFilter])

  const {
    data: accounts,
    pagination,
    isLoading,
    isError,
    setPagination,
    refetch,
  } = usePagination<Account, AccountSearchParams>({
    queryKey: [QUERY_KEYS.AUTH_ACCOUNT_LIST, currentPlatform],
    queryFn: params =>  GenericMatrixModule.endpoints.accountList(currentPlatform, params),
    searchParams,
    initialPageSize: 10,
    refetchInterval: 5000
  })

  const { mutate: deleteAccount } = useRequest(
    (id: number) =>  GenericMatrixModule.endpoints.deleteAccount(currentPlatform, id),
    {
      onSuccess: refetch
    })

  // 表格列定义
  const columns: ColumnDef<Account>[] = [
    {
      accessorKey: 'id',
      header: '序号',
      cell: ({ row }) => row.index + 1,
    },
    {
      accessorKey: 'nickname',
      header: '账号',
      cell: ({ row }) => {
        const account = row.original
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={account.avatar} />
              <AvatarFallback>
                {(account.nickname || account.orgNickname || 'U').charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="font-medium">{account.nickname || account.orgNickname || '-'}</span>
              {account.orgNickname && account.nickname !== account.orgNickname && (
                <span className="text-xs text-muted-foreground">{account.orgNickname}</span>
              )}
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: 'channelType',
      header: '媒体平台',
      cell: ({ row }) => {
        const channelType = row.getValue('channelType') as number
        return platformMap[channelType] || '未知平台'
      },
    },
    {
      accessorKey: 'accessTokenStatus',
      header: '授权状态',
      cell: ({ row }) => {
        const account = row.original
        const statusInfo = getAuthStatusInfo(account)
        return (
          <Badge variant={statusInfo.variant}>
            {statusInfo.label}
          </Badge>
        )
      },
    },
    {
      accessorKey: 'createTime',
      header: '授权时间',
      cell: ({ row }) => {
        const createTime = row.getValue('createTime') as number
        return formatTimestamp(createTime)
      },
    },
    {
      accessorKey: 'expiredTime',
      header: '过期时间',
      cell: ({ row }) => {
        const expiredTime = row.getValue('expiredTime') as number
        return formatTimestamp(expiredTime)
      },
    },
    {
      accessorKey: 'uniqueId',
      header: '抖音ID',
      cell: ({ row }) => {
        const item  = row.original
        return (
          <span className="text-sm text-muted-foreground">
            {item.uniqueId  || '-'}
          </span>
        )
      },
    },
    {
      accessorKey: 'remark',
      header: '备注',
      cell: ({ row }) => {
        const remark = row.getValue('remark') as string
        return remark || '-'
      },
    },

    {
      id: 'actions',
      header: () => <div className="text-right">操作</div>,
      cell: ({ row }) => {
        const account = row.original
        return (
          <div className="flex gap-3 justify-end">
            <Button variant="link" onClick={() => handleViewDetail(account)}>查看</Button>
            <Button variant="link" onClick={() => handleAssignGroup(account)}>
              分组
            </Button>

            <WithConfirm
              title="删除账号"
              description={`确定要删除"${account.nickname}"吗？此操作不可恢复。`}
              confirmText="删除"
              confirmVariant="destructive"
              onConfirm={() => deleteAccount(account.id)}            
              asChild
            >
              <Button variant="link" className="text-destructive">
                删除
              </Button>
            </WithConfirm>
           
          </div>
        )
      },
    },
  ]

  return (
    <div className="flex h-full flex-col bg-background/50 overflow-auto rounded-lg">
      {/* 顶部工具栏 */}
      <div className="flex items-center justify-between border-b  px-4 py-3">
        <div className="flex items-center space-x-4">
          {/* 搜索框 */}
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="搜索账号、平台、负责人..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="w-64 pl-9"
            />
          </div>

          {/* 平台筛选 */}
          {/* <Select value={platformFilter} onValueChange={setPlatformFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="媒体平台" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部平台</SelectItem>
              <SelectItem value="巨量引擎">巨量引擎</SelectItem>

            </SelectContent>
          </Select> */}

          {/* 状态筛选 */}
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="授权状态" />
            </SelectTrigger>
            <SelectContent>
              {
                Object.values(accountAuthStatusMap).map(item => 
                  <SelectItem key={item.key} value={String(item.key)}>{item.label}</SelectItem>)
              }
            </SelectContent>
          </Select>

          <DateRangePicker
            value={dateRange}
            onChange={setDateRange}
          />
        </div>

        {/* 右侧按钮 */}
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <DownloadIcon className="h-4 w-4 mr-2" />
            导出数据
          </Button>
          <Button size="sm" onClick={handleNewAuthorization}>
            <PlusIcon className="h-4 w-4 mr-2" />
            新增授权
          </Button>
        </div>
      </div>

      {/* 表格内容 */}
      <div className="flex-1 p-4">
        <DataTable
          columns={columns}
          data={accounts}
          pagination={pagination}
          onPaginationChange={setPagination}
          loading={isLoading}
          emptyMessage={isError ? '加载数据失败，请重试' : '暂无授权账户数据'}
        />
      </div>

      {/* 分组对话框 */}
      <AssignAccountGroupDialog
        ref={assignGroupDialogRef}

      />

      {/* 账户详情抽屉 */}
      <AccountDetailDrawer
        ref={accountDetailDrawerRef}
      />

      {/* 授权对话框 */}
      <AuthorizationDialog
        ref={authorizationDialogRef}
      />
    </div>
  )
}
