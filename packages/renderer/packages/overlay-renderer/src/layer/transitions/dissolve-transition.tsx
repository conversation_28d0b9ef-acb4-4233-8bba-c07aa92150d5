// 叠化转场组件
import React from 'react'
import { useRenderContext } from '../../render.context'
import { TransitionBaseProps } from '../../types'
import { VideoLayerContent } from '../layers'

export const DissolveTransition: React.FC<TransitionBaseProps> = ({
  fromVideo,
  toVideo,
  progress
}) => {
  const { playerMetadata: { width, height } } = useRenderContext()

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width,
    height,
    overflow: 'hidden',
  }

  const fromVideoStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,

    width: '100%',
    height: '100%',
    opacity: 1 - progress,
    transform: `rotate(${fromVideo.rotation || 0}deg)`,
    transformOrigin: 'center center',
    zIndex: 1
  }

  const toVideoStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,

    width: '100%',
    height: '100%',
    opacity: progress,
    transform: `rotate(${toVideo.rotation || 0}deg)`,
    transformOrigin: 'center center',
    zIndex: 2,
  }

  return (
    <div style={containerStyle} id={`dissolve-${fromVideo.id}-${toVideo.id}`}>
      {/* 源视频层 */}
      <div style={fromVideoStyle}>
        <VideoLayerContent overlay={fromVideo} inTransition />
        {/*<OffthreadVideo*/}
        {/*  src={fromVideo.localSrc || fromVideo.src}*/}
        {/*  style={{*/}
        {/*    width: '100%',*/}
        {/*    height: '100%',*/}
        {/*    objectFit: fromVideo.styles?.objectFit || 'cover'*/}
        {/*  }}*/}
        {/*  volume={fromVideo.styles?.volume ?? 0}*/}
        {/*  {...getProgressiveOverlayProps(fromVideo)}*/}
        {/*/>*/}
      </div>

      {/* 目标视频层 */}
      <div style={toVideoStyle}>
        <VideoLayerContent overlay={toVideo} inTransition />
        {/*<OffthreadVideo*/}
        {/*  src={toVideo.localSrc || toVideo.src}*/}
        {/*  style={{*/}
        {/*    width: '100%',*/}
        {/*    height: '100%',*/}
        {/*    objectFit: toVideo.styles?.objectFit || 'cover'*/}
        {/*  }}*/}
        {/*  volume={toVideo.styles?.volume ?? 0}*/}
        {/*  {...getProgressiveOverlayProps(toVideo)}*/}
        {/*/>*/}
      </div>
    </div>
  )
}
