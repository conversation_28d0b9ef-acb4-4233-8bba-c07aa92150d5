import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import { Outlet, useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify'
import { Loader2, Trash, VideoOff } from 'lucide-react'
import { cn } from '@/components/lib/utils'
import { HotAnalyzerModule } from '@/libs/request/api/analyzer'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { useQueryClient } from '@tanstack/react-query'
import { AnalyzeStatus } from '@/types/analyzer'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { useInfiniteQueryHotAnalyzer } from '@/hooks/queries/useQueryAnalyzer'
import { FileOrFolderUploader } from '@/modules/task-center/components/uploader'
import { UploadTask } from '@app/shared/types/local-db/entities/upload-task.entity'
import { UploadingTasks } from '@/modules/task-center/components/UploadingTasks'
import { OSSModules } from '@app/shared/types/ipc/file-uploader.js'

const HotAnalyzerList: React.FC = () => {
  const queryClient = useQueryClient()
  const navigate = useNavigate()
  const { data } = useInfiniteQueryHotAnalyzer()
  const historyTaskList = useMemo(() => data?.pages.flatMap(v => v.list) || [], [data])

  const [url, setUrl] = useState('')
  const [isLocalUrl, setIsLocalUrl] = useState(false)
  const [loading, setLoading] = useState(false)

  const handleAnalyze = useCallback(async () => {
    if (!url) return toast.error('请输入视频链接')
    setLoading(true)
    try {
      const { id } = await HotAnalyzerModule.create({ url, islocal: isLocalUrl })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.HOT_ANALYZER_DETAIL, id] })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.HOT_ANALYZER_LIST] })
      setUrl('')
      toast('上传成功，请等待解析完成', { type: 'success' })
    } catch (error: any) {
      toast.error(error?.message || '上传失败')
    } finally {
      setLoading(false)
    }
  }, [url, isLocalUrl, navigate, queryClient])

  const handleDelete = async id => {
    try {
      await HotAnalyzerModule.delete({ id })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.HOT_ANALYZER_LIST] })
      toast('删除成功', { type: 'success' })
      navigate('/home/<USER>')
    } catch (error: any) {
      toast.error(error?.message || '删除失败')
    }
  }

  const handleUpload = async (tasks: UploadTask.CompleteEvent) => {
    try {
      console.log(tasks)
      if (tasks.tasks && tasks.tasks.length > 0) {
        const firstTask = tasks.tasks[0]
        if (firstTask.status === UploadTask.Status.COMPLETED) {
          setUrl(firstTask.url)
          setIsLocalUrl(true)
        }
      }
    } catch (error: any) {
      toast.error(error?.message || '上传失败')
    }
  }

  useEffect(() => {
    if (isLocalUrl && url) {
      const analyze = async () => {
        await handleAnalyze()
        setIsLocalUrl(false)
      }
      analyze()
    }
  }, [url, isLocalUrl])

  return (
    <div className="flex flex-col h-full gap-20 w-full py-20 px-2 relative">
      {/* 解析 */}
      <div className="flex flex-col items-center w-full gap-6">
        <h4 className="text-2xl font-bold text-primary-highlight1">用AI读懂爆款脚本的赚钱逻辑</h4>
        <div className="flex gap-4 h-10 w-full">
          <div className={cn('relative mb-2 flex-1 h-full')}>
            <Input
              placeholder="请输入视频链接"
              value={url}
              className="w-full h-10"
              onChange={e => setUrl(e.target.value)}
            />
            <div className="absolute right-2 top-1/2 -translate-y-1/2">
              <FileOrFolderUploader
                variant="onlyFile"
                targetOssModule={OSSModules.ai}
                scene={UploadTask.UploadSourceScenes.HOT_ANALYZER}
                folderOptions={{
                  directoryType: 'storage',
                }}
                fileCategory={['VIDEO']}
                onComplete={(payload: UploadTask.CompleteEvent) => handleUpload(payload)}
              />
            </div>
          </div>
          <Button
            variant="default"
            className="bg-gradient-brand text-white w-20 h-full"
            onClick={handleAnalyze}
            disabled={loading}
          >
            {loading ? <Loader2 className="animate-spin size-4" /> : '解析'}
          </Button>
        </div>
      </div>
      <div className="flex flex-col gap-6">
        <h4 className="text-2xl font-bold ">历史任务</h4>

        {/* 历史任务列表 */}
        <div className="grid grid-cols-4 gap-6 overflow-auto">
          <UploadingTasks scene={UploadTask.UploadSourceScenes.HOT_ANALYZER} fullSize={true} />
          {historyTaskList.map((item, index) => (
            <Card
              key={index}
              className="border cursor-pointer shadow-lg overflow-hidden bg-transparent"
              onClick={() => {
                switch (item.status) {
                  case AnalyzeStatus.PENDING:
                  case AnalyzeStatus.STARTED:
                    toast.warning('请等待解析完成')
                    break
                  case AnalyzeStatus.FAILURE:
                    toast.error('任务解析失败')
                    break
                  case AnalyzeStatus.REVOKED:
                    toast.info('任务已取消')
                    break
                  default:
                    navigate(`/home/<USER>/analyzerDetail/${item.id}`)
                }
              }}
            >
              <CardContent className="flex flex-col relative h-52 p-0">
                {/* 解析中遮罩 */}
                {(item.status === AnalyzeStatus.PENDING || item.status === AnalyzeStatus.STARTED) && (
                  <div className="absolute inset-0 bg-black/50 bg-opacity-4 flex flex-col items-center justify-center z-10">
                    <Loader2 className="w-8 h-8 text-white animate-spin" />
                    <span className="text-white text-sm mt-2">解析中...</span>
                  </div>
                )}
                {item.status === AnalyzeStatus.FAILURE && (
                  <div className="absolute inset-0 bg-black/50 flex flex-col items-center justify-center z-10">
                    <VideoOff className="w-8 h-8 text-white" />
                    <span className="text-white text-sm mt-2">解析失败</span>
                  </div>
                )}
                {item.status === AnalyzeStatus.REVOKED && (
                  <div className="absolute inset-0 bg-black/50 flex flex-col items-center justify-center z-10">
                    <VideoOff className="w-8 h-8 text-white" />
                    <span className="text-white text-sm mt-2">任务已取消</span>
                  </div>
                )}

                {item.status === AnalyzeStatus.SUCCESS && (
                  <>
                    <div className="h-3/4">
                      <img src={item.coverUrl} alt={item.title} className="object-cover w-full h-full" />
                    </div>

                    <div className="flex-1 px-4 flex items-center">
                      <span className="truncate w-full" title={item.title}>
                        {item.title}
                      </span>
                    </div>
                  </>
                )}

                <div className="absolute top-2 right-2 w-10 h-6 bg-black/80 rounded-full flex items-center justify-center p-1 z-15">
                  <Trash
                    className="w-full h-full text-white"
                    onClick={e => {
                      e.stopPropagation()
                      handleDelete(item.id)
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          ))}
          {historyTaskList.length === 0 && (
            <div className="col-span-4 flex items-center justify-center h-40 text-muted-foreground">
              <span>暂无历史任务</span>
            </div>
          )}
        </div>
        <Outlet />
      </div>
    </div>
  )
}

export default HotAnalyzerList
