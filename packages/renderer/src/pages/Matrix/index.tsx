import React from 'react'
import { Outlet, useLocation } from 'react-router'
import { RouterTabs, type RouterTabItem } from '@/components/RouterTabs'
import { PlatformProvider } from '@/modules/matrix/context/provider'
import { PlatformKey } from '@/libs/request/api/generic-matrix'
import PlatformSelector from '@/modules/matrix/components/PlatformSelector'

const matrixTabs: RouterTabItem[] = [
  {
    value: 'summary',
    label: '数据统计',
    path: 'summary',
  },
  {
    value: 'distribution',
    label: '分发管理',
    path: 'distribution',
  },
  {
    value: 'auth',
    label: '账号授权',
    path: 'auth',
  },
  {
    value: 'list',
    label: '账号管理',
    path: 'list',
  },
  {
    value: 'draft',
    label: '草稿箱管理',
    path: 'draft',
  },
]

export const Matrix = () => {
  const location = useLocation()
  return (
    <PlatformProvider defaultPlatform={PlatformKey.dy}>
      <div className="flex flex-col w-full h-full">
        <div className="flex justify-between">
          <div className="flex w-fit ">
            <RouterTabs
              tabs={matrixTabs}
              basePath="/home/<USER>"
            />

          </div>
          {
            location.pathname.includes('/home/<USER>/distribution') ? null : <PlatformSelector />
          }

        </div>
        <div className="flex flex-col flex-1 overflow-hidden ">
          <main className="flex-1 overflow-hidden mt-3 ">
            <Outlet />
          </main>
        </div>
      </div>
    </PlatformProvider>
  )
}

