import React, { forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, useState } from 'react'
import { X } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { cn } from '@/components/lib/utils'
import { SettingsTabs, TabItem } from '@/modules/video-editor/shared'
import { TagsFromAPI } from '@/pages/HotAnalyzer/analyzerDetail/TagsFromAPI'
import { HotAnalyzerModule } from '@/libs/request/api/analyzer'
import { FileOrFolderUploader } from '@/modules/task-center/components/uploader'
import { UploadTask } from '@app/shared/types/local-db/entities/upload-task.entity'
import { OSSModules } from '@app/shared/types/ipc/file-uploader.js'

export interface SwitchTabItem extends Omit<TabItem, 'content'> {
  type: FieldType
  contentKey: string
  placeholder?: string
  content?: React.ReactNode
}

export const FIELD_TYPES = {
  TEXT: 'text', // 输入框
  TEXTAREA: 'textarea', // 多行输入框
  RADIO: 'radio', // 单选框
  CHECKBOX: 'checkbox', // 复选框
  SELECT: 'select', // 下拉框
  SWITCH: 'switch', // 开关
  FILE: 'file', // 文件
  TAGS_FROM_API: 'tags-from-api', // 标签列表（依赖某个字段生成的标签列表）
  TABS: 'tabs', // tabs
} as const

export type FieldType = (typeof FIELD_TYPES)[keyof typeof FIELD_TYPES]

export interface FieldConfig {
  name: string // 字段名
  label: string // 字段标签
  type?: FieldType // 字段类型
  required?: boolean // 是否必填
  options?: { label: string; value: any }[] // 选项列表
  showLabel?: boolean // 是否显示标签，默认 true
  dependOn?: string // 依赖哪个字段的值
  labelPosition?: 'top' | 'bottom' | 'left' | 'right' // 标签显示位置，默认 top
  switchTabs?: SwitchTabItem[] // 开关开启时显示的 Tabs
  targetField?: string// 指定目标输入框字段
}

type AiImitativeFormProps = {
  fields: FieldConfig[] // 表单字段配置
  promptCode: string // 表单code
  onValuesChange?: (values: Record<string, any>) => void // 表单值变化时的回调
  initialValues?: Record<string, any> // 表单初始值
  showRequiredMark?: boolean // 展示必填星号
}

// 渲染单个字段的函数
function renderFieldInput(field: FieldConfig, value: any, onChange: (val: any) => void) {
  switch (field.type) {
    case FIELD_TYPES.TEXT:
      return <Input value={value || ''} placeholder={`请输入${field.label}`} onChange={e => onChange(e.target.value)} />
    case FIELD_TYPES.TEXTAREA:
      return (
        <Textarea value={value || ''} onChange={e => onChange(e.target.value)} placeholder={`请输入${field.label}`} />
      )
    case FIELD_TYPES.RADIO:
      return (
        <RadioGroup value={value} onValueChange={onChange} className="flex gap-4">
          {field.options?.map(opt => (
            <label key={opt.value} className="inline-flex items-center space-x-2 cursor-pointer">
              <RadioGroupItem
                value={opt.value}
                className="w-4 h-4 rounded-full border border-gray-400 data-[state=checked]:border-current"
              />
              <span>{opt.label}</span>
            </label>
          ))}
        </RadioGroup>
      )
    case FIELD_TYPES.CHECKBOX:
      return (
        <div className="flex gap-4">
          {field.options?.map(opt => (
            <label key={opt.value} className="inline-flex items-center space-x-2">
              <Checkbox
                checked={Array.isArray(value) && value.includes(opt.value)}
                onCheckedChange={checked => {
                  const arr = Array.isArray(value) ? [...value] : []
                  if (checked) arr.push(opt.value)
                  else arr.splice(arr.indexOf(opt.value), 1)
                  onChange(arr)
                }}
              />
              <span>{opt.label}</span>
            </label>
          ))}
        </div>
      )
    case FIELD_TYPES.SELECT:
      return (
        <Select value={value || ''} onValueChange={onChange}>
          <SelectTrigger>
            <SelectValue placeholder={`请选择${field.label}`} />
          </SelectTrigger>
          <SelectContent>
            {field.options?.map(opt => (
              <SelectItem key={opt.value} value={opt.value}>
                {opt.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )
    case FIELD_TYPES.SWITCH:
      return <Switch checked={!!value} onCheckedChange={onChange} />
    case FIELD_TYPES.FILE: {
      const [fileInfo, setFileInfo] = useState<UploadTask.IUploadTask | null>(null)
      const [previewVisible, setPreviewVisible] = useState(false)

      return (
        <div>
          <div className="relative flex items-center justify-center h-8 border rounded px-2">
            {!value && (
              <div className="flex">
                <FileOrFolderUploader
                  variant="onlyFile"
                  scene={UploadTask.UploadSourceScenes.HOT_ANALYZER}
                  targetOssModule={OSSModules.ai}
                  folderOptions={{
                    directoryType: 'storage',
                  }}
                  fileCategory={['VIDEO']}
                  btnText="上传参考序列"
                  onComplete={(payload: UploadTask.CompleteEvent) => {
                    try {
                      if (payload.tasks && payload.tasks.length > 0) {
                        const firstTask = payload.tasks[0]
                        setFileInfo(payload.tasks[0]) // 保存文件信息
                        if (firstTask.status === UploadTask.Status.COMPLETED) {
                          // 更新表单字段值（比如存 URL）
                          onChange(firstTask.url)
                        }
                      }
                    } catch (error) {
                      console.error('文件上传失败', error)
                    }
                  }}
                />
              </div>
            )}
            {value && (
              <div className="flex-1 flex items-center justify-between gap-2">
                <span className="truncate max-w-xs">{fileInfo?.name}</span>
                <div className="flex items-center">
                  <Button variant="link" size="sm" onClick={() => setPreviewVisible(true)}>
                    预览
                  </Button>
                  <X className="w-4 h-4" onClick={() => onChange('')}>
                    清除
                  </X>
                </div>
              </div>
            )}
          </div>
          {/* 预览 */}
          {previewVisible && value && (
            <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
              <div className="bg-white p-4 rounded shadow-lg relative">
                <button
                  className="absolute top-2 right-2 px-2 py-1 bg-red-500 text-white rounded text-sm"
                  onClick={e => {
                    e.stopPropagation()
                    setPreviewVisible(false)
                  }}
                >
                  关闭
                </button>
                <div className="mt-2 text-right ">
                  <video src={value} controls className="w-[600px] h-auto" />
                </div>
              </div>
            </div>
          )}
        </div>
      )
    }
    default:
      return null
  }
}

export const AiImitativeForm = forwardRef<AiImitativeFormRef, AiImitativeFormProps>(
  ({ fields, promptCode, onValuesChange, initialValues = {}, showRequiredMark = true }, ref) => {
    const [values, setValues] = useState<Record<string, any>>(initialValues)
    const [errors, setErrors] = useState<Record<string, string>>({})
    const [activeTabMap, setActiveTabMap] = useState<Record<string, string>>({})

    const handleChange = (name: string, value: any) => {
      setValues(prev => ({ ...prev, [name]: value }))

      const field = fields.find(f => f.name === name)
      if (field?.required) {
        setErrors(prev => ({
          ...prev,
          [name]: value ? '' : `${field.label} 为必填项`,
        }))
      }

      onValuesChange?.({ ...values, [name]: value })
    }

    useImperativeHandle(ref, () => ({
      getValues: () => values,
      getErrors: () => errors,
      validate: () => {
        const newErrors: Record<string, string> = {}
        fields.forEach(field => {
          if (field.required && !values[field.name]) {
            newErrors[field.name] = `${field.label} 为必填项`
          }
        })
        const finalValues: Record<string, any> = { ...values }
        // 处理 DNA 字段
        fields.forEach(field => {
          if (field.type === FIELD_TYPES.TABS && field.switchTabs) {
            const activeValue = activeTabMap[field.name] || field.switchTabs[0].value
            const activeTab = field.switchTabs.find(tab => tab.value === activeValue)
            if (activeTab) {
              finalValues.url = values[activeTab.contentKey] || ''
              finalValues.isLocal = activeTab.value === 'file'
            }
            delete finalValues[field.name] // 删除临时字段
          }
        })

        delete finalValues.dnaSwitch

        setErrors(newErrors)
        return {
          values: {
            promptCode,
            finalValues,
          },
          errors: newErrors,
          isValid: Object.keys(newErrors).length === 0,
        }
      },
    }))

    return (
      <div className="space-y-4 text-sm">
        {fields.map((field, index) => {
          const position = field.labelPosition || 'top' // 默认 top
          const isHorizontal = position === 'left' || position === 'right'

          // 容器
          const containerClass = isHorizontal
            ? cn('flex items-center gap-4', position === 'right' && 'flex-row-reverse')
            : 'flex flex-col space-y-2'

          // 标签
          const labelClass = cn(
            'font-medium',
            isHorizontal && 'w-32',
            position === 'bottom' && 'order-2',
            position === 'right' && 'text-right',
          )

          // 输入控件 wrapper
          const inputWrapperClass = cn(
            isHorizontal ? 'flex-1 flex' : 'flex flex-col',
            position === 'left' && 'justify-end',
            position === 'right' && 'justify-start',
            position === 'bottom' && 'order-1',
            position === 'top' && 'order-2',
          )
          return (
            <div>
              <div key={`${field.name}-${index}`} className={containerClass}>
                {(field.showLabel ?? true) && (
                  <label className={labelClass}>
                    {field.label}
                    {field.required && showRequiredMark && <span className="text-red-500 ml-1">*</span>}
                  </label>
                )}
                <div className={cn(inputWrapperClass)}>
                  {/* 普通字段 */}
                  {field.type !== FIELD_TYPES.TABS &&
                    renderFieldInput(field, values[field.name], val => handleChange(field.name, val))}

                  {/* TAGS_FROM_API 仅显示标签，不绑定值 */}
                  {field.type === FIELD_TYPES.TAGS_FROM_API && field.dependOn && (
                    <TagsFromAPI
                      dependOnValue={values[field.dependOn]}
                      fetchTags={async (keyword: string) => {
                        if (!keyword) return []
                        try {
                          const res = await HotAnalyzerModule.tags({ promptCode: '产品特色标签', content: keyword })
                          const tagArray =
                            typeof res.content === 'string' ? res.content.split('|').map(t => t.trim()) : []
                          return tagArray || []
                          // return [
                          //   '轻量化',
                          //   '高颜值设计',
                          //   '环保材质',
                          //   '便携收纳',
                          //   '快速加热 \n智能触控',
                          //   '大容量续航',
                          //   'NFC互联',
                          //   '防尘防水',
                          //   '静音运行',
                          // ]
                        } catch (err) {
                          console.error('获取标签失败', err)
                          return []
                        }
                      }}
                      onTagClick={tag => {
                        if (!field.targetField) return
                        const currentVal = values[field.targetField] || ''
                        handleChange(field.targetField, currentVal ? `${currentVal},${tag}` : tag) // 点击添加到输入框
                      }}
                    />
                  )}

                  {/* tabs */}
                  {field.type === FIELD_TYPES.TABS && field.switchTabs && (
                    <SettingsTabs
                      tabs={field.switchTabs.map(tab => ({
                        ...tab,
                        content:
                          tab.content ||
                          renderFieldInput(
                            { ...tab, type: tab.type, name: tab.contentKey },
                            values[tab.contentKey],
                            val => handleChange(tab.contentKey, val),
                          ),
                      }))}
                      onTabChange={val => setActiveTabMap(prev => ({ ...prev, [field.name]: val }))}
                    />
                  )}
                </div>
              </div>
              {errors[field.name] && <p className="text-red-500 text-sm mt-1">{errors[field.name]}</p>}
            </div>
          )
        })}
      </div>
    )
  },
)
export type AiImitativeFormRef = {
  /** 获取当前表单值 */
  getValues: () => Record<string, any>

  /** 获取当前表单错误 */
  getErrors: () => Record<string, string>

  /** 校验必填字段并返回结果 */
  validate: () => { values: Record<string, any>; errors: Record<string, string>; isValid: boolean }
}
