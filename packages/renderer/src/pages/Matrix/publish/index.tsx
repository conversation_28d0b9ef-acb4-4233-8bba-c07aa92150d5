import React, { useEffect, useMemo, useRef, } from 'react'
import { Controller, FormProvider, SubmitHandler, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useNavigate, useSearchParams } from 'react-router'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@radix-ui/react-separator'
import useRequest from '@/hooks/useRequest'
import { QUERY_KEYS } from '@/constants/queryKeys'
import {  VideoSelectionSection } from '@/modules/matrix/components/publish/shared/VideoSelectionSection'

import { queryClient } from '@/main'
import { toast } from 'react-toastify'
import { LoaderCircle } from 'lucide-react'
import './style.css'
import { PublishData, PublishPayload, publishSchema } from '@/modules/matrix/schemas/publish.schema'
import DyForm from './forms/DyForm'
import {  PublishChannelSelector } from '@/modules/matrix/components/PublishChannelSelector'
import { GenericMatrixModule, PlatformKey } from '@/libs/request/api/generic-matrix'
import { PublishMode, PublishSetting, PublishTimeType, VideoMountMode } from '@/modules/matrix/enums/shared'
import XhsForm from './forms/XhsForm'
import { CommercialType } from '@/modules/matrix/enums/xhs.enums'
import { DraftInfo } from '@/modules/matrix/types/shared/draft'
import { PreviewPublishDialog, PreviewPublishDialogRef } from '@/modules/matrix/components/publish/shared/PreviewPublishDialog'
import { recoverFormFromDraft } from '@/modules/matrix/utils/draft'
import { buildPublishRequest, transformPublishDataToSubmitMap } from '@/modules/matrix/utils/shared'
import KsForm from './forms/KsForm'

export default function MatrixDistributionForm() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()

  const { draftId } = useMemo(() => ({
    planId: searchParams.get('planId'),
    platform: searchParams.get('platform') as PlatformKey,
    draftId: searchParams.get('draftId')
  }), [searchParams])

  const previewDialogRef = useRef<PreviewPublishDialogRef>(null)

  const methods = useForm<PublishData>({
    resolver: zodResolver(publishSchema),
    defaultValues: {
      channels: {
        selected: [PlatformKey.dy],
        active: PlatformKey.dy,
      },
      common: {
        name: '',
        videoList: [],
      },
      platforms: {
        dy: {
          titles: [{ title: '' }],
          accountList: [],
          publishMode: PublishMode.CHECK_IN,
          mountType: VideoMountMode.NONE,
          setting: PublishSetting.ONE_ACCOUNT_ONE_VIDEO,
          timeType: PublishTimeType.IMMEDIATE,
          timeSetting: {
            publishTime: Date.now(),
            period: 0,
            periodType: [],
            loopDays: [Date.now()],
            loopTime: '08:00',
            numEachDay: 1
          }
        },
        xhs: {
          titles: [{ title: '', desc: '' }],
          commercialType: CommercialType.RANDOM_PRODUCT_TO_ONE_VIDEO,
          maxProduct: 18,
          setting: PublishSetting.ONE_ACCOUNT_ONE_VIDEO,
          timeType: PublishTimeType.IMMEDIATE,
          accountList: [{ id: 1, avatar: 'momo', nickname: 'momo' }]
        },
      }
    }
  })

  const  {
    register,
    control,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
    getValues
  } = methods

  const nameValue = watch('common.name') || ''
  const channels = watch('channels')

  const { data: draftInfo, isLoading: isFormDataLoading } = useQuery({
    queryKey: [QUERY_KEYS.MATRIX_DRAFT_INFO, draftId],
    queryFn: async () => {
      if (draftId) {
        return GenericMatrixModule.common.draftDetail(draftId)
      }
    },
    staleTime: 0,
    enabled: !!draftId,
  })

  const { mutate: handleSaveToDraft, isPending: isSaving } = useRequest(
    () => {
      const data = getValues()

      const draftData: Omit<DraftInfo, 'id'> = {
        name: data.common.name,
        videoList: data.common.videoList,
        ...transformPublishDataToSubmitMap(data, { draftSuffix: true }),
      }

      console.log({ draftData })

      const submitData = draftId
        ? { ...draftData, id: parseInt(draftId) }
        : draftData

      return GenericMatrixModule.common.saveDraft(submitData)
    }
    ,
    {
      actionName: draftId ? '更新草稿' : '保存到草稿箱',
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATRIX_DRAFT_LIST] })
      }
    }
  )

  const submitPublishMutation = useRequest(
    (data: PublishPayload) => buildPublishRequest(data),
    {
      actionName: '发布任务',
      onSuccess: resultMap => {
        reset()
        // FIXME: 跳转跳哪儿？
        const firstResult = Object.values(resultMap)[0]
        navigate(`/home/<USER>/planDetail?ids=${encodeURIComponent(JSON.stringify([firstResult]))}`)
      },
  
    })

  const onSubmit: SubmitHandler<PublishData> = async data => {
    const submitData = {
      name: data.common.name,
      videoList: data.common.videoList,
      ...transformPublishDataToSubmitMap(data),
    }
   
    submitPublishMutation.mutate(submitData)
  }

  const findFirstErrorMessage = (errorObj: any): string | null => {
    if (!errorObj) return null

    if (errorObj.message) return errorObj.message

    for (const key of Object.keys(errorObj)) {
      const child = errorObj[key]
      const found = findFirstErrorMessage(child)
      if (found) return found
    }

    return null
  }

  const onInvalid = (errors: any) => {
    console.log({ errors })
    const firstErrorMessage = findFirstErrorMessage(errors)

    toast(`表单校验失败: ${firstErrorMessage ?? ''}`, {
      type: 'warning',
    })
  }

  useEffect(() => {
    if (!draftInfo) return
    const form = recoverFormFromDraft(draftInfo)
    reset(form)
  }, [draftInfo])

  // useEffect(() => {
  //   if (planId && platform) {
  //     recoverFormFromTask(platform, planId).then(form => {
  //       reset(form)
  //     })
  //   }
  // }, [planId, platform])

  if (isFormDataLoading) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <LoaderCircle className="animate-spin" />
      </div>
    )
  }

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit, onInvalid)}   className="flex h-full flex-col">

        {/* 滚动内容 */}
        <div className="flex-1 overflow-auto bg-background/50 rounded-lg p-6 space-y-6">
          {/* 表单字段 */}
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <h2 className="text-lg font-semibold text-gradient-brand">基本信息</h2>
            </div>

            <div className="space-y-6">
              {/* 计划名称 */}
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center gap-2">
                  计划名称 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  showLength
                  maxLength={30}
                  placeholder="请输入计划名称"
                  value={nameValue}
                  {...register('common.name')}
                  className={errors.common?.name ? 'border-red-500' : ''}
                />
                {errors.common?.name && (
                  <p className="text-sm text-red-500">{errors.common?.name.message}</p>
                )}
              </div>

              {/* 视频选择 */}
              <Controller
                name="common.videoList"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <VideoSelectionSection
                    value={value || []}
                    onChange={onChange}
                  />
                )}
              />

            </div>
          </div>

          <Separator />

          <Controller
            control={control}
            name="channels"
            render={({ field: { value, onChange } }) => (
              <PublishChannelSelector
                value={value}
                onChange={onChange}
              />
            )}
          />

          { channels.active === PlatformKey.dy &&  <DyForm /> }
          { channels.active === PlatformKey.xhs &&  <XhsForm /> }
          { channels.active === PlatformKey.ks &&  <KsForm /> }

          <PreviewPublishDialog ref={previewDialogRef} />
        </div>

        {/* 提交按钮 */}
        <div className=" p-4 flex justify-end gap-4">
          <Button
            type="button"
            onClick={() => handleSaveToDraft() }
            loading={isSaving}
          >
            {draftId ? '更新草稿' : '保存草稿箱'}
          </Button>

          <Button
            type="submit"
            onClick={handleSubmit( () => previewDialogRef.current?.open(), onInvalid)}
          >
            预览
          </Button>

          <Button
            type="submit"
            loading={submitPublishMutation.isPending}
            className="min-w-[120px]"
          >
            {submitPublishMutation.isPending ? '发布中...' : '立即发布'}
          </Button>
        </div>

      </form>
    </FormProvider>

  )
}
