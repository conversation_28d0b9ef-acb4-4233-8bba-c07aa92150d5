import { <PERSON><PERSON>ey } from '@/libs/request/api/generic-matrix'
import React, { ReactNode, useState } from 'react'
import { PlatformContext } from './context'
import { PLATFORM_CONFIG } from '../constants/shared'

interface PlatformProviderProps {
  children: ReactNode
  defaultPlatform?: PlatformKey
}

export const PlatformProvider: React.FC<PlatformProviderProps> = ({
  children,
  defaultPlatform = PlatformKey.dy
}) => {
  const [currentPlatform, setCurrentPlatform] = useState<PlatformKey>(defaultPlatform)

  const setPlatform = (platform: PlatformKey) => {
    setCurrentPlatform(platform)
  }
 
  const platformConfig = PLATFORM_CONFIG[currentPlatform]
 
  return (
    <PlatformContext.Provider 
      value={{
        currentPlatform,
        platformConfig,
        setPlatform,
      }}
    >
      {children}
    </PlatformContext.Provider>
  )
}