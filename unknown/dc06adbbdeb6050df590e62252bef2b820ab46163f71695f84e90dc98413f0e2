import React from 'react'
import { useMixcutContext } from '@/modules/mixcut/context/context'
import { formItemsByCategory, MixcutRulesFormCategories } from '@/modules/mixcut/context/useMixcutRulesForm'
import { LabeledCheckbox } from '@/components/ui/checkbox'
import { z, ZodObject } from 'zod'
import { MixcutPipelineSchemas } from '@app/shared/types/mixcut'

type GetPipelines<TCategory extends MixcutRulesFormCategories> = Pick<
  typeof MixcutPipelineSchemas.schemaByPipeline,
  (typeof formItemsByCategory)[TCategory][number]
>

type GetPipelineConfig<
  TCategory extends MixcutRulesFormCategories,
  TPipeline extends keyof GetPipelines<TCategory>
> = GetPipelines<TCategory>[TPipeline]

interface MixcutRuleCheckboxProps<
  TCategory extends MixcutRulesFormCategories,
  TPipelineKey extends keyof GetPipelines<TCategory>
> {
  label: string
  category: TCategory
  pipeline: TPipelineKey,

  children?: (context: {
    category: TCategory
    pipeline: TPipelineKey,

    getConfigValue<
      Key extends (
        GetPipelineConfig<TCategory, TPipelineKey> extends ZodObject<any>
          ? keyof GetPipelineConfig<TCategory, TPipelineKey>['shape']
          : never
      ),
      Value extends (
        GetPipelineConfig<TCategory, TPipelineKey> extends ZodObject<any>
          ? z.infer<GetPipelineConfig<TCategory, TPipelineKey>>[Key]
          : never
      )
    >(key: Key): Value

    setConfigValue<
      Key extends (
        GetPipelineConfig<TCategory, TPipelineKey> extends ZodObject<any>
          ? keyof GetPipelineConfig<TCategory, TPipelineKey>['shape']
          : never
      ),
      Value extends (
        GetPipelineConfig<TCategory, TPipelineKey> extends ZodObject<any>
          ? z.infer<GetPipelineConfig<TCategory, TPipelineKey>>[Key]
          : never
      )
    >(key: Key, value: Value): void
  }) => React.ReactNode
}

export function MixcutRuleSwitch<
  TCategory extends MixcutRulesFormCategories,
  TPipelineKey extends keyof GetPipelines<TCategory>
>({
  label, pipeline: _pipeline, category, children
}: MixcutRuleCheckboxProps<TCategory, TPipelineKey>) {
  const pipeline = _pipeline as unknown as string

  const { generation: { rulesForm: { watch, setValue } } } = useMixcutContext()

  const formKey = `${category}.${pipeline}.enabled` as any
  const formValue = watch(formKey)
  const checkBoxId = `${category}-${pipeline}-checkbox`

  const checked = formValue || false

  return (
    <>
      <div className="flex space-x-2">
        <LabeledCheckbox
          id={checkBoxId}
          checked={checked}
          onChange={(checked: boolean) => {
            setValue(formKey, checked)
          }}
          label={label}
        />
      </div>

      {checked && children && typeof children === 'function' && (
        <div className="pl-6 mb-8">
          {children({
            pipeline: _pipeline,
            category,
            getConfigValue(key) {
              return watch(`${category}.${pipeline}.${key as unknown as string}` as any)
            },
            setConfigValue(key, value) {
              setValue(`${category}.${pipeline}.${key as unknown as string}` as any, value)
            }
          })}
        </div>
      )}
    </>
  )
}
