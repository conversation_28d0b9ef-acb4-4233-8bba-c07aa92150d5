import { Module } from '@nestjs/common'
import { FileUploaderModule } from './modules/file-uploader/file-uploader.module.js'
import { WindowManagerModule } from './modules/window-manager/window-manager.module.js'
import { ResourceModule } from './modules/resource/resource.module.js'
import { EditorModule } from './modules/editor/editor.module.js'
import { MixcutModule } from './modules/mixcut/mixcut.module.js'
import { CrudableModule } from './modules/crudable/crudable.module.js'
import { GlobalModule } from '@/modules/global/global.module.js'
import { FileDownloaderModule } from './modules/file-downloader/file-downloader.module.js'

@Module({
  imports: [
    FileUploaderModule,
    FileDownloaderModule,
    WindowManagerModule,
    ResourceModule,
    EditorModule,
    MixcutModule,
    CrudableModule,
    GlobalModule,
  ]
})
export class RootModule {
}
