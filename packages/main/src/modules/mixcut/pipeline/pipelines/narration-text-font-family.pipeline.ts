import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut.js'
import { MixcutPipelineContext, SimpleMixcutPipeline } from '../mixcut-pipeline.types.js'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { TextOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'

export class NarrationTextFontFamilyPipeline extends SimpleMixcutPipeline<typeof MIXCUT_PIPELINES.narrationText.fontFamily, TextOverlay> {

  private readonly fontSrc?: string
  private readonly fontFamily?: string

  constructor(
    context: MixcutPipelineContext<typeof MIXCUT_PIPELINES.narrationText.fontFamily>,
  ) {
    super(context)
    const { fontSrc, fontFamily } = _.sample(context.params.options) || {}
    this.fontSrc = fontSrc
    this.fontFamily = fontFamily
  }

  async processSingleOverlay(overlay: TextOverlay & MixcutableOverlay) {
    if (!this.fontSrc || !this.fontFamily) return overlay

    return {
      ...overlay,
      src: this.fontSrc,
      styles: {
        ...overlay.styles,
        fontFamily: this.fontFamily
      }
    }
  }

  filter(overlay: MixcutableOverlay): overlay is TextOverlay & MixcutableOverlay {
    return SimpleMixcutPipeline.filters.narrationText(overlay)
  }
}
