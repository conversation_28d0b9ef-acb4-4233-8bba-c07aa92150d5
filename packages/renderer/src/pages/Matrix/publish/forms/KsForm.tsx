import { useForm<PERSON>ontex<PERSON>, Controller } from 'react-hook-form'
import React from 'react'
import { ScheduledPublishSection } from '@/modules/matrix/components/publish/shared/ScheduledPublishSection'
import { RadioGroupField } from '@/modules/matrix/components/publish/shared/RadioGroupField'
import { LoopPublishSection } from '@/modules/matrix/components/publish/shared/LoopPublishSection'
import { PublishData } from '@/modules/matrix/schemas/publish.schema'
import { PublishTimeType } from '@/modules/matrix/enums/shared'
import { AccountProduct } from '@/modules/matrix/schemas/shared.schema'
import { timeTypeOptions } from '@/modules/matrix/constants/shared'
import { AccountSelector } from '@/modules/matrix/components/publish/shared/AccountSelector'
import { PlatformKey } from '@/libs/request/api/generic-matrix'
import { LoopTimeTypeTooltip } from '@/modules/matrix/components/publish/shared/LoopTimeTypeTooltip'
import { TitleManage } from '@/modules/matrix/components/publish/shared/TitleArrayField'

export default function KsForm() {
  const { control, formState: { errors }, watch, setValue } = useFormContext<PublishData>()

  const accountProducts =  watch('platforms').ks?.accountProducts || []

  const timeType = watch('platforms').ks?.timeType

  return (
    <div className="space-y-6">

      {/* 动态标题 */}
      <TitleManage
        platform={PlatformKey.ks}
        control={control}
        namePrefix="platforms.ks.titles"
        errors={errors}
      />

      {/* 账号 */}
      <Controller
        control={control}
        name="platforms.ks.accountList"
        render={({ field: { value, onChange } }) => (
          <AccountSelector
            platform={PlatformKey.ks}
            value={value || []}
            onChange={val => {
              const updatedAccountProducts: AccountProduct[]  = val.map(v => {
                const existing = accountProducts.find(ap => ap.accountInfo.id === v.id)
                return existing || {
                  accountInfo: v,
                  products: [{ title: '', url: '' }] 
                }
              })

              setValue<any>('platforms.ks.accountProducts', updatedAccountProducts)

              onChange(val)
            }}
            errors={errors.platforms?.ks?.accountList?.message }
          />
        )}
      />

      {/* 发布时间类型 */}
      <Controller
        control={control}
        name="platforms.ks.timeType"
        render={({ field: { value, onChange } }) => (
          <RadioGroupField
            label="发布时间类型"
            options={timeTypeOptions}
            value={value}
            onChange={val => onChange(Number(val))}
            error={errors.platforms?.ks?.timeType?.message}
            tailContent={val => val === PublishTimeType.LOOP && <LoopTimeTypeTooltip />}
          />
        )}
      />
      {/* 定时发布配置 - 当发布时间类型为定时时显示 */}
      {timeType === PublishTimeType.SCHEDULED && (
        <ScheduledPublishSection
          control={control}
          namePrefix="platforms.ks.timeSetting"
        />
      )}
      
      {timeType === PublishTimeType.LOOP && (
        <LoopPublishSection
          control={control}
          namePrefix="platforms.ks.timeSetting"
        />
      )}
    </div>
  )
}
