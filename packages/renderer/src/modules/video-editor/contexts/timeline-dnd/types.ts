import { Overlay } from '@app/shared/types/overlay'
import { GhostElement } from '@/modules/video-editor/types'
import { MaterialResource, PasterResource, ResourceSource, SoundResource, StyledTextResource } from '@/types/resources'
import { ResourceCacheType } from '@app/shared/types/resource-cache.types'
import { ResourcePlugins } from '@/modules/video-editor/resource-plugin-system'

export class OverlaysAdjustment extends Map<
  /**
   * Overlay ID
   */
  number,
  {
    /**
     * 起始帧的变化量
     */
    fromFrameShift?: number
    /**
     * 持续时长的变化量
     */
    durationShift?: number
    /**
     * 目标分镜序号
     */
    targetStoryboardIndex?: number
  }
> {

  public apply(another: OverlaysAdjustment) {
    another.forEach((value, key) => {
      this.set(key, value)
    })
  }
}

export interface DraggableState {
  /**
   * 是否允许拖拽 (无论为何种 `DragAction`)
   */
  draggable: boolean

  /**
   * 拖拽导致的其他 Overlay 的变动
   */
  overlaysAdjust?: OverlaysAdjustment

  /**
   * 拖拽过后, 目标 Overlay 的起始帧
   */
  adjustedStartFrame?: number

  /**
   * 拖拽过后, 目标 Overlay 的总时长
   */
  adjustedDuration?: number

  /**
   * 拖拽过后, 目标 Overlay 所处的轨道序号
   */
  adjustedRow?: number

  /**
   * 拖拽过后, 目标 Overlay 所处的分镜序号
   */
  targetStoryboardIndex?: number

  updatedOverlay?: Overlay
}

type DragAction = 'move' | 'resize-end'

export interface OverlayDragInfo {
  overlay: Overlay

  initialFrom: number
  initialDurationInFrames: number
  initialRow: number

  action?: DragAction

  landingPoint?: GhostElement
  draggableState?: DraggableState

  currentFrom?: number
  currentDuration?: number
  currentRow?: number
}

export type DroppableResource =
  | {
    sourcePlugin: ResourcePlugins.MATERIAL_LIB
    data: MaterialResource.Media & { materialType: ResourceSource }
  }
  | {
    sourcePlugin: ResourcePlugins.STICKER,
    data: (
      | PasterResource.Paster
      | (PasterResource.SharedPaster & { materialType: ResourceSource })
    )
  }
  | {
    sourcePlugin: ResourcePlugins.SOUND_EFFECT,
    data: (
      | SoundResource.Sound
      | (SoundResource.SharedSound & { materialType: ResourceSource })
    )
  }
  | {
    sourcePlugin: ResourcePlugins.TEXT,
    data?: StyledTextResource.StyledText
  }
  | {
    sourcePlugin: ResourcePlugins.TRANSITION,
    data: { id: string, name: string, type: string }
  }

export type ResourceMeta = {
  type: ResourceCacheType,
  url?: string,
  filename?: string,
  customExt?: string
}
