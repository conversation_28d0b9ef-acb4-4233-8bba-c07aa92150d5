import React, { FC, ReactNode } from 'react'
import { Overlay, OverlayType } from '@app/shared/types/overlay'
import { TimelineItemLabel } from './timeline-item-label'
import clsx from 'clsx'

export interface TimelineItemContainerProps {
  /** 要显示的覆盖层对象 */
  item: Overlay
  /** 容器宽度（像素） */
  width: number
  /** 容器高度（像素，可选） */
  height?: number
  /** 是否显示标签 */
  showLabel?: boolean
  /** 子元素内容 */
  children?: ReactNode
  /** 额外的 CSS 类名 */
  className?: string
}

/**
 * 根据覆盖层类型获取对应的样式类名
 * @param type 覆盖层类型
 * @returns 样式类名字符串
 */
function getItemClasses(type: OverlayType): string {
  switch (type) {
    case OverlayType.STORYBOARD:
      return 'bg-primary-accent'
    case OverlayType.TRANSITION:
      return 'bg-primary-base/80'
    case OverlayType.TEXT:
      return 'bg-[#9E53E6] hover:bg-[#9E53E6] border-[#9E53E6] text-[#9E53E6]'
    case OverlayType.VIDEO:
      return 'bg-pink-400 border-slate-900 dark:border-white text-white dark:text-black'
    case OverlayType.SOUND:
      return 'bg-[#E49723] hover:bg-[#E49723] border-[#E49723] text-[#E49723]'
    case OverlayType.CAPTION:
      return 'bg-blue-500/20 hover:bg-blue-500/30 border-blue-500 text-blue-700'
    case OverlayType.STICKER:
      return 'bg-red-500 hover:bg-red-500 dark:bg-red-500 dark:hover:bg-red-500 border-red-500 dark:border-red-500 text-red-500 dark:text-white'
    default:
      return 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-600 dark:hover:bg-gray-500 border-gray-300 dark:border-gray-400 text-gray-950 dark:text-white'
  }
}

/**
 * 时间轴项目容器组件
 * 负责渲染时间轴项目的外观样式，包括背景、边框、标签等
 * 使用相对定位，适合在各种布局中复用
 */
export const StyleOnlyTimelineItem: FC<TimelineItemContainerProps> = ({
  item,
  width,
  height,
  children,
  className,
}) => {
  return (
    <div
      className={clsx(
        'relative rounded-md cursor-pointer group overflow-hidden',
        getItemClasses(item.type),
        className
      )}
      style={{
        width,
        height,
        transition: 'opacity 0.2s',
      }}
    >
      {/* 标签显示 */}
      <div className="**:z-1">
        <TimelineItemLabel item={item} />
      </div>

      {/* 子元素内容 */}
      <div className="z-10 absolute h-full w-full">
        {children}
      </div>
    </div>
  )
}
